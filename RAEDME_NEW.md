
# RAG Demo

基于 RAG (Retrieval-Augmented Generation) 的智能问答系统。

## 功能特性

- 文档管理
  - 支持上传 PDF、Word、PPT 等多种格式文档
  - 自动文档解析与向量化存储
  - 文档权限管理
  
- 对话系统
  - 支持多轮对话
  - 基于文档的智能问答
  - 对话历史管理
  
- 短语管理
  - 支持添加、删除和查询常用短语
  - 防止重复添加相同内容

## 技术栈

- 后端框架：Tornado
- 数据库：SQLAlchemy (异步)
- 向量检索：
  - Elasticsearch：文本检索
  - HNSW：向量近邻搜索
- 文档处理：
  - PyMuPDF：PDF 处理
  - Magic PDF：文档解析
  - Office 文档转换
- chatbi：
  - dbgpt：language2sql、工作流、元数据存储与检索、bi

## API 接口

### 对话相关
- `POST /api/v1/chat/completion`：聊天补全
- `POST /api/v1/chat/completion/stream`：流式聊天补全
- `POST /api/v1/dialog`：创建对话
- `GET /api/v1/dialog`：获取对话详情
- `GET /api/v1/dialogs`：获取对话列表

### 文档相关
- `POST /api/v1/docs/upload`：上传文档
- `GET /api/v1/docs`：获取文档列表

### 短语相关
- `POST /api/v1/phrase`：添加短语
- `DELETE /api/v1/phrase`：删除短语
- `GET /api/v1/phrases`：获取短语列表

## 快速开始

1. 安装依赖
```bash
pip install -r requirements.txt
```
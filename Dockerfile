FROM nvcr.io/nvidia/cuda:11.8.0-base-ubuntu20.04

# 设置工作目录
WORKDIR /rag_server


# 设置为非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装必要的工具和库
RUN apt update && apt install -y \
    build-essential \
    wget \
    libssl-dev \
    zlib1g-dev \
    libncurses5-dev \
    libgdbm-dev \
    libnss3-dev \
    libreadline-dev \
    libffi-dev \
    zlib1g-dev  \
    libbz2-dev  \
    liblzma-dev \
    nginx  \
    curl \
    vim \
    nodejs \
    npm \
    libcairo2  \
    libcups2 \
    && rm -rf /var/lib/apt/lists/*

# 安装 tzdata 并设置时区
RUN apt install -y tzdata && \
ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
dpkg-reconfigure --frontend noninteractive tzdata

# 安装 libreoffice，和中文字体， magic-pdf 会依赖
RUN apt update && apt install -y \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \ 
    fonts-noto-cjk \
    && rm -rf /var/lib/apt/lists/* \ 
    && fc-cache -fv

RUN wget "https://mirrors.cloud.tencent.com/libreoffice/libreoffice/stable/25.2.2/deb/x86_64/LibreOffice_25.2.2_Linux_x86-64_deb.tar.gz" \
    && tar -xzf LibreOffice_25.2.2_Linux_x86-64_deb.tar.gz \
    && dpkg -i ./LibreOffice_25.2.2.2_Linux_x86-64_deb/DEBS/*.deb  \
    && rm -rf LibreOffice_25.2.2_Linux_x86-64_deb.tar.gz LibreOffice_X.Y.Z_Linux_x86_deb

# 下载并安装 Python 3.12
RUN wget https://www.python.org/ftp/python/3.10.16/Python-3.10.16.tgz \
    && tar -xzf Python-3.10.16.tgz \
    && cd Python-3.10.16 \
    && ./configure --enable-optimizations \
    && make -j$(nproc) \
    && make install \
    && cd .. \
    && rm -rf Python-3.10.16.tgz Python-3.10.16


# 复制项目文件
COPY requirements.txt .
COPY app ./app
COPY web ./web
RUN  rm -rf ./app/conf/*
COPY docker/start.sh ./start.sh
COPY resource ./resource

# 构建前端
# RUN cd web && npm install && npm run build && cd ..

# 安装依赖
RUN pip3 install "magic-pdf[full]"==1.2.0 --extra-index-url https://wheels.myhloli.com
RUN pip3 install -r requirements.txt
RUN pip3 install transformers==4.45

# 调用magic-pdf, 自动下载相关模型文件
COPY app/test/docs/DeepSeek_R1.pdf ./DeepSeek_R1.pdf
COPY docker/magic-pdf.json ./magic-pdf.json
RUN  sed -i 's/cuda/cpu/g' ./magic-pdf.json
RUN export MINERU_TOOLS_CONFIG_JSON=/rag_server/magic-pdf.json && mkdir magic-output && magic-pdf -p ./DeepSeek_R1.pdf -m ocr -o magic-output -e 1 && rm -rf ./DeepSeek_R1.pdf && rm -rf ./magic-output
RUN python3 app/deepdoc/helpers.py

#启动命令
ENTRYPOINT ["./start.sh"]

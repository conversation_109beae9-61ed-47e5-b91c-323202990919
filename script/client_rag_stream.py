import aiohttp
import asyncio
import json

async def interactive_chat(url):
    print("欢迎使用交互式聊天测试客户端！")
    print("输入 'exit' 退出程序。")

    headers = {
        "Content-Type": "application/json"
    }

    async with aiohttp.ClientSession() as session:
        while True:
            user_input = input("\n请输入问题（或输入 'exit' 退出）：")
            if user_input.lower() == "exit":
                print("退出程序。")
                break

            dialog_id = 1  # 示例对话 ID，可以根据需要修改
            enable_rag = True  # 示例是否启用 RAG，可以根据需要修改

            data = {
                "question": user_input,
                "dialog_id": dialog_id,
                "enable_rag": enable_rag
            }

            try:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_data = await response.text()
                        print(f"\n请求失败，状态码：{response.status}")
                        print(f"错误信息：{error_data}")
                        continue

                    print("\n正在接收回答（流式输出）：")
                    # import pdb;pdb.set_trace()
                    async for line in response.content:
                        if line.strip():
                            decoded = line.decode('utf-8').strip()
                            if decoded.startswith('data: '):    # 流式chunk
                                event_data = decoded[6:]
                                if event_data == '[DONE]':
                                    print("\n[Stream completed]")
                                    return
                            
                                try:
                                    data = json.loads(event_data)
                                    if 'content' in data:
                                        print(data['content'], end='', flush=True)
                                    elif 'error' in data:
                                        print(f"\n[Error] {data['error']}")
                                except json.JSONDecodeError:
                                    print(f"\n[Invalid JSON] {event_data}")
                            elif decoded.startswith('final: '): # 最终结果
                                response_data = json.loads(decoded[6:])
                                code = response_data.get("code", 500)
                                message = response_data.get("message", "未知错误")
                                data = response_data.get("data", {})
                                if code == 200:
                                    answer = data.get("answer", "未知回答")
                                    references = data.get("references", [])
                                    # print(f"\n最终回答：{answer}")
                                    if references:
                                        print("\n\n[引用的文档]：")
                                        for ref in references:
                                            doc_title = ref.get("doc_title", "未知标题")
                                            doc_url = ref.get("url", "未知链接")
                                            print(f"- {doc_title} (链接: {doc_url})")

                                        # 检查链接是否可访问
                                        try:
                                            async with session.get(doc_url) as ref_response:
                                                if ref_response.status != 200:
                                                    print(f"  警告：无法访问链接 {doc_url}，状态码：{ref_response.status}")
                                                    print("  请检查链接的合法性或稍后重试。")
                                        except Exception as e:
                                            print(f"  错误：无法解析链接 {doc_url}，原因：{e}")
                                            print("  请检查链接的合法性或稍后重试。")
                            else:
                                print(f"\n[Unknown data] {decoded}")

                    print("\n\n回答结束。")
            except Exception as e:
                print(f"\n发生错误：{e}")
                print("请检查服务地址是否正确，或者稍后重试。")


if __name__ == "__main__":
    host = "**************"
    port = 9989
    service_url = f"http://{host}:{port}/api/v1/chat/completion/stream"  # 构造完整的服务地址

    asyncio.run(interactive_chat(service_url))
import aiohttp
import asyncio
import json

async def login(session, host, port):
    """登录并获取访问令牌"""
    login_url = f"http://{host}:{port}/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        async with session.post(login_url, json=login_data) as response:
            if response.status != 200:
                print("登录失败")
                return None
            
            response_data = await response.json()
            return response_data.get("data", {}).get("access_token")
    except Exception as e:
        print(f"登录时发生错误：{e}")
        return None

async def generate_article_stream_with_template(url, doc_topic, template_id, keywords, length=2000):
    print("开始测试使用模板生成文章的流式接口")
    
    async with aiohttp.ClientSession() as session:
        # 首先进行登录获取token
        token = await login(session, "**************", "9989")
        if not token:
            print("获取访问令牌失败，无法继续生成文章")
            return
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        data = {
            "doc_topic": doc_topic,
            "template_id": template_id,
            "keywords": keywords,
            "length": length
        }
        
        try:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_data = await response.text()
                    print(f"\n请求失败，状态码：{response.status}")
                    print(f"错误信息：{error_data}")
                    return
                
                print("\n正在接收文章内容（流式输出）：\n")
                full_article = ""
                
                async for line in response.content:
                    if line.strip():
                        decoded = line.decode('utf-8').strip()
                        if decoded.startswith('data: '):    # 流式chunk
                            event_data = decoded[6:]
                            if event_data == '[DONE]':
                                print("\n[Stream completed]")
                                break
                        
                            try:
                                data = json.loads(event_data)
                                if 'content' in data:
                                    content = data['content']
                                    full_article += content
                                    print(content, end='', flush=True)
                                elif 'error' in data:
                                    print(f"\n[Error] {data['error']}")
                            except json.JSONDecodeError:
                                print(f"\n[Invalid JSON] {event_data}")
                        elif decoded.startswith('final: '): # 最终结果
                            response_data = json.loads(decoded[6:])
                            code = response_data.get("code", 500)
                            message = response_data.get("message", "未知错误")
                            data = response_data.get("data", {})
                            if code == 200:
                                article = data.get("article", "")
                                storage_info = data.get("storage", {})
                                print("\n\n[完整文章已接收]")
                                print("\n\n存储文章信息：", storage_info)
                                
                                # 可以选择保存文章到文件
                                save_to_file = input("\n是否保存文章到文件？(y/n): ")
                                if save_to_file.lower() == 'y':
                                    filename = f"generated_article_{int(asyncio.get_event_loop().time())}.md"
                                    with open(filename, "w", encoding="utf-8") as f:
                                        f.write(article)
                                    print(f"文章已保存到文件: {filename}")
                        else:
                            print(f"\n[Unknown data] {decoded}")
                
                print("\n\n生成完成。")
        except Exception as e:
            print(f"\n发生错误：{e}")
            print("请检查服务地址是否正确，或者稍后重试。")

async def interactive_mode():
    """交互式模式，允许用户输入主题、模板ID和关键词"""
    host = "**************"
    port = 9989
    service_url = f"http://{host}:{port}/api/v1/generate/article/stream_with_template"
    
    print("欢迎使用模板文章生成流式测试客户端！")
    print("=" * 50)
    
    # 获取文档主题
    doc_topic = input("请输入文档主题 (默认: 人工智能在医疗领域的应用): ")
    if not doc_topic:
        doc_topic = "人工智能在医疗领域的应用"
    
    # 获取模板ID
    template_id_str = input("\n请输入模板ID (默认: 10): ")
    try:
        template_id = int(template_id_str) if template_id_str else 10
    except ValueError:
        print("输入的模板ID无效，将使用默认值10")
        template_id = 10
    
    # 获取关键词
    keywords_str = input("\n请输入关键词，用逗号分隔 (默认: 深度学习,医学影像,辅助诊断,智能医疗设备): ")
    if keywords_str:
        keywords = [k.strip() for k in keywords_str.split(',')]
    else:
        keywords = ["深度学习", "医学影像", "辅助诊断", "智能医疗设备"]
    # 获取文章长度
    length_str = input("\n请输入文章长度 (默认: 2000): ")
    try:
        length = int(length_str) if length_str else 2000
    except ValueError:
        print("输入的长度无效，将使用默认值2000")
        length = 2000
    
    # 确认生成
    print("\n=== 生成参数确认 ===")
    print(f"文档主题: {doc_topic}")
    print(f"模板ID: {template_id}")
    print(f"关键词: {', '.join(keywords)}")
    print(f"文章长度: {length}")
    
    confirm = input("\n确认开始生成? (y/n, 默认: y): ")
    if confirm.lower() not in ["n", "no"]:
        await generate_article_stream_with_template(service_url, doc_topic, template_id, keywords, length)
    else:
        print("已取消生成")


if __name__ == "__main__":
    asyncio.run(interactive_mode())
import aiohttp
import asyncio
import json

async def generate_article_stream(url, doc_topic, outline, length=2000, article_id=1):
    print("开始测试文章生成流式接口")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "doc_topic": doc_topic,
        "outline": outline,
        "length": length,
        "article_id": article_id
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_data = await response.text()
                    print(f"\n请求失败，状态码：{response.status}")
                    print(f"错误信息：{error_data}")
                    return
                
                print("\n正在接收文章内容（流式输出）：\n")
                full_article = ""
                
                async for line in response.content:
                    if line.strip():
                        decoded = line.decode('utf-8').strip()
                        if decoded.startswith('data: '):    # 流式chunk
                            event_data = decoded[6:]
                            if event_data == '[DONE]':
                                print("\n[Stream completed]")
                                break
                        
                            try:
                                data = json.loads(event_data)
                                if 'content' in data:
                                    content = data['content']
                                    full_article += content
                                    print(content, end='', flush=True)
                                elif 'error' in data:
                                    print(f"\n[Error] {data['error']}")
                            except json.JSONDecodeError:
                                print(f"\n[Invalid JSON] {event_data}")
                        elif decoded.startswith('final: '): # 最终结果
                            response_data = json.loads(decoded[6:])
                            code = response_data.get("code", 500)
                            message = response_data.get("message", "未知错误")
                            data = response_data.get("data", {})
                            if code == 200:
                                article = data.get("article", "")
                                storage_info = data.get("storage", {})
                                print("\n\n[完整文章已接收]")
                                print("\n\n存储文章信息：", storage_info)
                                
                                # 可以选择保存文章到文件
                                save_to_file = input("\n是否保存文章到文件？(y/n): ")
                                if save_to_file.lower() == 'y':
                                    filename = f"generated_article_{int(asyncio.get_event_loop().time())}.md"
                                    with open(filename, "w", encoding="utf-8") as f:
                                        f.write(article)
                                    print(f"文章已保存到文件: {filename}")
                        else:
                            print(f"\n[Unknown data] {decoded}")
                
                print("\n\n生成完成。")
    except Exception as e:
        print(f"\n发生错误：{e}")
        print("请检查服务地址是否正确，或者稍后重试。")


def get_sample_outline():
    """返回一个示例大纲"""
    return {
        "primary": [
            {
                "title": "低空经济的概念与发展现状", 
                "secondary": [
                    {
                        "title": "低空经济的定义与特征", 
                        "tertiary": [
                            {"title": "低空经济的基本概念与核心要素"}, 
                            {"title": "低空经济与传统经济的区别与联系"}
                        ]
                    }, 
                    {
                        "title": "全球低空经济的发展现状与趋势", 
                        "tertiary": [
                            {"title": "主要国家低空经济的发展模式与经验"}, 
                            {"title": "低空经济技术的创新与应用"}
                        ]
                    }
                ]
            }, 
            {
                "title": "低空经济对新质生产力的促进作用", 
                "secondary": [
                    {
                        "title": "新质生产力的内涵与低空经济的契合点", 
                        "tertiary": [
                            {"title": "新质生产力的核心特征与低空经济的关联"}, 
                            {"title": "低空经济如何推动新质生产力的形成"}
                        ]
                    }, 
                    {
                        "title": "低空经济在产业升级中的具体作用", 
                        "tertiary": [
                            {"title": "低空经济在智能制造中的应用与影响"}, 
                            {"title": "低空经济对物流与供应链的优化作用"}
                        ]
                    }
                ]
            }, 
            {
                "title": "低空经济发展的挑战与未来展望", 
                "secondary": [
                    {
                        "title": "低空经济发展面临的主要挑战", 
                        "tertiary": [
                            {"title": "政策与法规的制约因素"}, 
                            {"title": "技术与安全问题的解决方案"}
                        ]
                    }, 
                    {
                        "title": "低空经济的未来发展方向与机遇", 
                        "tertiary": [
                            {"title": "低空经济在绿色经济中的潜力"}, 
                            {"title": "低空经济与新质生产力的协同发展路径"}
                        ]
                    }
                ]
            }
        ]
    }


async def interactive_mode():
    """交互式模式，允许用户输入主题和选择预设大纲或自定义大纲"""
    host = "**************"
    port = 9901
    service_url = f"http://{host}:{port}/api/v1/generate/article/stream"
    
    print("欢迎使用文章生成流式测试客户端！")
    print("=" * 50)
    
    # 获取文档主题
    doc_topic = input("请输入文档主题 (默认: 低空经济发展对新质生成力具有极大促进): ")
    if not doc_topic:
        doc_topic = "低空经济发展对新质生成力具有极大促进"
    
    # 选择大纲方式
    print("\n请选择大纲来源:")
    print("1. 使用预设的示例大纲")
    print("2. 从文件加载大纲")
    print("3. 手动输入大纲JSON")
    
    choice = input("请选择 (1-3, 默认: 1): ")
    
    outline = None
    if choice == "2":
        file_path = input("请输入大纲JSON文件路径: ")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                outline = json.load(f)
        except Exception as e:
            print(f"读取文件失败: {e}")
            print("将使用默认大纲...")
            outline = get_sample_outline()
    elif choice == "3":
        print("请输入大纲JSON (输入完成后按Ctrl+D结束):")
        outline_str = ""
        try:
            while True:
                line = input()
                outline_str += line + "\n"
        except EOFError:
            try:
                outline = json.loads(outline_str)
            except json.JSONDecodeError:
                print("JSON格式错误，将使用默认大纲...")
                outline = get_sample_outline()
    else:
        outline = get_sample_outline()
    
    # 获取文章长度
    length_str = input("\n请输入文章长度 (默认: 2000): ")
    try:
        length = int(length_str) if length_str else 2000
    except ValueError:
        print("输入的长度无效，将使用默认值2000")
        length = 2000
    
    # 确认生成
    print("\n=== 生成参数确认 ===")
    print(f"文档主题: {doc_topic}")
    print(f"文章长度: {length}")
    print("大纲: ", json.dumps(outline, ensure_ascii=False, indent=2)[:100] + "...")
    article_id = 1
    confirm = input("\n确认开始生成? (y/n, 默认: y): ")
    if confirm.lower() not in ["n", "no"]:
        await generate_article_stream(service_url, doc_topic, outline, length, article_id)
    else:
        print("已取消生成")


if __name__ == "__main__":
    asyncio.run(interactive_mode())
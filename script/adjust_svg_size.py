
# 调整svg的像素


import xml.etree.ElementTree as ET

def resize_svg(input_file, output_file, width, height):
    # 解析 SVG 文件
    tree = ET.parse(input_file)
    root = tree.getroot()

    # 设置新的宽度和高度
    root.set('width', str(width))
    root.set('height', str(height))

    # 保存修改后的 SVG 文件
    tree.write(output_file)

# 示例调用
input_file = 'input.svg'
output_file = 'output.svg'
width = 100
height = 60
resize_svg(input_file, output_file, width, height)
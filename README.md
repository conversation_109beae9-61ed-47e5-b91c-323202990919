# 部署RAG
```
conda create -n rag python=3.10
conda activate rag
pip3 install -U "magic-pdf[full]" --extra-index-url https://wheels.myhloli.com
pip3 install -r requirements.txt
```


# 问题记录：
1. magic-pdf报错: 

```
ValueError: CustomMBartDecoder does not support an attention implementation through torch.nn.functional.scaled_dot_product_attention yet. Please request the support for this architecture: https://github.com/huggingface/transformers/issues/28005.  If you believe this error is a bug, please open an issue in Transformers GitHub repository and load your model with the argument `attn_implementation="eager"` meanwhile. Example: `model = AutoModel.from_pretrained("openai/whisper-tiny", attn_implementation="eager")`
```

- 修改transformers的版本为4.45  
```pip3 install transformers==4.45```

- 修改 magif-pdf 配置路径
```export MINERU_TOOLS_CONFIG_JSON=/home/<USER>/.magic_pdf/magic-pdf.json```

2. 用多张卡会有问题
先用单张卡```BGEM3FlagModel(config.EMB_MODEL_PATH["bge-m3"], use_fp16=True, devices="cuda:0")```


3. magic-pdf 解析pdf错误，offlicelib缺失
```yum install libreoffice```

4. 在docker中使用GPU
安装NVIDIA Container Toolkit
```curl -s -L https://nvidia.github.io/libnvidia-container/stable/rpm/nvidia-container-toolkit.repo | sudo tee /etc/yum.repos.d/nvidia-container-toolkit.repo```

配置/etc/docker/daemon.json ，加入：
```
{
    "runtimes": {
        "nvidia": {
            "path": "/usr/bin/nvidia-container-runtime",
            "runtimeArgs": []
        }
    },
    "default-runtime": "nvidia"
}
```

重启docker
```systemctl restart docker```


**服务端口地址**：http://**************:9999/

**数据库及LLM等配置信息**： 详见 ```app/config.py```

# 启动
## 文档导入
测试环境下已经导入了，可以直接启动服务。
```
conda activate rag
cd app && python3 import.py ./test/docs
```

## 服务启动
```
conda activate rag
cd app && nohup python3 main.py &
```

### 使用客户端测试
```
# 非流式返回
python script/client_rag.py

# 流式返回
python script/client_rag_stream.py
```


# TODO
- 文档管理接口
- 整理部署方案
- 接入权限系统
- 支持多版本系统
- 流式返回 （先生成内容，最后返回引用） -- DONE
- 文档管理（删除文档、接口）
- 检索策略封装
- 文档导入策略封装
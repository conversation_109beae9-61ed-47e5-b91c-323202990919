"""Abstract interface for document loader implementations."""

from typing import Optional, List, Dict
import re
import logging
import config as conf
import os

from deepdoc.helpers import detect_file_encodings, num_tokens_from_string
from deepdoc.base_extractor import BaseExtractor, ExtractorResult


class TxtExtractor(BaseExtractor):
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def __call__(self, file_path=None, chunk_token_num=2000, delimiter="\n!?;。；！？") -> List[Dict[str, str]]:
        self.logger.info(f"Extracting text from {file_path}")

        self.detected_encodings = detect_file_encodings(file_path)
        txt = self.__get_text(file_path)
        chunks = self.parser_txt(txt, chunk_token_num, delimiter)
        
        preview_local_path = os.path.join(conf.TEMP_DIR, f"preview_tmp_loal_{os.path.basename(file_path)}")
        with open(preview_local_path, 'w', encoding='utf-8') as f:
            f.write(txt)

        # 转换为统一的返回格式
        chunks = [{"title": "", "content": chunk} for chunk in chunks if chunk.strip()]
        
        return ExtractorResult(chunks, txt, preview_local_path, "")

    def __get_text(self, file_path):
        txt = ""
        with open(file_path, "rb") as f:
            for encoding in self.detected_encodings:
                try:
                    txt = f.read().decode(encoding.encoding)
                    break
                except UnicodeDecodeError:
                    continue
                
        return txt

    @classmethod
    def parser_txt(cls, txt, chunk_token_num, delimiter) -> List[str]:
        if not isinstance(txt, str):
            raise TypeError("txt type should be str!")
        chunks = [""]
        tk_nums = [0]
        delimiter = delimiter.encode('utf-8').decode('unicode_escape').encode('latin1').decode('utf-8')

        def add_chunk(t):
            nonlocal chunks, tk_nums, delimiter
            tnum = num_tokens_from_string(t)
            if tk_nums[-1] > chunk_token_num:
                chunks.append(t)
                tk_nums.append(tnum)
            else:
                chunks[-1] += t
                tk_nums[-1] += tnum

        dels = []
        s = 0
        for m in re.finditer(r"`([^`]+)`", delimiter, re.I):
            f, t = m.span()
            dels.append(m.group(1))
            dels.extend(list(delimiter[s: f]))
            s = t
        if s < len(delimiter):
            dels.extend(list(delimiter[s:]))
        dels = [re.escape(d) for d in dels if d]
        dels = [d for d in dels if d]
        dels = "|".join(dels)
        secs = re.split(r"(%s)" % dels, txt)
        for sec in secs:
            if re.match(f"^{dels}$", sec):
                continue
            add_chunk(sec)

        return chunks


if __name__ == "__main__":
    extractor = TxtExtractor()
    chunks, text = extractor('/home/<USER>/projects/接口文档.txt')
    from pprint import pprint
    pprint(chunks)
    # print(text)
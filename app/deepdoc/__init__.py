from constant.constant import ParserType
from deepdoc.strategies.policy import PolicyStrategy
from deepdoc.strategies.legal import LegalStrategy
from deepdoc.strategies.paper import PaperStrategy
from deepdoc.strategies.regulation import RegulationStrategy
from deepdoc.strategies.redhead import RedHeadStrategy

PARSER_FACTORY = {
    ParserType.REGULATION.value: RegulationStrategy,
    ParserType.LEGAL.value: LegalStrategy,
    ParserType.PAPER.value: PaperStrategy,
    ParserType.POLICY.value: PolicyStrategy,
    ParserType.READHEAD.value: RedHeadStrategy
}
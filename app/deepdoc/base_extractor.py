from abc import ABC, abstractmethod
from typing import List

class ExtractorResult:
    def __init__(self, chunks, md_content, preview_file_local_path, page_origin_text):
        self.chunks = chunks
        self.md_content = md_content
        self.preview_file_local_path = preview_file_local_path
        self.page_origin_text = page_origin_text

class BaseExtractor(ABC):
    """文档提取器基类"""
    
    @abstractmethod
    def __call__(self, file_path: str) -> List[str]:
        """处理文档并返回文本块列表"""
        pass
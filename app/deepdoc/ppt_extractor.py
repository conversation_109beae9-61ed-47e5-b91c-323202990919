import logging
from io import BytesIO
from pptx import Presentation
from typing import List, Dict
import os

from deepdoc.base_extractor import BaseExtractor, ExtractorResult
from utils.doc_parser import parse_doc_task
from utils.preprocess import extract_content_with_titles

logger = logging.getLogger(__name__)


class PptExtractor(BaseExtractor):

    def __call__(self, file_path, callback=None) -> List[Dict]:
        logger.info(f"开始处理PPT文件: {file_path}")
        try:
            md_content, content_list_content, tmp_pdf_path, page_origin_text = parse_doc_task(file_path)
            chunks = extract_content_with_titles(content_list_content)
            # os.remove(tmp_pdf_path)
            return ExtractorResult(chunks, md_content, tmp_pdf_path, page_origin_text)
        
        except Exception as e:
            logger.error(f"处理PPT文件时出错: {str(e)}")
            raise ValueError(f"PPT解析失败: {str(e)}")
        
        # try:
        #     ppt = Presentation(file_path)
        #     result = []
        #     self.total_page = len(ppt.slides)
            
        #     # 提取每个幻灯片的内容
        #     for i, slide in enumerate(ppt.slides):
        #         slide_title = f"幻灯片 {i+1}"
                
        #         # 尝试获取幻灯片标题
        #         if slide.shapes.title and slide.shapes.title.text.strip():
        #             slide_title = f"{slide_title} - {slide.shapes.title.text.strip()}"
                
        #         texts = []
        #         # 按位置排序处理形状
        #         for shape in sorted(
        #                 slide.shapes, key=lambda x: ((x.top if x.top is not None else 0) // 10, x.left)):
        #             try:
        #                 txt = self.__extract(shape)
        #                 if txt and txt.strip():
        #                     texts.append(txt)
        #             except Exception as e:
        #                 logging.error(f"处理幻灯片 {i+1} 的形状时出错: {str(e)}")
                
        #         # 合并当前幻灯片的所有文本
        #         slide_content = "\n".join(texts)
        #         if slide_content.strip():
        #             result.append({
        #                 "title": slide_title,
        #                 "content": slide_content
        #             })
            
        #     # 添加整个PPT的标题列表作为最后一个特殊切片
        #     all_titles = " - ".join([item["title"] for item in result])
        #     result.append({"title": all_titles, "content": ""})
            
        #     return result
            
        # except Exception as e:
        #     logging.error(f"处理PPT文件时出错: {str(e)}")
        #     raise ValueError(f"PPT解析失败: {str(e)}")
        
    def __get_bulleted_text(self, paragraph):
        is_bulleted = bool(paragraph._p.xpath("./a:pPr/a:buChar")) or bool(paragraph._p.xpath("./a:pPr/a:buAutoNum")) or bool(paragraph._p.xpath("./a:pPr/a:buBlip"))
        if is_bulleted:
            return f"{'  '* paragraph.level}.{paragraph.text}"
        else:
            return paragraph.text

    def __extract(self, shape):
        if shape.shape_type == 19:  # 表格
            tb = shape.table
            rows = []
            for i in range(1, len(tb.rows)):
                rows.append("; ".join([tb.cell(
                    0, j).text + ": " + tb.cell(i, j).text for j in range(len(tb.columns)) if tb.cell(i, j)]))
            return "\n".join(rows)

        if shape.has_text_frame:  # 文本框
            text_frame = shape.text_frame
            texts = []
            for paragraph in text_frame.paragraphs:
                if paragraph.text.strip():
                    texts.append(self.__get_bulleted_text(paragraph))
            return "\n".join(texts)

        if shape.shape_type == 6:  # 组合形状
            texts = []
            for p in sorted(shape.shapes, key=lambda x: (x.top // 10, x.left)):
                t = self.__extract(p)
                if t:
                    texts.append(t)
            return "\n".join(texts)
"""Abstract interface for document loader implementations."""

import csv
import logging
from typing import List, Dict, Tuple, Optional
import traceback
import pandas as pd
import config as conf
import os
from utils.extractor import html_text_from_csv

from deepdoc.base_extractor import BaseExtractor, ExtractorResult
from deepdoc.helpers import detect_file_encodings

logger = logging.getLogger(__name__)


class CsvExtractor(BaseExtractor):
    """Load CSV files."""
    
    def __init__(
        self,
        autodetect_encoding: bool = True,
        source_column: Optional[str] = None,
        csv_args: Optional[dict] = None,
    ):
        """Initialize CSV extractor."""
        self._autodetect_encoding = autodetect_encoding
        self.source_column = source_column
        self.csv_args = csv_args or {}

    def __call__(self, file_path) -> Tuple[List[Dict[str, str]], str]:
        """解析CSV文件"""
        logger.info(f"开始处理CSV文件: {file_path}")
        try:
            chunks, text = self.parse_csv(file_path)

            base_name, _ = os.path.splitext(os.path.basename(file_path))
            preview_local_path = os.path.join(conf.TEMP_DIR, f"preview_tmp_loal_{base_name}.html")
            with open(preview_local_path, 'w', encoding='utf-8') as html_file:
                html_file.write(text)

            return ExtractorResult(chunks, text, preview_local_path, "")
        except Exception as e:
            logger.error(f"处理CSV文件时出错: {str(e)}")
            logger.error(traceback.format_exc())
            raise ValueError(f"CSV解析失败: {str(e)}")

    def parse_csv(self, file_path: str) -> Tuple[List[Dict[str, str]], str]:
        """解析CSV文件"""
        chunks = []
        all_content = []
        encoding = None
        df = None

        # 首先尝试常见的中文编码
        encodings_to_try = ['utf-8', 'gbk', 'gb18030', 'gb2312', 'big5']
        
        for enc in encodings_to_try:
            try:
                df = pd.read_csv(file_path, encoding=enc, on_bad_lines="skip", **self.csv_args)
                encoding = enc
                logger.info(f"成功使用 {enc} 编码读取CSV文件")
                break
            except UnicodeDecodeError:
                logger.warning(f"使用 {enc} 编码读取CSV文件时出错: UnicodeDecodeError")
                continue
            except Exception as e:
                logger.warning(f"使用 {enc} 编码读取CSV文件时出错: {str(e)}")
                continue
        
        if df is None:
            error_msg = f"无法使用任何编码格式读取CSV文件: {file_path}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # 检查源列是否存在
        if self.source_column and self.source_column not in df.columns:
            raise ValueError(f"源列 '{self.source_column}' 在CSV文件中未找到。")
        
        # 处理每一行数据
        row_contents = []
        for i, row in df.iterrows():
            row_content = []
            for col in df.columns:
                if pd.notna(row[col]):
                    value = str(row[col]).strip()
                    
                    # 每个单元格作为一个独立的chunk
                    chunks.append({
                        "title": f"{col}",
                        "content": value
                    })
                    
                    # 为了生成完整文本，保留原来的格式
                    row_content.append(f"{col}: {value}")
            
            if row_content:
                row_str = "; ".join(row_content)
                row_contents.append(row_str)
        
        # 生成完整文本
        if row_contents:
            content = "\n".join(row_contents)
            all_content.append(f"## CSV内容\n\n{content}")
        
        # 将所有内容合并为一个字符串
        # full_text = "\n\n".join(all_content)
        full_text = html_text_from_csv(file_path)
        
        return [], full_text


if __name__ == '__main__':
    extractor = CsvExtractor()
    chunks, text = extractor('/home/<USER>/projects/接口文档.csv')
    from pprint import pprint
    pprint(chunks)
    # print(text)

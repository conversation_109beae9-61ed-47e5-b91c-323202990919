import traceback

from .base_strategy import BaseParsingStrategy, ExtractorResult


class RegulationStrategy(BaseParsingStrategy):
    """
    规章制度解析策略
    """
    def __init__(self, extract_res: ExtractorResult):
        # super().__init__(raw_content_data)
        # self.regulation_elements = {}
        # self.file_name = file_name
        pass

    async def parse(self):
        """
        实现规章制度解析逻辑。
        :return: 解析后的规章制度结果
        """
        # self.logger.info(f"开始解析规章制度文档, 文档名字为: {self.file_name}")
        
        return 

    async def summarize(self) -> str:
        """
        辅助方法，生成解析结果的摘要。
        :param prompt: LLM 提示词
        :param text: 需要生成摘要的文本
        """
        
        return
import traceback

from .base_strategy import BaseParsingStrategy
from deepdoc.base_extractor import ExtractorResult


class RedHeadStrategy(BaseParsingStrategy):
    """
    规章制度解析策略
    """
    def __init__(self, extract_res: ExtractorResult):
        super().__init__(extract_res.md_content)
        self.regulation_elements = {}
        self.file_name = extract_res.preview_file_local_path
        self.extract_res = extract_res

    async def parse(self):
        """
        实现规章制度解析逻辑。
        :return: 解析后的规章制度结果
        """
        # self.logger.info(f"开始解析规章制度文档, 文档名字为: {self.file_name}")
        
        return 

    async def summarize(self) -> str:
        """
        辅助方法，生成解析结果的摘要。
        :param prompt: LLM 提示词
        :param text: 需要生成摘要的文本
        """
        
        return self.extract_res.page_origin_text[0] + "\n" + self.extract_res.page_origin_text[-1]
from abc import ABC, abstractmethod
import logging
from deepdoc.base_extractor import ExtractorResult


class BaseParsingStrategy(ABC):
    """
    解析策略的基类。
    所有具体的解析策略都应继承此类并实现 parse 方法。
    """
    def __init__(self, raw_content_data: any):
        """
        构造函数。
        :param raw_content_data: 由文档格式转换模块/基础内容提取器提供的基础内容。
        """
        self.raw_content_data = raw_content_data
        self.parsed_elements = {} # 用于存储解析出来的各个元素
        self.logger = logging.getLogger(__name__)

    @abstractmethod
    async def parse(self) -> dict:
        """
        核心解析方法，由子类实现。
        """
        pass
    
    @abstractmethod
    async def summarize(self) -> str:
        """
        辅助方法，生成解析结果的摘要。
        :param prompt: LLM 提示词
        :param text: 需要生成摘要的文本
        """
        # 这里可以根据需要实现摘要逻辑
        return 

    # @abstractmethod
    # async def _extract_text_from_raw_data(self) -> str:
    #     """
    #     辅助方法，从 raw_content_data 中提取纯文本。
    #     具体实现取决于 raw_content_data 的结构。
    #     """
    #     if isinstance(self.raw_content_data, str):
    #         return 
    #     elif isinstance(self.raw_content_data, dict):
    #         return 
        
    #     # ... 其他可能的结构处理
    #     raise NotImplementedError("无法从 raw_content_data 提取纯文本，请检查转换器输出和此方法的实现。")

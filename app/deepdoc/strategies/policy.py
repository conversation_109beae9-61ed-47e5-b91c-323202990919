import traceback
import logging

from .base_strategy import BaseParsingStrategy, ExtractorResult


class PolicyStrategy(BaseParsingStrategy):
    """
    政策解析策略
    """
    def __init__(extract_res: ExtractorResult):
        # super().__init__(raw_content_data)
        # self.file_name = file_name
        # self.policy_elements = {}  # 用于存储解析出来的政策元素
        pass

    async def parse(self):
        """
        实现政策解析逻辑。
        :return: 解析后的政策元素字典
        """
        # self.logger.info(f"开始解析政策文档, 文档名字为: {self.file_name}")
        
        return 
    
    async def summarize(self) -> str:
        """
        辅助方法，生成解析结果的摘要。
        :param prompt: LLM 提示词
        :param text: 需要生成摘要的文本
        """
        
        return
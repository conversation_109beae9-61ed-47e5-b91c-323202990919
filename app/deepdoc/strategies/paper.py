import traceback

from .base_strategy import BaseParsingStrategy, ExtractorResult


class PaperStrategy(BaseParsingStrategy):
    """
    论文解析策略
    """
    def __init__(self, extract_res: ExtractorResult):
        # super().__init__(raw_content_data)
        # self.file_name = file_name
        # self.paper_elements = {}  # 用于存储解析出来的论文元素
        pass

    async def parse(self):
        """
        实现论文解析逻辑。
        :return: 解析后的论文元素字典
        """
        # self.logger.info(f"开始解析论文文档， 文档名字为: {self.file_name}")
        
        return 
    
    async def summarize(self) -> str:
        """
        辅助方法，生成解析结果的摘要。
        :param prompt: LLM 提示词
        :param text: 需要生成摘要的文本
        """
        
        return
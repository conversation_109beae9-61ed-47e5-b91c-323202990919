"""Abstract interface for document loader implementations."""

from typing import List
import os
import asyncio
import logging
import concurrent.futures
import torch
import config
import multiprocessing

from deepdoc.txt_extractor import TxtExtractor
from deepdoc.ppt_extractor import PptExtractor
from deepdoc.pdf_extractor import PdfExtractor
from deepdoc.md_extractor import MarkdownExtractor
from deepdoc.docx_extractor import DocxExtractor
from deepdoc.html_extractor import HtmlExtractor
from deepdoc.excel_extractor import ExcelExtractor
from deepdoc.csv_extractor import CsvExtractor
from utils.minio import upload_file

logger = logging.getLogger(__name__)
if multiprocessing.get_start_method(True) is None:
    multiprocessing.set_start_method('spawn')
executor = concurrent.futures.ProcessPoolExecutor(max_workers=config.MINERU_POOL_SIZE)
torch.set_num_threads(10)  # 防止线程竞争开销过大


class FileParser:
    """Interface for extract files."""

    @classmethod
    async def extract(cls, file_path: str):
        """Extract chunks from file."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        file_type = os.path.splitext(file_path)[1].lower()  # 添加 lower() 确保大小写一致
        try:
            if file_type == ".txt":
                extractor = TxtExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            elif file_type in [".ppt", ".pptx"]:
                extractor = PptExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            elif file_type == ".pdf":
                extractor = PdfExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            elif file_type in [".doc", ".docx"]:
                extractor = DocxExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            elif file_type in [".md", ".markdown"]:
                extractor = MarkdownExtractor()
                return await cls._run_in_executor(extractor, file_path)

            elif file_type == ".html":
                extractor = HtmlExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            elif file_type in [".xlsx", ".xls"]:
                extractor = ExcelExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            elif file_type == ".csv":
                extractor = CsvExtractor()
                return await cls._run_in_executor(extractor, file_path)
            
            else:
                logger.error(f"Unsupported file type: {file_type}")
                raise ValueError(f"Unsupported file type: {file_type}")
                
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            raise Exception(f"Error processing file {file_path}: {str(e)}")
    
    @classmethod
    async def _run_in_executor(cls, extractor, file_path):
        """在线程池中运行同步代码"""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(executor, extractor, file_path)
    
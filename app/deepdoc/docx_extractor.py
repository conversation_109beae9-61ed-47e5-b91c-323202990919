import re
from typing import List, Optional, Callable
import logging
import os
import asyncio

from deepdoc.base_extractor import BaseExtractor, ExtractorResult
from utils.doc_parser import parse_doc_task
from utils.preprocess import extract_content_with_titles

logger = logging.getLogger(__name__)


class DocxExtractor(BaseExtractor):
    def __call__(self, file_path: str) -> List[dict]:
        """解析 docx 文件"""
        logger.info(f"解析docx文件 {file_path}")

        # todo 临时先将 docx 转为 pdf，后期再进行优化
        md_content, content_list_content, tmp_pdf_path, page_origin_text = parse_doc_task(file_path)
        chunks = extract_content_with_titles(content_list_content)

        # os.remove(tmp_pdf_path)

        return ExtractorResult(chunks, md_content, tmp_pdf_path, page_origin_text)
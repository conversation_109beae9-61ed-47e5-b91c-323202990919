"""Abstract interface for document loader implementations."""

import re
from pathlib import Path
from typing import Optional, cast, List
import logging

from deepdoc.base_extractor import BaseExtractor, ExtractorResult
from deepdoc.helpers import detect_file_encodings


class MarkdownExtractor(BaseExtractor):
    """Load Markdown files.


    Args:
        file_path: Path to the file to load.
    """

    def __init__(
        self,
        remove_hyperlinks: bool = True,
        remove_images: bool = True,
    ):
        """Initialize with file path."""
        self._remove_hyperlinks = remove_hyperlinks
        self._remove_images = remove_images
        self.logger = logging.getLogger(__name__)

    def __call__(self, file_path) -> List[dict]:
        """Load from file path."""
        self.logger.info(f"开始处理Markdown文件: {file_path}")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()

            chunks = []
            # 解析文档内容
            tups = self.parse_tups(file_path)
            
            # 如果没有任何标题，创建默认章节
            if not tups or (len(tups) == 1 and tups[0][0] is None):
                content = tups[0][1] if tups else ""
                if content.strip():
                    chunks.append({
                        "title": "文档内容",
                        "content": content.strip()
                    })
            else:
                # 处理每个标题和内容
                for header, content in tups:
                    if content.strip():
                        title = header if header else "未命名章节"
                        chunks.append({
                            "title": title,
                            "content": content.strip()
                        })
            
            return ExtractorResult(chunks, text, file_path, "")
            
        except Exception as e:
            self.logger.error(f"处理Markdown文件时出错: {str(e)}")
            raise ValueError(f"Markdown解析失败: {str(e)}")

    def markdown_to_tups(self, markdown_text: str) -> list[tuple[Optional[str], str]]:
        """Convert a markdown file to a dictionary.

        The keys are the headers and the values are the text under each header.

        """
        markdown_tups: list[tuple[Optional[str], str]] = []
        lines = markdown_text.split("\n")

        current_header = None
        current_text = ""
        code_block_flag = False

        for line in lines:
            if line.startswith("```"):
                code_block_flag = not code_block_flag
                current_text += line + "\n"
                continue
            if code_block_flag:
                current_text += line + "\n"
                continue
            header_match = re.match(r"^#+\s", line)
            if header_match:
                if current_header is not None:
                    markdown_tups.append((current_header, current_text))

                current_header = line
                current_text = ""
            else:
                current_text += line + "\n"
        markdown_tups.append((current_header, current_text))

        if current_header is not None:
            # pass linting, assert keys are defined
            markdown_tups = [
                (re.sub(r"#", "", cast(str, key)).strip(), re.sub(r"<.*?>", "", value)) for key, value in markdown_tups
            ]
        else:
            markdown_tups = [(key, re.sub("\n", "", value)) for key, value in markdown_tups]

        return markdown_tups

    def remove_images(self, content: str) -> str:
        """Get a dictionary of a markdown file from its path."""
        pattern = r"!{1}\[\[(.*)\]\]"
        content = re.sub(pattern, "", content)
        return content

    def remove_hyperlinks(self, content: str) -> str:
        """Get a dictionary of a markdown file from its path."""
        pattern = r"\[(.*?)\]\((.*?)\)"
        content = re.sub(pattern, r"\1", content)
        return content

    def parse_tups(self, filepath: str) -> list[tuple[Optional[str], str]]:
        """Parse file into tuples."""
        content = ""
        try:
            detected_encodings = detect_file_encodings(filepath)
            for encoding in detected_encodings:
                try:
                    content = Path(filepath).read_text(encoding=encoding.encoding)
                    break
                except UnicodeDecodeError:
                    continue
        except Exception as e:
            raise RuntimeError(f"Error loading {filepath}") from e

        if self._remove_hyperlinks:
            content = self.remove_hyperlinks(content)

        if self._remove_images:
            content = self.remove_images(content)

        return self.markdown_to_tups(content)


if __name__ == "__main__":
    extractor = MarkdownExtractor()
    chunks, text = extractor("../README.md")
    from pprint import pprint
    pprint(chunks)
    # print(text)
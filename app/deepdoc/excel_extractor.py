import logging
from typing import List, Dict, Tuple, Optional
import os
import pandas as pd
from openpyxl import load_workbook
from utils.extractor import html_text_from_excel
import config as conf

from deepdoc.base_extractor import BaseExtractor, ExtractorResult

logger = logging.getLogger(__name__)


class ExcelExtractor(BaseExtractor):

    def __call__(self, file_path) -> Tuple[List[Dict[str, str]], str]:
        """解析excel文件"""
        logger.info(f"开始处理Excel文件: {file_path}")
        try:
            chunks, text = self.parse_excel(file_path)

            base_name, _ = os.path.splitext(os.path.basename(file_path))
            preview_local_path = os.path.join(conf.TEMP_DIR, f"preview_tmp_loal_{base_name}.html")
            with open(preview_local_path, 'w', encoding='utf-8') as html_file:
                html_file.write(text)

            return ExtractorResult(chunks, text, preview_local_path, "")
        except Exception as e:
            logger.error(f"处理Excel文件时出错: {str(e)}")
            raise ValueError(f"Excel解析失败: {str(e)}")

    def parse_excel(self, file_path: str) -> Tuple[List[Dict[str, str]], str]:
        """解析excel文件"""
        chunks = []
        all_content = []
        file_extension = os.path.splitext(file_path)[-1].lower()
        
        if file_extension == '.xlsx':
            wb = load_workbook(file_path, data_only=True)
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                data = sheet.values
                try:
                    cols = next(data)
                except StopIteration:
                    continue
                
                df = pd.DataFrame(data, columns=cols)
                df.dropna(how="all", inplace=True)
                
                sheet_content = []
                for index, row in df.iterrows():
                    for col_index, (k, v) in enumerate(row.items()):
                        if pd.notna(v):
                            cell = sheet.cell(row=index + 2, column=col_index + 1)
                            if cell.hyperlink:
                                value = f"[{v}]({cell.hyperlink.target})"
                            else:
                                value = str(v)
                            
                            # 每个单元格作为一个独立的chunk
                            chunks.append({
                                "title": f"{k}",
                                "content": value
                            })
                            
                            # 为了生成完整文本，仍然保留原来的格式
                            sheet_content.append(f"{k}: {value}")
                
                if sheet_content:
                    content = "\n".join(sheet_content)
                    all_content.append(f"## 工作表 - {sheet_name}\n\n{content}")
                
        elif file_extension == ".xls":
            excel_file = pd.ExcelFile(file_path, engine="xlrd")
            for sheet_name in excel_file.sheet_names:
                df = excel_file.parse(sheet_name=sheet_name)
                df.dropna(how="all", inplace=True)
                
                sheet_content = []
                for _, row in df.iterrows():
                    for k, v in row.items():
                        if pd.notna(v):
                            value = str(v)
                            
                            # 每个单元格作为一个独立的chunk
                            chunks.append({
                                "title": f"{k}",
                                "content": value
                            })
                            
                            # 为了生成完整文本，仍然保留原来的格式
                            sheet_content.append(f"{k}: {value}")
                
                if sheet_content:
                    content = "\n".join(sheet_content)
                    all_content.append(f"## 工作表 - {sheet_name}\n\n{content}")
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")

        # 将所有工作表内容合并为一个字符串
        # full_text = "\n\n".join(all_content)
        full_text, chunks = html_text_from_excel(file_path)
        
        return chunks, full_text


if __name__ == '__main__':
    extractor = ExcelExtractor()
    # chunks, text = extractor('/home/<USER>/projects/接口文档.xlsx')
    chunks, text = extractor('/home/<USER>/24年合资企业表总版12.xlsx')
    from pprint import pprint
    # pprint(chunks)
    print(text)

import readability
import html_text
import chardet
import logging
from typing import List, Dict, Tuple

from deepdoc.base_extractor import BaseExtractor, ExtractorResult
from deepdoc.helpers import find_codec

logger = logging.getLogger(__name__)


class HtmlExtractor(BaseExtractor):
    def __call__(self, file_path, binary=None) -> Tuple[List[Dict[str, str]], str]:
        logger.info(f"解析 html 文件: {file_path}")

        txt = ""
        if binary:
            encoding = find_codec(binary)
            txt = binary.decode(encoding, errors="ignore")
        else:
            with open(file_path, "r", encoding=self._get_encoding(file_path)) as f:
                txt = f.read()

        chunks = self.parser_txt(txt)
        return ExtractorResult(chunks, txt, file_path, "")
    
    def _get_encoding(self, file):
        with open(file,'rb') as f:
            tmp = chardet.detect(f.read())
            return tmp['encoding']

    @classmethod
    def parser_txt(cls, txt) -> List[Dict[str, str]]:
        if not isinstance(txt, str):
            logger.warning(f"txt type is not str, type(txt)={type(txt)}")
            raise TypeError("txt type should be str!")
        
        chunks = []
        html_doc = readability.Document(txt)
        title = html_doc.title()
        content = html_text.extract_text(html_doc.summary(html_partial=True))
        
        # 将内容按段落分割
        sections = content.split("\n")
        
        # 如果有标题，将标题作为第一个chunk
        if title:
            chunks.append({
                "title": "标题",
                "content": title
            })
        
        # 将每个段落作为一个chunk
        for i, section in enumerate(sections, 1):
            if section.strip():
                chunks.append({
                    "title": f"段落{i}",
                    "content": section.strip()
                })

        return chunks


if __name__ == "__main__":
    extractor = HtmlExtractor()
    chunks, text = extractor('/home/<USER>/projects/weather.html')
    print(chunks)
    print(text)

import os

from utils.doc_parser import parse_doc_task
from utils.preprocess import extract_content_with_titles
from deepdoc.base_extractor import BaseExtractor, ExtractorResult


class PdfExtractor(BaseExtractor):
    
    def __call__(self, file_path, callback=None):
        md_content, content_list_content, tmp_pdf_path, page_origin_text = parse_doc_task(file_path)
        chunks = extract_content_with_titles(content_list_content)
        # os.remove(tmp_pdf_path)
        
        return ExtractorResult(chunks, md_content, tmp_pdf_path, page_origin_text)
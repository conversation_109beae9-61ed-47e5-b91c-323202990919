# encoding: utf-8
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
import logging
import traceback

from connector.retrieval_document import RetrievalDocument as Document
from connector.field import Retrieval<PERSON>ield as Field
import config as conf


class ElasticSearchClient(object):
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = self._init_client()
        pass
    
    def _init_client(self):
        try:
            hosts = conf.ES_HOST
            client = Elasticsearch(
                hosts=conf.ES_HOST,
                basic_auth=(conf.ES_USER, conf.ES_PASSWORD),
                request_timeout=30,
                retry_on_timeout=True,
                max_retries=3
            )
            return client
        except Exception as e:
            self.logger.error(f"连接 Elasticsearch 失败: {e}, {traceback.format_exc()}")
            return None
    
    @property
    def knowlegde_qa_mapping(self):
        """
        创建 knowlegde_qa 索引的映射
        """
        return {
            "mappings":{
                "properties":{
                    Field.CHUNK_ID.value: {"type": "keyword"},
                    Field.DOC_ID.value: {"type": "keyword"},
                    Field.CHUNK_CONTENT.value: {"type": "text"},
                    Field.CHUNK_TITLE.value: {"type": "text"},
                    Field.CHUNK_SOURCE.value: {"type": "keyword"},
                    # Field.DENSE_VECTOR.value: {"type": "dense_vector", "dims": 1024, "index": True, "similarity": "dot_product"},
                    Field.CREATE_TIME.value: {"type": "keyword"},
                    Field.UPDATE_TIME.value: {"type": "keyword"},
                    Field.SOURCE_INFO.value: {"type": "object", "enabled": False},
                    Field.EXTRA_INFO.value: {"type": "object", "enabled": False}
                }
            }
        }
    
    def check_if_index_exist(self, index_name: str):
        """检查索引是否存在"""
        return self.client.indices.exists(index=index_name)

    def create_index(self, index_name: str):
        """
        创建索引
        :param index_name: 索引名称 (对应于 Milvus 中的 collection_name)
        """
        index_exist = self.check_if_index_exist(index_name)
        if index_exist:
            self.logger.warning(f"索引 {index_name} 已存在，跳过创建")
            return {"status": True, "message": f"索引 {index_name} 已存在，跳过创建"}
        
        self.logger.info(f"create index: {index_name}")
        self.client.indices.create(index=index_name, body=self.knowlegde_qa_mapping)

        return {"status": True, "message": f"创建索引 {index_name} 成功"}
    
    def search_by_full_text(self, query_text: str, doc_ids: list, index_name: str, top_k: int = 10):
        """
        搜索文档
        :param query_text: 查询文本
        :param doc_ids: 文档ID列表
        :param index_name: 索引名称
        :param top_k: 返回结果数量
        :return: List[Document]
        """
        if not query_text:
            return []

        must_conditions = [{"terms": {Field.DOC_ID.value: doc_ids}}]
        if query_text:
            must_conditions.append({"match": {Field.CHUNK_CONTENT.value: query_text}})
            
        try:
            search_body = {
                "size": top_k,
                "query": {
                    "bool": {
                        "must": must_conditions
                    }
                }
            }
            
            results = self.client.search(index=index_name, body=search_body)
            
            docs = []
            for hit in results['hits']['hits']:
                source = hit['_source']
                doc = Document(
                    doc_id=source.get(Field.DOC_ID.value),
                    chunk_content=source.get(Field.CHUNK_CONTENT.value),
                    meta_data={
                        'chunk_id': source.get(Field.CHUNK_ID.value),
                        'score': hit['_score'],
                        'chunk_title': source.get(Field.CHUNK_TITLE.value),
                        'chunk_source': source.get(Field.CHUNK_SOURCE.value),
                        'source_info': source.get(Field.SOURCE_INFO.value),
                        'extra_info': source.get(Field.EXTRA_INFO.value)
                    },
                    chunk_provider='ES'
                )
                docs.append(doc)
            
            return docs
            
        except Exception as e:
            self.logger.error(f"ES搜索失败: {e}, {traceback.format_exc()}")
            return []
    
    def insert_documents(self, index_name: str, documents: dict):
        """
        插入文档
        :param index_name: 索引名称
        :param documents: 文档数据，格式与 Milvus 相同
        :return: dict
        """
        # 确保索引存在
        if not self.check_if_index_exist(index_name):
            self.create_index(index_name)
        
        if not isinstance(documents, dict):
            return {"status": False, "message": "参数 documents 必须为字典"}
        
        try:
            content_nums = len(documents[Field.CHUNK_CONTENT.value])
            actions = []
            existing_ids = []
            
            # 构建批量插入的文档
            for i in range(content_nums):
                unique_id = f"{documents[Field.DOC_ID.value]}_{documents[Field.CHUNK_ID.value][i]}"
                
                # 检查文档是否已存在
                if self.client.exists(index=index_name, id=unique_id):
                    existing_ids.append(unique_id)
                    continue
                
                # 构建单个文档
                doc = {
                    Field.CHUNK_ID.value: documents[Field.CHUNK_ID.value][i],
                    Field.DOC_ID.value: documents[Field.DOC_ID.value],
                    Field.CHUNK_CONTENT.value: documents[Field.CHUNK_CONTENT.value][i],
                    Field.CHUNK_TITLE.value: documents[Field.CHUNK_TITLE.value][i],
                    Field.CHUNK_SOURCE.value: documents[Field.CHUNK_SOURCE.value][i],
                    Field.CREATE_TIME.value: documents[Field.CREATE_TIME.value],
                    Field.UPDATE_TIME.value: documents[Field.UPDATE_TIME.value],
                    Field.SOURCE_INFO.value: documents[Field.SOURCE_INFO.value][i],
                    Field.EXTRA_INFO.value: documents[Field.EXTRA_INFO.value][i]
                }
                
                action = {
                    "_index": index_name,
                    "_id": unique_id,
                    "_source": doc
                }
                actions.append(action)
            
            self.logger.info(f"已经存在的content_id: {existing_ids}, 待插入的文档数量: {len(actions)}")
            if not actions:
                return {"status": True, "message": "当前提供的文档已经存在于ES中，无需插入"}
            
            # 批量插入
            successes, error = bulk(self.client, actions)
            message = f"待插入数据{len(actions)}, 成功插入{successes}, 已存在{len(existing_ids)}条"
            self.logger.info(message)
            return {"status": True, "message": message}
        
        except Exception as e:
            error_msg = f"插入文档失败: {e}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return {"status": False, "message": error_msg}
    
    def delete_by_doc_ids(self, index_name: str, doc_ids: list):
        """
        根据文档ID删除数据
        :param index_name: 索引名称
        :param doc_ids: 文档ID列表
        :return: dict
        """
        try:
            body = {
                "query": {
                    "terms": {
                        Field.DOC_ID.value: doc_ids
                    }
                }
            }
            
            response = self.client.delete_by_query(index=index_name, body=body, refresh=True)
            
            deleted_count = response.get('deleted', 0)
            message = f"成功删除 {deleted_count} 条数据"
            self.logger.info(message)
            
            return {"status": True, "message": message}
            
        except Exception as e:
            error_msg = f"删除文档失败: {e}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return {"status": False, "message": error_msg}
    
    def delete_by_chunk_ids(self, index_name: str, doc_id: str, chunk_ids: list):
        """
        根据chunk_id删除数据
        :param index_name: 索引名称
        :param doc_id: 文档ID
        :param chunk_ids: chunk ID列表
        :return: dict
        """
        if not chunk_ids:
            return {"status": False, "message": "chunk_ids 为空，无需删除"}

        try:
            body = {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {Field.DOC_ID.value: doc_id}},
                            {"terms": {Field.CHUNK_ID.value: chunk_ids}}
                        ]
                    }
                }
            }
            
            response = self.client.delete_by_query(index=index_name, body=body, refresh=True)
            
            deleted_count = response.get('deleted', 0)
            message = f"需要删除文档 {doc_id} 下的分片数量 {len(chunk_ids)} 个, 成功删除 {deleted_count} 个分片"
            self.logger.info(message)
            
            return {"status": True, "message": message}
            
        except Exception as e:
            error_msg = f"删除chunk失败: {e}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return {"status": False, "message": error_msg}

    def delete_index(self, index_name: str):
        """
        删除索引
        :param index_name: 索引名称
        :return: dict
        """
        try:
            if not self.check_if_index_exist(index_name):
                return {"status": True, "message": f"索引 {index_name} 不存在，无需删除"}
            
            self.client.options(ignore_status=[400, 404]).indices.delete(index=index_name)
            message = f"成功删除索引 {index_name}"
            self.logger.info(message)
            return {"status": True, "message": message}
        except Exception as e:
            error_msg = f"删除索引失败: {e}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return {"status": False, "message": error_msg}
    

if __name__ == '__main__':
    import time
    
    es_client = ElasticSearchClient()
    test_index = "unitest_knowledge_qa"
    
    # 1. 基础索引操作测试
    print("\n=== 测试索引操作 ===")
    delete_result = es_client.delete_index(test_index)
    print("删除索引结果:", delete_result)
    create_result = es_client.create_index(test_index)
    print("创建索引结果:", create_result)
    # 测试重复创建
    duplicate_create = es_client.create_index(test_index)
    print("重复创建索引结果:", duplicate_create)
    
    # 2. 准备测试文档
    test_docs = {
        Field.CHUNK_ID.value: ["chunk_1", "chunk_2"],
        Field.DOC_ID.value: "doc_1",
        Field.CHUNK_CONTENT.value: [
            "这是第一个测试文档的内容",
            "这是第二个测试文档的内容"
        ],
        Field.CHUNK_TITLE.value: ["测试文档1", "测试文档2"],
        Field.CHUNK_SOURCE.value: ["test", "test"],
        Field.CREATE_TIME.value: "2024-01-01 01:00:00",
        Field.UPDATE_TIME.value: "2024-01-01 01:00:00",
        Field.SOURCE_INFO.value: [
            {"author": "测试作者1"},
            {"author": "测试作者2"}
        ],
        Field.EXTRA_INFO.value: [
            {"category": "测试分类1"},
            {"category": "测试分类2"}
        ]
    }

    # 3. 文档操作测试
    print("\n=== 测试文档插入 ===")
    # 首次插入
    insert_result = es_client.insert_documents(test_index, test_docs)
    print("首次插入文档结果:", insert_result)
    # 测试重复插入
    duplicate_insert = es_client.insert_documents(test_index, test_docs)
    print("重复插入文档结果:", duplicate_insert)
    
    time.sleep(1)
    
    # 4. 搜索测试
    print("\n=== 测试搜索功能 ===")
    # 正常搜索
    search_result = es_client.search_by_full_text("测试文档", ["doc_1"], test_index, top_k=2)
    print("正常搜索结果:")
    for doc in search_result:
        print(f"文档ID: {doc.doc_id}")
        print(f"分片内容: {doc.chunk_content}")
        print(f"元数据: {doc.meta_data}\n")
    
    # 空查询文本搜索
    empty_search = es_client.search_by_full_text("", ["doc_1"], test_index, top_k=2)
    print("空查询文本搜索结果数量:", len(empty_search))
    
    # 搜索不存在的文档
    non_exist_search = es_client.search_by_full_text("测试文档", ["non_exist_doc"], test_index, top_k=2)
    print("搜索不存在文档结果数量:", len(non_exist_search))
    
    # 5. 删除测试
    print("\n=== 测试删除功能 ===")
    # 删除存在的chunk
    chunk_delete_result = es_client.delete_by_chunk_ids(test_index, "doc_1", ["chunk_1"])
    print("删除存在的chunk结果:", chunk_delete_result)
    
    # 删除不存在的chunk
    non_exist_chunk_delete = es_client.delete_by_chunk_ids(test_index, "doc_1", ["non_exist_chunk"])
    print("删除不存在的chunk结果:", non_exist_chunk_delete)
    
    # 删除存在的doc
    doc_delete_result = es_client.delete_by_doc_ids(test_index, ["doc_1"])
    print("删除存在的doc结果:", doc_delete_result)
    
    # 删除不存在的doc
    non_exist_doc_delete = es_client.delete_by_doc_ids(test_index, ["non_exist_doc"])
    print("删除不存在的doc结果:", non_exist_doc_delete)
    
    # 6. 清理测试
    print("\n=== 清理测试 ===")
    # 删除已存在的索引
    final_delete = es_client.delete_index(test_index)
    print("最终删除索引结果:", final_delete)
    # 删除不存在的索引
    non_exist_index_delete = es_client.delete_index("non_exist_index")
    print("删除不存在索引结果:", non_exist_index_delete)

import json
import traceback
import config

from pymilvus import (
    connections, 
    FieldSchema, 
    CollectionSchema, 
    DataType, 
    Collection, 
    utility,
    AnnSearchRequest,
    WeightedRanker
)

import logging
from concurrent.futures import ThreadPoolExecutor
from enum import Enum
import time

import config as conf
from connector.retrieval_document import RetrievalDocument as Document
from connector.field import RetrievalField as Field


class MilvusClient:
    def __init__(self,):
        self.default_collection = config.DEFAULT_INDEX_NAME
        self.sees = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.dense_index = {"metric_type": "IP", "index_type": "IVF_FLAT", "params": {"nlist": 1024}}
        self.sparse_index = {"metric_type": "IP", "index_type": "SPARSE_INVERTED_INDEX"}
        self.dense_search_params = {"metric_type": "IP", "params": {"nprobe": 256}}
        self.sparse_search_params = {"metric_type": "IP", "params": {"drop_ratio_search": 0.2}}
        self._init_connection()
        self.logger = logging.getLogger(__name__)
    
    def _init_connection(self, timeout=3):
        try:
            connections.connect(uri=config.MILVUS_URI, timeout=timeout)
        except Exception as e:
            self.logger.error(f"Failed to connect to Milvus: {e}")
            return None
    
    def _load_collection(self, collection_name: str, partition_id: str):
        """
        加载 collection
        :param collection_name: collection 名称
        :param partition_id: 分区 id
        """
        try:
            if utility.has_collection(collection_name):
                collection = Collection(collection_name)
                # 检查分区是否存在
                if not collection.has_partition(str(partition_id)):
                    collection.create_partition(str(partition_id))
            else:
                collection_schema = CollectionSchema(self.fields, description="qa_book_collection")
                collection = Collection(name=collection_name, schema=collection_schema)
                collection.create_partition(str(partition_id))
                collection.create_index(
                    field_name=Field.DENSE_VECTOR.value, 
                    index_params=self.dense_index
                )
                collection.create_index(
                    field_name=Field.SPARSE_VECTOR.value, 
                    index_params=self.sparse_index
                )
            
            # 加载集合和分区
            collection.load()
            
            return collection
        
        except Exception as e:
            self.logger.error(f"加载集合失败: {e}")
            return None
    
    def _ensure_collection(self, collection_name: str, partition_id: str):
        """确保 self.sees 绑定的是当前 collection"""
        if collection_name is None:
            collection_name = self.default_collection

        if self.sees is None or self.sees.name != collection_name:
            self.sees = self._load_collection(collection_name, str(partition_id))
    
    @property
    def fields(self):
        fields = [
            FieldSchema(name="pk", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name=Field.CHUNK_ID.value, dtype=DataType.VARCHAR, max_length=64),
            FieldSchema(name=Field.DOC_ID.value, dtype=DataType.VARCHAR, max_length=64),
            FieldSchema(name=Field.CHUNK_CONTENT.value, dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name=Field.CHUNK_TITLE.value, dtype=DataType.VARCHAR, max_length=3072),
            FieldSchema(name=Field.CHUNK_SOURCE.value, dtype=DataType.VARCHAR, max_length=64),
            FieldSchema(name=Field.DENSE_VECTOR.value, dtype=DataType.FLOAT_VECTOR, dim=1024),
            FieldSchema(name=Field.SPARSE_VECTOR.value, dtype=DataType.SPARSE_FLOAT_VECTOR),
            FieldSchema(name=Field.CREATE_TIME.value, dtype=DataType.VARCHAR, max_length=64),
            FieldSchema(name=Field.UPDATE_TIME.value, dtype=DataType.VARCHAR, max_length=64),
            FieldSchema(name=Field.SOURCE_INFO.value, dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name=Field.EXTRA_INFO.value, dtype=DataType.VARCHAR, max_length=2000)
        ]
        return fields
    
    @property
    def output_fields(self):
        return [field.value for field in [
            Field.CHUNK_ID,
            Field.DOC_ID,
            Field.CHUNK_CONTENT,
            Field.CHUNK_TITLE,
            Field.CHUNK_SOURCE,
            Field.CREATE_TIME,
            Field.UPDATE_TIME,
            Field.SOURCE_INFO,
            Field.EXTRA_INFO
        ]]

    def dense_search(self, dense_emb, expr, top_k, partition_names):
        """稠密向量检索"""
        dense_results = self.sees.search(
                    data=dense_emb, 
                    expr=expr, 
                    anns_field=Field.DENSE_VECTOR.value, 
                    param=self.dense_search_params,
                    output_fields=self.output_fields,
                    limit=top_k,
                    partition_names=partition_names
                    )
        
        dense_docs = self.format_search_result(dense_results)
        return dense_docs

    def sparse_search(self, spare_emb, expr, top_k, partition_names):
        """稀疏向量检索"""
        sparse_results = self.sees.search(
                    data=spare_emb, 
                    expr=expr, 
                    anns_field=Field.SPARSE_VECTOR.value,
                    param=self.sparse_search_params,
                    output_fields=self.output_fields,
                    limit=top_k,
                    partition_names=partition_names
                    )
        
        spare_docs = self.format_search_result(sparse_results)
        return spare_docs
    
    def hybrid_search(self, dense_emb, sparse_emb, dense_weight, sparse_weight, expr, top_k, partition_names):
        """混合检索"""
        dense_req = AnnSearchRequest(
            data=dense_emb,
            anns_field=Field.DENSE_VECTOR.value,
            param=self.dense_search_params,
            limit=top_k,
            expr=expr
        )
        
        sparse_req = AnnSearchRequest(
            data=sparse_emb,
            anns_field=Field.SPARSE_VECTOR.value,
            param=self.sparse_search_params,
            limit=top_k,
            expr=expr
        )
        
        rerank = WeightedRanker(sparse_weight, dense_weight)
        
        hybird_results = self.sees.hybrid_search(
            [sparse_req, dense_req],
            rerank=rerank,
            limit=top_k,
            output_fields=self.output_fields,
            partition_names=partition_names
        )
        
        hybrid_docs = self.format_search_result(hybird_results)
        
        return hybrid_docs

    def format_search_result(self, search_results) -> Document:
        if not search_results:
            return None

        docs = []
        for hits in search_results:
            for hit in hits:
                doc = Document(
                    doc_id=hit.entity.get(Field.DOC_ID.value),
                    chunk_content=hit.entity.get(Field.CHUNK_CONTENT.value),
                    meta_data={
                        'chunk_id': hit.entity.get(Field.CHUNK_ID.value),
                        'score': hit.score,
                        'chunk_title': hit.entity.get(Field.CHUNK_TITLE.value),
                        'chunk_source': hit.entity.get(Field.CHUNK_SOURCE.value),
                        'source_info': hit.entity.get(Field.SOURCE_INFO.value),
                        'extra_info': hit.entity.get(Field.EXTRA_INFO.value)
                    },
                    chunk_provider='Milvus'
                )
                docs.append(doc)
        
        return docs

    def __search_emb_sync(self, dense_emb, spare_emb, expr, partition_ids, dense_weight, sparse_weight, 
                          top_k, timeout, enable_hybrid_search):
        """
        执行搜索任务
        :param partition_ids: 可以是单个分区ID或分区ID列表
        """
        if isinstance(partition_ids, (list, tuple)) and partition_ids:
            partition_names = [str(pid) for pid in partition_ids]
        else:
            partition_names = [str(partition_ids)]
        
        search_docs = []
        try:
            if not enable_hybrid_search:
                dense_docs = self.dense_search(dense_emb, expr, top_k, partition_names)
                sparse_docs = self.sparse_search(spare_emb, expr, top_k, partition_names)

                search_docs = dense_docs + sparse_docs
            else:
                # 混合搜索
                hybrid_docs = self.hybrid_search(dense_emb, 
                                                 spare_emb, 
                                                 dense_weight, 
                                                 sparse_weight, 
                                                 expr, 
                                                 top_k, 
                                                 partition_names,
                                                 )
                search_docs = hybrid_docs
            
            return search_docs
            
        except Exception as e:
            self.logger.error(f"{expr}, milvus搜索失败: {e}")
            self.logger.debug(traceback.format_exc())
            return search_docs

    def search_emb_async(self,
                         partition_ids,
                         dense_emb, 
                         sparse_emb, 
                         expr,
                         collection_name=None,
                         dense_weight=1.0, 
                         sparse_weight=0.6, 
                         top_k=10,
                         timeout=5,
                         enable_hybrid_search=True):
        """
        异步执行搜索任务
        :param collection_name: 集合名称
        :param partition_ids: 分区ID列表
        :param dense_emb: 密集向量
        :param spare_emb: 稀疏向量
        :param expr: 搜索条件
        :param dense_weight: 密集向量权重
        :param sparse_weight: 稀疏向量权重
        :param top_k: 结果数量
        :param timeout: 超时时间
        :param enable_hybrid_search: 是否启用混合搜索
        :return: 搜索结果
        """
        # 确保集合已加载
        # 如果是多个分区，只需要确保集合存在，并加载第一个分区即可
        if isinstance(partition_ids, (list, tuple)) and partition_ids:
            partition_id = partition_ids[0]
        else:
            partition_id = partition_ids
        self._ensure_collection(collection_name, partition_id)
        
        # 将search_emb_sync函数放入线程池中运行
        future = self.executor.submit(
            self.__search_emb_sync, 
            dense_emb, 
            sparse_emb, 
            expr, 
            partition_ids,
            dense_weight,
            sparse_weight, 
            top_k,
            timeout, 
            enable_hybrid_search
        )

        return future.result()
    
    def search_by_chunk_id(self, partition_id: str, doc_id: str, chunk_id: str, collection_name: str = None) -> list:
        """
        通过分片ID搜索所有相关文档
        :param collection_name: 集合名称
        :param partition_id: 分区 id
        :param chunk_id: 分片 id
        :return: 文档列表
        """
        self._ensure_collection(collection_name, partition_id)
        
        try:
            # 构建查询表达式
            expr = f'doc_id == "{str(doc_id)}" AND chunk_id == "{str(chunk_id)}"'
            
            # 执行查询
            results = self.sees.query(
                expr=expr,
                output_fields=self.output_fields,
                partition_names=[str(partition_id)]
            )
            
            if not results:
                self.logger.warning(f"未找到相关分片: doc_id={doc_id}, chunk_id={chunk_id}")
                return []
            
            # 将查询结果转换为 Document 对象
            docs = []
            for result in results:
                doc = Document(
                    doc_id=result.get(Field.DOC_ID.value),
                    chunk_content=result.get(Field.CHUNK_CONTENT.value),
                    meta_data={
                        'chunk_id': result.get(Field.CHUNK_ID.value),
                        'chunk_title': result.get(Field.CHUNK_TITLE.value),
                        'create_time': result.get(Field.CREATE_TIME.value),
                        'chunk_source': result.get(Field.CHUNK_SOURCE.value),
                        'source_info': result.get(Field.SOURCE_INFO.value),
                        'extra_info': result.get(Field.EXTRA_INFO.value)
                    },
                    chunk_provider='Milvus'
                )
                docs.append(doc)
            
            return docs
            
        except Exception as e:
            self.logger.error(f"查询分片失败: doc_id: {doc_id}, chunk_id={chunk_id}, error={str(e)}")
            self.logger.debug(traceback.format_exc())
            return []

    def search_by_doc_id(self, partition_id: str, doc_id: str, collection_name: str = None) -> list:
        """
        通过文档ID搜索所有相关分片
        :param collection_name: 集合名称
        :param partition_id: 分区 id
        :param doc_id: 文档 id
        :return: 文档的所有分片列表
        """
        self._ensure_collection(collection_name, partition_id)
        
        try:
            # 构建查询表达式
            expr = f'doc_id == "{str(doc_id)}"'
            
            # 执行查询
            results = self.sees.query(
                expr=expr,
                output_fields=self.output_fields,
                partition_names=[str(partition_id)]
            )
            
            if not results:
                self.logger.warning(f"未找到文档: doc_id={doc_id}")
                return []
            
            # 将查询结果转换为 Document 对象
            docs = []
            for result in results:
                doc = Document(
                    doc_id=result.get(Field.DOC_ID.value),
                    chunk_content=result.get(Field.CHUNK_CONTENT.value),
                    meta_data={
                        'chunk_id': result.get(Field.CHUNK_ID.value),
                        'chunk_title': result.get(Field.CHUNK_TITLE.value),
                        'chunk_source': result.get(Field.CHUNK_SOURCE.value),
                        'source_info': result.get(Field.SOURCE_INFO.value),
                        'extra_info': result.get(Field.EXTRA_INFO.value)
                    },
                    chunk_provider='Milvus'
                )
                docs.append(doc)
            
            return docs
            
        except Exception as e:
            self.logger.error(f"查询文档失败: doc_id={doc_id}, error={str(e)}")
            self.logger.debug(traceback.format_exc())
            return []

    def insert_documents(self, partition_id: str, documents: dict, collection_name: str = None):
        """
        插入数据
        :param collection_name: 集合名称
        :param partition_id: 分区 id
        :param documents: 字典，存储了 向量/文本 等信息
        :return: dict, {'status': bool, 'message': str, 'data': dict}
        """
        self._ensure_collection(collection_name, partition_id)
        partition_id = str(partition_id)
        
        try:
            # 删除已存在的chunk_ids
            chunk_ids = documents[Field.CHUNK_ID.value]
            doc_id = documents[Field.DOC_ID.value]
            if chunk_ids:
                self.delete_by_chunk_ids(partition_id, doc_id, chunk_ids, collection_name=collection_name)
        except Exception as e:
            self.logger.warning(f"删除已存在的chunks时出错: {e}")
            self.logger.debug(f"删除已存在的chunks时出错, {traceback.format_exc()}")
        
        try:
            content_nums = len(documents[Field.CHUNK_CONTENT.value])
            entities = [
                documents[Field.CHUNK_ID.value],
                [documents[Field.DOC_ID.value]] * content_nums,
                documents[Field.CHUNK_CONTENT.value],
                documents[Field.CHUNK_TITLE.value],
                documents[Field.CHUNK_SOURCE.value],
                documents[Field.DENSE_VECTOR.value],
                documents[Field.SPARSE_VECTOR.value],
                [documents[Field.CREATE_TIME.value]] * content_nums,
                [documents[Field.UPDATE_TIME.value]] * content_nums,
                documents[Field.SOURCE_INFO.value],
                documents[Field.EXTRA_INFO.value]
            ]
            
            assert len(entities[0]) == content_nums, "实体数量与内容数量不匹配"
            
            # 批量插入
            insert_num = 0
            for i in range(0, content_nums, 2000):
                start = int(time.time() * 1000)
                tmp_entities = [entity[i:i + 2000] for entity in entities]
                
                if tmp_entities[0]:  # 如果有数据要插入
                    tmp_cnt = len(tmp_entities[0])
                    self.sees.insert(tmp_entities, partition_name=partition_id, field_names=self.output_fields)
                    
                    end = int(time.time() * 1000)
                    insert_num += tmp_cnt
                    self.logger.info(f"{collection_name} 写入 Milvus 进度：{insert_num} / {content_nums}, "
                        f"写入 {tmp_cnt} 条数据耗时：{end - start} ms")
            
            self.sees.flush()
            message = f"成功插入 {content_nums} 条数据到 {collection_name} 的 {partition_id} 分区"
            self.logger.info(message)
            return {"status": True, "message": message}
            
        except Exception as e:
            error_msg = f"插入数据失败: {str(e)}"
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
            return {"status": False, "message": error_msg}
    
    def delete_by_doc_ids(self, partition_id: str, doc_ids: list, collection_name: str = None):
        """
        删除数据
        :param collection_name: 集合名称
        :param partition_id: 分区 id
        :param doc_ids: 文档 id 列表
        """
        self._ensure_collection(collection_name, partition_id)
        
        try:
            # 确保 doc_ids 中的每个 id 都被引号包裹
            doc_ids_str = ', '.join([f'"{str(doc_id)}"' for doc_id in doc_ids])
            expr = f"doc_id in [{doc_ids_str}]"
            
            # 先查询要删除的数据是否存在
            res = self.sees.query(
                expr=expr,
                output_fields=[
                    Field.DOC_ID.value, 
                    Field.CHUNK_ID.value
                ],
                partition_names=[str(partition_id)]
            )
            
            if not res:
                self.logger.warning(f"未找到要删除的 doc_ids: {doc_ids}")
                return
            
            # 执行删除操作
            self.sees.delete(
                expr=expr,
                partition_names=[str(partition_id)]
            )
            
            # 确保删除操作生效
            self.sees.flush()
            
            chunk_count = len(res)
            affected_docs = len(set(r['doc_id'] for r in res))
            message = f"成功删除 {chunk_count} 个分片，涉及 {affected_docs} 个文档，doc_ids={doc_ids}"
            self.logger.info(message)
            
            return {"status": True, "message": message}
            
        except Exception as e:
            error_msg = f"删除 doc_ids={doc_ids} 失败: {str(e)}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            return {"status": False, "message": error_msg}

    def delete_by_chunk_ids(self, partition_id: str, doc_id: str, chunk_ids: list, collection_name: str = None):
        """
        删除指定文档下的特定 chunks
        :param collection_name: 集合名称
        :param partition_id: 分区 id
        :param doc_id: 文档 id
        :param chunk_ids: chunk id 列表
        """
        self._ensure_collection(collection_name, partition_id)
        
        try:
            # 构建查询表达式，同时匹配 doc_id 和 chunk_ids
            chunk_ids_str = ', '.join([f'"{str(chunk_id)}"' for chunk_id in chunk_ids])
            expr = f'doc_id == "{str(doc_id)}" && chunk_id in [{chunk_ids_str}]'
            
            # 先查询要删除的数据是否存在
            res = self.sees.query(
                expr=expr,
                output_fields=[
                    Field.DOC_ID.value, 
                    Field.CHUNK_ID.value
                ],
                partition_names=[str(partition_id)]
            )
            
            if not res:
                self.logger.warning(f"未找到要删除的数据，doc_id={doc_id}, chunk_ids={chunk_ids}")
                return 
            
            # 执行删除操作
            self.sees.delete(
                expr=expr,
                partition_names=[str(partition_id)]
            )
            
            # 确保删除操作生效
            self.sees.flush()
            
            found_chunks = [r['chunk_id'] for r in res]
            self.logger.info(f"成功删除文档 {doc_id} 下的 {len(res)} 个分片: {found_chunks}")
            
            # 如果有未找到的 chunk_ids，记录警告
            not_found_chunks = set(chunk_ids) - set(found_chunks)
            if not_found_chunks:
                self.logger.warning(f"以下 chunk_ids 未在文档 {doc_id} 中找到: {list(not_found_chunks)}")
                
        except Exception as e:
            self.logger.error(f"删除文档 {doc_id} 的 chunk_ids {chunk_ids} 失败: {e}")
            self.logger.debug(traceback.format_exc())


if __name__ == '__main__':
    from datetime import datetime
    import numpy as np
    
    milvus_client = MilvusClient()
    collection_name = "test_knowledge_qa"
    partition_id = "test_partition"

    # 测试用例1：基本插入功能
    documents_basic = {
        'chunk_id': ['test_chunk_1', 'test_chunk_2'],
        'doc_id': 'test_doc_1',
        'chunk_content': ['这是测试内容1。', '这是测试内容2。'],
        'chunk_title': ['测试标题1', '测试标题2'],
        'chunk_source': ['RAW', 'RAW'],
        'dense_vector': [[0.1] * 1024, [0.2] * 1024],
        'sparse_vector': [{1: 0.5, 2: 0.5}, {3: 0.5, 4: 0.5}],
        'create_time': "2025-01-01 00:00:00",
        'update_time': "2025-01-02 13:00:00",
        'source_info': ['{"page": 1}', '{"page": 2}'],
        'extra_info': ['{"test": "info1"}', '{"test": "info2"}']
    }
    
    print("\n测试1：基本插入功能")
    try:
        milvus_client.insert_documents(partition_id, documents_basic, collection_name)
        print("基本插入测试成功")
    except Exception as e:
        print(f"基本插入测试失败: {e}")

    # # 测试用例2：重复插入（测试删除已存在数据功能）
    # print("\n测试2：重复插入相同chunk_id的数据")
    # documents_duplicate = {
    #     'chunk_id': ['test_chunk_1'],
    #     'doc_id': 'test_doc_1',
    #     'chunk_content': ['这是更新后的内容。'],
    #     'chunk_title': ['更新后的标题'],
    #     'chunk_source': ['LLM_GEN'],
    #     'dense_vector': [[0.3] * 1024],
    #     'sparse_vector': [{5: 0.5, 6: 0.5}],
    #     'create_time': "2024-01-01 01:00:00",
    #     'update_time': "2025-01-02 14:00:00",
    #     'source_info': ['{"page": 1, "updated": true}'],
    #     'extra_info': ['{"test": "updated"}']
    # }
    # try:
    #     milvus_client.insert_documents(partition_id, documents_duplicate, collection_name)
    #     print("重复插入测试成功")
    # except Exception as e:
    #     print(f"重复插入测试失败: {e}")

    # 测试用例3：搜索功能
    print("\n测试3：基于 向量搜索")
    dense_vector = [0.1] * 1024
    sparse_vector = {1: 0.5, 2: 0.5}
    
    try:
        search_results = milvus_client.search_emb_async(
            collection_name=collection_name,
            partition_ids=partition_id,
            dense_emb=[dense_vector],
            sparse_emb=[sparse_vector],
            expr='doc_id == "test_doc_1"',
            top_k=5,
            enable_hybrid_search=True
        )
        print("搜索测试成功，结果：")
        print(search_results)
    except Exception as e:
        print(f"搜索测试失败: {e}")

    # 测试用例3-1: 基于doc_id搜索
    print("\n测试3-1：基于doc_id搜索")
    try:
        search_results = milvus_client.search_by_doc_id(
            collection_name=collection_name,
            partition_id=partition_id,
            doc_id='test_doc_1'
        )
        print("基于doc_id搜索测试成功，结果：")
        print(search_results)
    except Exception as e:
        print(f"基于doc_id搜索测试失败: {e}")

    # 测试用例4：删除功能
    # print("\n测试4：删除功能")
    # try:
    #     # 按doc_id删除
    #     milvus_client.delete_by_doc_ids(collection_name, partition_id, ['test_doc_1'])
    #     print("按doc_id删除测试成功")
        
    # except Exception as e:
    #     print(f"删除测试失败: {e}")

    # try:
    #     # 按chunk_id删除
    #     milvus_client.delete_by_chunk_ids(
    #         collection_name, 
    #         partition_id, 
    #         'test_doc_1', 
    #         ['test_chunk_1', 'test_chunk_2']
    #     )
    #     print("按chunk_id删除测试成功")
    # except Exception as e:
    #     print(f"删除测试失败: {e}")

    # # 测试用例5：异常处理
    # print("\n测试5：异常处理测试")
    # invalid_documents = {
    #     'chunk_id': ['test_chunk_1'],
    #     'doc_id': 'test_doc_1',
    #     'chunk_content': ['测试内容'],
    #     # 缺少必要字段
    # }
    # try:
    #     milvus_client.insert_documents(partition_id, invalid_documents, collection_name)
    #     print("异常处理测试失败：应该抛出异常")
    # except Exception as e:
    #     print(f"异常处理测试成功：正确捕获了异常 - {e}")
        
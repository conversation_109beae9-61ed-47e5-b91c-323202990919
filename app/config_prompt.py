# 生成大纲的提示词模板
GENERATE_OUTLINE_PROMPT = """请提供一份按照以下要求生成文档大纲的格式举例：你的任务是根据给定的主题、文档关键字和参考资料生成一份文档大纲。请仔细阅读以下主题信息和文档关键字，并按照指示生成大纲。在生成大纲时，需在每个大纲标题中融入与主题相关的关键字以及文档关键字，以突出重点。

### 文档主题
{doc_topic}

### 文档关键字
{keywords_str}

### 参考资料
<context>
{Documents}
</context>

### 生成文档大纲指南
1. 大纲应全面覆盖与主题相关的主要方面。
2. 每个大纲级别应逻辑清晰，层次分明，且标题分级控制在三级以内。
3. 大纲标题应简洁明了，准确概括该部分的核心内容，且包含相关关键字和文档关键字。
4. 尽量提供至少三个主要部分，每个主要部分下可根据需要设置子部分。

### 输出格式示例
{{
 "primary": [
 {{
 "title": "一级标题示例",
 "secondary": [
 {{
 "title": "1.1 二级标题示例",
 "tertiary": [
 {{
 "title": "1.2 三级标题示例"
 }}
 ]
 }}
 ]
 }}
 ]
}}

请使用json格式写下你的文档大纲, 一级标题key为"primary"，二级标题key为"secondary"，三级标题key为"tertiary"。"""

# 生成文章的提示词模板
GENERATE_ARTICLE_PROMPT = """文档主题:
<document_topic>
{doc_topic}
</document_topic>

大纲内容:
<outline>
{outline_str}
</outline>

### 参考资料
<context>
{Documents}
</context>

长度限制:
<length>
{length}
</length>


在生成文档时，请遵循以下指南:
1. 确保文档内容与大纲紧密相关，涵盖大纲中的所有要点。
2. 根据文档主题，采用合适的语言风格和格式。例如，如果是学术文档，语言应严谨准确；如果是宣传文案，语言可更生动活泼。
3. 使文档结构清晰，逻辑连贯，过渡自然。
4. 对大纲中的要点进行适当的展开和阐述，提供足够的细节和解释，但避免冗长和啰嗦。
5. 检查文档是否存在语法错误、拼写错误和表达歧义。
6: 文档字数应控制在长度限制以内。
7: 文档内容应符合中文排版规范，按重点段落内容量要多；
8: 注意除标题外小标题要加序号；
9: 可以根据参考资料中的内容进行补充和扩展，如果参考资料中没有相关内容，可以根据自己的理解进行补充；

请以markdown写下生成的文档。"""


# 文档总结、关键词、关键句、关键数据的提示词模板
SUMMARY_PROMPT = """请对以下文档进行全面分析和总结，输出以下内容：
1. 总结（summary）：用简洁的语言概括文档的主要内容和核心观点
2. 关键词（keywords）：提取5-10个最能代表文档主题的关键词
3. 关键句（keysentences）：提取3-5个最能代表文档核心内容的关键句
4. 关键数据（keydata）：提取文档中出现的重要数据点及其对应的值

请以JSON格式返回结果，格式如下：
{{
  "summary": "文档总结内容...",
  "keywords": ["关键词1", "关键词2", ...],
  "keysentences": ["关键句1", "关键句2", ...],
  "keydata": {{"数据项1": "值1", "数据项2": "值2", ...}}
}}

文档内容：
<doc>
{Documents}
</doc>
"""

# 生成文章的提示词模板
GENERATE_ARTICLE_WITH_TEMPLATE_PROMPT = """文章主题:
<document_topic>
{doc_topic}
</document_topic>

文章关键字:
<keywords>
{keywords_str}
</keywords>

文章写作模板:
<template>
{template_content}
</template>

文章模板类型：
<template_type>
{template_type}
</template_type>

文章长度限制:
<length>
{length}
</length>


### 参考资料
<context>
{Documents}
</context>


在生成文档时，请遵循以下指南:
1. 确保文章内容与主题紧密相关，涵盖所给关键词，注意一定要符合所给模板风格和类型。
2. 根据文章主题，采用合适的语言风格和格式。例如，如果是学术文档，语言应严谨准确；如果是宣传文案，语言可更生动活泼。
3. 使文章结构清晰，逻辑连贯，过渡自然。
4. 对文章中的要点进行适当的展开和阐述，提供足够的细节和解释，但避免冗长和啰嗦。
5. 检查文章是否存在语法错误、拼写错误和表达歧义。
6: 文章字数应控制在长度限制以内。
7: 文章内容应符合中文排版规范，按重点段落内容量要多；
8: 注意除标题外小标题要加序号；
9: 可以根据参考资料中的内容进行补充和扩展，如果参考资料中没有相关内容，可以根据自己的理解进行补充；

请以markdown写下生成的文档。"""

# 长文章章节生成的提示词模板
GENERATE_LONG_ARTICLE_SECTION_PROMPT = """你是一位专业的文章写作助手，正在协助生成一篇长文章的特定章节。

文章主题: {doc_topic}
章节标题: {section_title}
章节大纲: {section_outline}
预期字数: {expected_length}

参考资料:
<context>
{documents}
</context>

请根据以下要求生成该章节的完整内容：

1. **内容要求**：
   - 内容要详实充分，符合预期字数要求（{expected_length}字左右）
   - 语言要专业准确，逻辑清晰，表达流畅
   - 结构要完整，包含适当的小标题和段落划分
   - 内容要与主题和大纲高度相关，保持一致性

2. **格式要求**：
   - 使用markdown格式输出
   - 章节标题使用适当的标题级别（##或###）
   - 合理使用列表、表格等格式增强可读性
   - 确保内容的独立性和完整性

3. **质量要求**：
   - 避免空洞的表述，提供具体的信息和观点
   - 适当引用参考资料中的相关内容
   - 保持学术性和专业性
   - 确保逻辑连贯，论证充分

4. **特别注意**：
   - 这是长文章的一个章节，要考虑与其他章节的衔接
   - 内容要有深度，避免浅显的泛泛而谈
   - 如果是技术性内容，要提供足够的细节和例子

请生成该章节的完整内容："""


# 文本校验的提示词模板
TEXT_CHECK_PROMPT = """请对以下文本进行全面的错误检查，包括但不限于拼写错误、标点符号错误、语法错误等。

文本内容：
<text>
{text}
</text>

请按照以下格式返回检查结果：
- 对于每个发现的错误，提供一个包含三个元素的列表项：[原始内容片段, 错误类型, 纠正后的文本]
- 错误类型有：单词拼写错误，中文词语搭配错误，标点符号错误，句子语法错误，用词不当等
- 如果没有发现错误，请返回空列表
- 不要做多余的解释，只返回正确的格式化结果

请以JSON格式返回结果，格式如下：
[
  ["原始内容片段1", "错误类型1", "纠正后的文本1"],
  ["原始内容片段2", "错误类型2", "纠正后的文本2"],
  ...
]

注意：
1. 只标注确定的错误，不要过度纠正作者的表达风格
2. 保持原文的语气和风格
3. 对于混合文本，分别检查中英文部分
4. 校验片段的粒度以句子为单位
5. 对比检查校验前后的文本，确保纠错是合理的
"""
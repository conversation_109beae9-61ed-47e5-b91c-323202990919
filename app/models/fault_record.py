from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime
from . import Base
import datetime


class FaultRecords(Base):
    __tablename__ = 'fault_records'

    agent_id = Column(Integer, ForeignKey('agent.agent_id'), nullable=False, comment='关联的智能体ID')
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False, comment='上传的用户ID')
    fault_id = Column(Integer, primary_key=True, comment='故障记录主键ID')
    fault_type = Column(String(255), nullable=False, comment='故障类型')
    fault_reason = Column(Text, nullable=False, comment='故障原因')
    fault_content = Column(Text, nullable=False, comment='故障描述内容')
    fault_severity = Column(String(255), nullable=False, comment='故障严重程度')
    fault_time = Column(DateTime, comment='故障发生时间')
    create_time = Column(DateTime, default=datetime.datetime.utcnow, comment='故障记录创建时间')
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='故障记录更新时间')

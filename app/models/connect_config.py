from sqlalchemy import Column, Integer, String, DateTime, Text, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# 避免循环导入，直接创建 Base
Base = declarative_base()

class ConnectConfig(Base):
    """数据源配置模型"""
    __tablename__ = 'connect_config'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    db_type = Column(String(50), nullable=False, comment='数据库类型：mysql,postgresql,sqlite,oracle')
    db_name = Column(String(100), nullable=False, comment='数据库名称')
    db_path = Column(String(500), comment='数据库路径(SQLite使用)')
    db_host = Column(String(100), comment='数据库主机地址')
    db_port = Column(Integer, comment='数据库端口')
    db_user = Column(String(100), comment='数据库用户名')
    db_pwd = Column(String(500), comment='数据库密码(加密存储)')
    comment = Column(String(500), comment='备注说明')
    sys_code = Column(String(100), comment='系统编码')
    # 移除 user_name 字段
    user_id = Column(Integer, nullable=False, comment='创建用户ID')
    gmt_created = Column(DateTime, default=func.now(), comment='创建时间')
    gmt_modified = Column(DateTime, default=func.now(), onupdate=func.now(), comment='修改时间')
    ext_config = Column(Text, comment='扩展配置(JSON格式)')
    external_id = Column(String(100), comment='外部系统数据源ID')
    original_json = Column(Text, comment='原始JSON记录')
    
    # 创建索引
    __table_args__ = (
        Index('idx_user_id', 'user_id'),
        Index('idx_db_type', 'db_type'),
        Index('idx_sys_code', 'sys_code'),
        Index('idx_external_id', 'external_id', unique=True),
    )
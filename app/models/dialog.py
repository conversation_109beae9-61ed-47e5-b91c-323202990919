from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime, ForeignKey
from . import Base
import datetime

class Dialog(Base):
    __tablename__ = 'dialogs'
    dialog_id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
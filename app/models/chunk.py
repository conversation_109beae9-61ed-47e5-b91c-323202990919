from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime
from . import Base
import datetime

class Chunk(Base):
    __tablename__ = 'chunks'
    chunk_id = Column(Integer, primary_key=True)
    doc_id = Column(Integer, nullable=False)
    chunk_content = Column(Text, nullable=False)
    chunk_title = Column(Text)
    update_time = Column(DateTime, default=datetime.datetime.utcnow)
    dense_vector = Column(Text)
    sparse_vector = Column(Text)
from sqlalchemy import Column, Integer, DateTime, Text, String, JSON, ForeignKey
from . import Base
import datetime


from sqlalchemy import Column, Integer, DateTime, Text, String, JSON, ForeignKey
from sqlalchemy.dialects.mysql import LONGTEXT # 如果你用MySQL且Text可能非常大
from . import Base # 假设 Base 是从您的 . __init__.py 或类似文件中导入的
import datetime

class Chatbi(Base):


    __tablename__ = 'chatbi'

    chatbi_id = Column(Integer, primary_key=True, autoincrement=True, comment='chatbi_app主键')
    app_code = Column(Text, nullable=False, comment='app_code 前后一致, 通常建议使用String并指定长度或UUID类型') # 考虑到示例是UUID，Text也可以，但如果固定格式，String或专用UUID类型更好
    app_describe = Column(Text, comment='app_describe描述')
    app_name = Column(String(255), nullable=False, comment='app_name名称')
    create_time = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, comment='chatbi_app创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='chatbi_app更新时间')
    #和后端比较，增加
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False, comment='关联的用户ID')
    app_icon_path = Column(String(500), comment='app图标链接，对应minio中的文件路径')
    # --- New
    team_mode = Column(String(100), nullable=True, comment='团队模式 (e.g., native_app)') # 根据实际可能的值调整长度和可空性
    language = Column(String(20), nullable=True, comment='语言 (e.g., zh, en)') # 根据实际可能的值调整长度和可空性
    team_context = Column(JSON, nullable=True, comment='团队上下文信息 (e.g., {"chat_scene": "chat_with_db_execute", "scene_name": "Chat Data"})')
    param_need = Column(JSON, nullable=True, comment='参数需求列表 (e.g., [{"type": "model", "value": "..."}])')
    recommend_questions = Column(JSON, nullable=True, comment='推荐问题列表 (e.g., [{"question": "...", "valid": true}])')
    extro_info = Column(JSON, comment='预留扩展字段')

    # def __repr__(self):
    #     return f"<Agent(chatbi_id={self.chatbi_id}, app_name='{self.app_name}', app_code='{self.app_code}')>"
    #
    # {
    #     "app_code": "493cc4a4-4e53-11f0-a3e3-0242ac120004",
    #     "app_describe": "612",
    #     "team_mode": "native_app",
    #     "app_name": "621",
    #     "language": "zh",
    #     "team_context": {
    #         "chat_scene": "chat_with_db_execute",
    #         "scene_name": "Chat Data"
    #     },
    #     "param_need": [
    #         {
    #             "type": "model",
    #             "value": "Qwen2.5-72B-Instruct-GPTQ-Int4"
    #         },
    #         {
    #             "type": "temperature",
    #             "value": 1
    #         },
    #         {
    #             "type": "max_new_tokens",
    #             "value": 3000
    #         },
    #         {
    #             "type": "resource",
    #             "value": "database",
    #             "bind_value": "chatbi"
    #         },
    #         {
    #             "type": "prompt_template"
    #         }
    #     ],
    #     "recommend_questions": [
    #         {
    #             "question": "近一周温度变化曲线",
    #             "valid": true
    #         },
    #         {
    #             "question": "近一个月温度变化曲线",
    #             "valid": true
    #         }
    #     ]
    # }
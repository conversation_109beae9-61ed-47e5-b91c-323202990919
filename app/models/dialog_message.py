from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime, JSON
from . import Base
import datetime

class DialogMessage(Base):
    __tablename__ = 'dialog_messages'
    message_id = Column(Integer, primary_key=True, autoincrement=True)
    dialog_id = Column(Integer, ForeignKey('dialogs.dialog_id'), nullable=False)
    role = Column(String(20), nullable=False)
    content = Column(Text, nullable=False)
    time = Column(DateTime, default=datetime.datetime.utcnow)
    refs = Column(Text)
    extra_info = Column(JSON)
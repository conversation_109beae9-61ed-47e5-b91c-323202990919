from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from config import DB_CONFIG
import logging
from passlib.context import CryptContext
from urllib.parse import quote_plus


engine_logger = logging.getLogger('sqlalchemy.engine')
engine_logger.setLevel(logging.CRITICAL)


Base = declarative_base()

# 创建异步数据库引擎
encoded_password = quote_plus(DB_CONFIG['password'])
async_engine = create_async_engine(
    f"mysql+aiomysql://{DB_CONFIG['user']}:{encoded_password}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['db']}",
    pool_size=10,  # 连接池大小
    max_overflow=20,  # 最大溢出连接数
    pool_timeout=30,  # 连接池超时时间（秒）
    pool_recycle=60,  # 连接回收时间（秒）
    echo=False  # 可设置为 False 以关闭 SQL 语句打印
)
async_session = sessionmaker(async_engine, expire_on_commit=False, class_=AsyncSession)


engine = create_engine(
    f"mysql+mysqlconnector://{DB_CONFIG['user']}:{encoded_password}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['db']}",
    echo=False  # 可设置为 False 以关闭 SQL 语句打印
)
Session = sessionmaker(bind=engine)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# from .document import Document
from .dialog import Dialog
from .dialog_message import DialogMessage
from .phrase import Phrase
from .chunk import Chunk
from .user import User
from .department import Department
from .article import Article
from .knowledge_base import KnowledgeBase
from .rag_document import RAGDocument
from .kb_share_relation import KBShareRelation
from .article_summary import ArticleSummary
from .dialog_message_token import DialogMessageToken
from .connect_config import ConnectConfig
from .article_template import ArticleTemplate
from .agent import Agent
from .chatbi import Chatbi


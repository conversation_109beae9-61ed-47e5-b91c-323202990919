# from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime, ForeignKey
# from . import Base
# import datetime


# class Document(Base):
#     __tablename__ = 'documents'
#     doc_id = Column(Integer, primary_key=True, autoincrement=True)
#     path = Column(String(255), nullable=False)
#     name = Column(String(255), nullable=False)
#     markdown = Column(Text, nullable=False)
#     permission_level = Column(String(20), nullable=False)
#     version = Column(String(20), nullable=False)
#     minio_config = Column(Text, nullable=False)
#     upload_time = Column(DateTime, default=datetime.datetime.utcnow)

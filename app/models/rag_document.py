from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON
from . import Base
import datetime

class RAGDocument(Base):
    __tablename__ = 'rag_documents'
    
    doc_id = Column(Integer, primary_key=True, autoincrement=True)
    doc_name = Column(String(255), nullable=False, comment='文档名称')
    doc_type = Column(String(50), nullable=False, comment='文档文件类型(PDF, Word, TXT 等)')
    doc_status = Column(String(20), nullable=False, comment='文档生命周期状态(草稿, 已发布, 已归档等)')
    latest_version = Column(Integer, default=1, comment='是否为最新版本 (1 是最新，0 是旧版本)')
    doc_version = Column(Integer, default=1, comment='文档版本号')
    parse_status = Column(Integer, default=0, comment='文档的解析状态')
    chunk_cnt = Column(Integer, default=0, comment='文档分块数量')
    doc_checksum = Column(String(64), comment='文档hash码')
    is_deleted = Column(Integer, default=0, comment='是否删除(0没有删除, 1是删除)')
    minio_doc_path = Column(String(255), nullable=False, comment='MinIO 对象存储路径')
    minio_preview_doc_path =  Column(String(255), nullable=False, comment='MinIO 预览对象存储路径')
    minio_bucket_name = Column(String(100), nullable=False, comment='MinIO 桶名称')
    kb_id = Column(Integer, ForeignKey('knowledge_bases.kb_id'), comment='知识库 ID')
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False, comment='创建人用户 ID')
    create_time = Column(DateTime, default=datetime.datetime.utcnow, comment='文档上传时间')
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='文档更新时间')
    extro_info = Column(JSON, comment='预留扩展字段')
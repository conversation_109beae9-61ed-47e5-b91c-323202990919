from sqlalchemy import Column, Integer, String, Text, DateTime, BigInteger, ForeignKey
from . import Base
import datetime

class Article(Base):
    __tablename__ = 'aigc_articles'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    doc_topic = Column(String(255), nullable=False)
    keywords = Column(String(255), server_default='')
    wcount = Column(Integer, server_default='0')
    outline = Column(Text)
    abstract = Column(Text)
    article_wcount = Column(Integer, server_default='0')
    file_size = Column(BigInteger, server_default='0')
    input_tokens = Column(Integer, server_default='0')
    output_tokens = Column(Integer, server_default='0')
    file_name = Column(String(255), nullable=True)
    file_type = Column(String(255), nullable=True)
    file_path = Column(String(255), nullable=True)
    io_bucket_name = Column(String(255), nullable=True)
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    is_deleted = Column(Integer, server_default='0')  # 添加这行
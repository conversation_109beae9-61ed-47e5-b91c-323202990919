from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.sql import func
from . import Base

class ArticleTemplate(Base):
    """文章模板表"""
    __tablename__ = 'article_template'

    template_id = Column(Integer, primary_key=True, autoincrement=True, comment='文章模板唯一ID')
    template_name = Column(String(255), nullable=True, comment='文章模板名称')
    template_content = Column(Text, nullable=True, comment='模板内容')
    template_type = Column(String(255), nullable=True, comment='模型类型，比如公文、简报等')
    permission_level = Column(String(50), default='employee', comment='访问权限：admin/employee/guest')
    is_manual = Column(Integer, default=1, comment='是否为手工编辑，1为手工编辑，0为自动生成')
    is_deleted = Column(Integer, default=0, comment='是否删除')
    user_id = Column(Integer, nullable=True, comment='创建人用户ID')
    input_tokens = Column(Integer, default=0, comment='输入tokens总数')
    output_tokens = Column(Integer, default=0, comment='输出tokens总数')
    check_sum = Column(String(255), nullable=True, comment='自动生成模板的校验码')
    create_time = Column(DateTime, default=func.current_timestamp(), comment='文档上传时间')
    update_time = Column(DateTime, default=func.current_timestamp(), 
                        onupdate=func.current_timestamp(), comment='文档更新时间')
    extro_info = Column(JSON, nullable=True, comment='预留扩展字段')

    def __repr__(self):
        return f"<ArticleTemplate(template_id={self.template_id}, template_type={self.template_type})>"
from sqlalchemy import Column, Integer, DateTime, Text, String, JSON, ForeignKey
from . import Base
import datetime


class Agent(Base):
    __tablename__ = 'agent'

    agent_id = Column(Integer, primary_key=True, autoincrement=True, comment='agent主键')
    agent_name = Column(String(255), nullable=False, comment='agent名称')
    agent_desc = Column(Text, comment='agent描述')
    create_time = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, comment='agent创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='agent更新时间')
    agent_code = Column(Text, nullable=False, comment='agent代码')
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    agent_icon_path = Column(String(500), comment='agent图标链接，对应minio中的文件路径')
    extro_info = Column(JSON, comment='预留扩展字段')
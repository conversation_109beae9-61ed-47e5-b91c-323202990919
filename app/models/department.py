from sqlalchemy import Column, Integer, String, DateTime, JSON, ForeignKey
from sqlalchemy.sql import func
from . import Base

class Department(Base):
    """部门表"""
    __tablename__ = 'departments'

    department_id = Column(Integer, primary_key=True, autoincrement=True, comment='部门ID')
    department_name = Column(String(100), nullable=False, unique=True, default='待分配', comment='部门名称')
    department_phone = Column(String(20), default='', comment='部门联系电话')
    department_email = Column(String(100), default='', comment='部门邮箱')
    employee_count = Column(Integer, default=0, comment='部门员工数量')
    creator_id = Column(Integer, ForeignKey('users.user_id', ondelete='RESTRICT', onupdate='CASCADE'), 
                       nullable=True, comment='部门创建人ID')
    create_time = Column(DateTime, default=func.current_timestamp(), comment='创建时间')
    update_time = Column(DateTime, default=func.current_timestamp(), 
                        onupdate=func.current_timestamp(), comment='更新时间')
    extro_info = Column(JSON, nullable=True, comment='预留扩展字段')

    def __repr__(self):
        return f"<Department(department_name='{self.department_name}')>"
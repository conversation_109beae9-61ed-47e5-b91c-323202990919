from sqlalchemy import Column, Integer, String, DateTime, Enum, JSON
from . import Base
import datetime

class User(Base):
    __tablename__ = 'users'
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    last_login = Column(DateTime)
    permission_level = Column(Enum('admin', 'employee', 'guest'), default='employee')
    # 新增字段
    department = Column(String(100))
    group = Column(String(50), default='employee')
    telphone = Column(String(20))
    id_num = Column(String(50))
    nickname = Column(String(100))
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    attr_01 = Column(String(255))
    attr_02 = Column(String(255))
    extro_info = Column(JSON)
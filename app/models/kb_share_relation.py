from sqlalchemy import Column, Integer, String, DateTime, JSON, ForeignKey
from . import Base
import datetime

class KBShareRelation(Base):
    __tablename__ = 'kb_share_relations'
    
    kb_id = Column(Integer, primary_key=True)
    from_user_id = Column(Integer, primary_key=True)
    to_user_id = Column(Integer, primary_key=True)
    to_department = Column(String(255), comment='分享部门')  # 修改字段名
    attr_02 = Column(String(255))
    extro_info = Column(JSON)
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    def __repr__(self):
        return f"<KBShareRelation(kb_id={self.kb_id}, from_user_id={self.from_user_id}, to_user_id={self.to_user_id})>"
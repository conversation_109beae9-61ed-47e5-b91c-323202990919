from sqlalchemy import Column, Integer, DateTime, Text, String, JSON, ForeignKey, func
from . import Base
import datetime


class DataControl(Base):
    __tablename__ = 'data_control'
    __table_args__ = {'comment': '数据访问控制表'}
    dt_id = Column(Integer, primary_key=True, autoincrement=True, comment='数据权限主键')
    data_type = Column(String(255), nullable=False, comment='数据资源类型：知识库、应用')
    data_id = Column(Integer, nullable=False, comment='数据资源id')
    dt_type = Column(String(255), nullable=False, comment='数据访问控制类型：公开、私有、所在部门、指定部门')
    dt_dept_code = Column(String(255), nullable=False, comment='数据访问控制部门编码')
    dt_dept_code_list = Column(String(255), nullable=False, comment='数据访问控制指定部门编码列表')
    create_time = Column(DateTime, nullable=False, default=func.now(), comment='agent创建时间')
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=datetime.datetime.utcnow, comment='agent更新时间')
    owner_id = Column(Integer, ForeignKey('users.user_id'), nullable=False, comment='数据资源所有者')
    extro_info = Column(JSON, comment='预留扩展字段')

    def to_dict(self):
        return {
            'dt_id': self.dt_id,
            'data_type': self.data_type,
            'data_id': self.data_id,
            'dt_type': self.dt_type,
            'dt_dept_code': self.dt_dept_code,
            'dt_dept_code_list': self.dt_dept_code_list,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'owner_id': self.owner_id,
            'extro_info': self.extro_info
        }
from sqlalchemy import Column, Integer, String, Text, DateTime, BigInteger, ForeignKey
from . import Base
import datetime

class ArticleSummary(Base):
    __tablename__ = 'article_summary'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    keywords = Column(Text)
    abstract = Column(Text)
    keysentences = Column(Text)
    keydata = Column(Text)
    article_wcount = Column(Integer, server_default='0')
    input_tokens = Column(Integer, server_default='0')
    output_tokens = Column(Integer, server_default='0')
    file_name = Column(String(255))
    file_type = Column(String(255))
    file_path = Column(String(255))
    io_bucket_name = Column(String(255))
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    is_deleted = Column(Integer, server_default='0')  # 添加这行
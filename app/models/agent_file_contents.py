from sqlalchemy import Column, Integer, DateTime, Text, String, JSON, ForeignKey
from . import Base
import datetime


class AgentFileContents(Base):
    __tablename__ = 'agent_file_contents'
    
    agent_file_id = Column(Integer, primary_key=True, autoincrement=True, comment='agent文件主键')
    agent_id = Column(Integer, ForeignKey('agent.agent_id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
    file_contents = Column(Text, nullable=False, comment='agent文件内容')
    parser_status = Column(Integer, nullable=False, default=0, comment='agent文件解析状态，0-未解析，1-解析中，2-解析完成')
    create_time = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, comment='agent创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment='agent更新时间')
    extro_info = Column(JSON, comment='预留扩展字段')
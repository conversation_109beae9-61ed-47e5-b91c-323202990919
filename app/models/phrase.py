from sqlalchemy import Column, Integer, Text, DateTime, ForeignKey
from . import Base
import datetime

class Phrase(Base):
    __tablename__ = 'phrases'
    phrase_id = Column(Integer, primary_key=True, autoincrement=True)
    content = Column(Text, nullable=False)
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
    user_id = Column(Integer, ForeignKey('users.user_id'), nullable=False)
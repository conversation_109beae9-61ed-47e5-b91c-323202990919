from sqlalchemy import Column, Integer, String, DateTime, BigInteger, Enum, ForeignKey
from . import Base
import datetime

class DialogMessageToken(Base):
    """对话消息Tokens记录表"""
    __tablename__ = 'dialog_messages_tokens'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    message_id = Column(Integer, nullable=False, comment='关联的消息ID')
    dialog_id = Column(Integer, nullable=False, comment='关联的对话ID')
    role = Column(Enum('user', 'assistant', 'system'), nullable=False, comment='角色')
    user_id = Column(Integer, nullable=False, comment='用户ID')
    input_tokens = Column(Integer, default=0, comment='输入tokens数, 针对user和system角色')
    output_tokens = Column(Integer, default=0, comment='输出tokens数, 针对assistant角色')
    is_deleted = Column(Integer, default=0, comment='是否删除：0-未删除，1-已删除')
    create_time = Column(DateTime, default=datetime.datetime.utcnow, comment='记录创建时间')

    def __repr__(self):
        return f"<DialogMessageToken(id={self.id}, message_id={self.message_id}, role='{self.role}')>"
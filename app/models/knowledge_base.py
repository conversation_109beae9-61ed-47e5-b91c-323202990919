from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from . import Base
import datetime
from sqlalchemy.ext.mutable import MutableDict

class KnowledgeBase(Base):
    __tablename__ = 'knowledge_bases'
    
    kb_id = Column(Integer, primary_key=True, autoincrement=True)
    kb_name = Column(String(255), nullable=False)
    kb_tags = Column(String(255))
    kb_description = Column(String(1000))
    kb_parse_strategy = Column(String(50), nullable=False, default='normal')
    kb_type = Column(String(32), nullable=False, default='normal')
    user_id = Column(Integer, nullable=False)
    shared_to_user_id = Column(Integer)
    doc_ids = Column(JSON)
    permission_group = Column(String(50), default='employee')
    permission_level = Column(String(50), default='employee')  # 默认值从'private'改为'employee'
    is_deleted = Column(Integer, default=0)  # 添加是否删除标记，默认为0表示未删除
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    extro_info = Column(MutableDict.as_mutable(JSON))
    
    def __repr__(self):
        return f"<KnowledgeBase(kb_id={self.kb_id}, kb_name='{self.kb_name}', user_id={self.user_id})>"
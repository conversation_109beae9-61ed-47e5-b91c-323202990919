import os
import tempfile
import yaml

# 项目根目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

os.environ['MINERU_TOOLS_CONFIG_JSON'] = BASE_DIR + "/conf/magic-pdf.json"
os.environ['VIRTUAL_VRAM_SIZE'] = "16"

MINERU_POOL_SIZE = 2

TEMP_DIR = tempfile.mkdtemp()

DEFAULT_INDEX_NAME = "rag_chunk_index"

def read_yaml_config(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            # 加载 YAML 文件内容
            config = yaml.safe_load(file)
            return config
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 未找到。")
    except yaml.YAMLError as e:
        print(f"错误: 解析 YAML 文件时出错 - {e}")
    return None

# 读取配置文件
config = read_yaml_config(BASE_DIR+'/conf/service_conf.yaml')

SERVER_PORT = config.get('rag_server').get('port')

# 数据库连接配置
mysql_config = config.get('mysql')
DB_CONFIG = {
    "host": mysql_config.get('host'),
    "port": mysql_config.get('port'),
    "user": mysql_config.get('user'),
    "password": mysql_config.get('password'),
    "db": mysql_config.get('db'),
    "charset": "utf8mb4"
}

# 密钥
SECRET_KEY = 'rag_demo_secret_key_asdfggsaqwer12345'
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24

# ES配置
es_config =  config.get('es')
ES_HOST=es_config.get('host')
ES_PORT=es_config.get('port')
ES_PASSWORD=es_config.get('password')
ES_USER=es_config.get('user')

# Milvus 配置
milvus_config = config.get('milvus')
MILVUS_URI = milvus_config.get('uri')

# MINIO配置
minio_config =  config.get('minio')
MINIO_HOST=minio_config.get('host')
MINIO_USER=minio_config.get('user')
MINIO_PASSWORD=minio_config.get('password')
MINIO_BUCKET_NAME=minio_config.get('bucket')
MINIO_AK="PnBO0qYbezJ9815pMT2S"
MINIO_SK="WwtRfYyBOwzXs7NMngcrlJgnLTYV73BdL06Mt8DA"

# 向量模型配置
emb_config =  config.get('embedding')
EMB_MODEL_PATH = {
    "bge-m3": emb_config.get("bge-m3")
}

# rerank模型
rerank_config =  config.get('rerank')
RERANK_MODEL_PATH = {
    "bge-reranker-v2-m3": rerank_config.get("bge-reranker-v2-m3")
}

# LLM配置
llm_config =  config.get('llm')
API_KEY = llm_config.get('api_key')
BASE_URL = llm_config.get('base_url')
LLM_MODEL = llm_config.get('model')
ENDPOINT_ID = llm_config.get('endpoint_id')

# 织语配置
zhiyu_config =  config.get('zhiyu')
ZHIYU_APP_ID = zhiyu_config.get('app_id')
ZHIYU_APP_KEY = zhiyu_config.get('app_key')
ZHIYU_ACCESS_TOKEN = zhiyu_config.get('access_token')
ZHIYU_API_BASE_URL = zhiyu_config.get('zhiyu_api_base_url')
ZHIYU_USER_ID = zhiyu_config.get('user_id')

#请求答案
CHAT_COMPLETION_API_URL = zhiyu_config.get('chat_completion_api_url')
CHAT_COMPLETION_API_TOKEN = zhiyu_config.get('chat_completion_api_token')



# 模型数量
MODEL_NUM = config.get('model_num')

# API_KEY = '5ce231eb-564a-4748-b93c-fa3c41b1bb55'
# BASE_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
# LLM_MODEL = 'deepseek-v3-241226'
# ENDPOINT_ID = 'ep-m-20250221110813-rvpzw'

# API_KEY = 'no-need'
# BASE_URL = 'http://localhost:8000/v1/chat/completions'
# LLM_MODEL = 'Qwen2.5-72B-Instruct-GPTQ-Int4'

BASE_PROMPT_TEMPLATE = """
# 任务
你是一位助手，请根据用户提问进行回答。

"""

RAG_PROMPT_TEMPLATE = """
你是一个智能助手，具备超长上下文处理能力。你的任务是根据提供的参考资料，准确回答用户问题，请严格遵循以下规则：
1. 答案必须完全基于上下文，禁止编造信息
2. 优先使用原文关键信息，保持回答的准确性
3. 回答需结构清晰，分点说明核心内容
4. 控制回答长度在上下文长度的1.5倍以内
5. 对于复杂数据（如表格、图表），需进行结构化呈现
6. 忽略上下文信息中的图片内容
7. 若上下文信息不足，请回答"知识库当前文档中没有找到相关资料，无法回答您的这个问题，非常抱歉~", 然后直接结束
"""

TEXT_PROOFREDER_PROMPT_TEMPLATE = f"""
你是一个专业的文本校对助手。请根据用户提供的文本内容和用户的问题，指出其中的错误。
### 文本内容：
{{raw_text}}

### 校对规则
1. 仔细审查用户的要求，按要求指出文本内容中的错误；
2. 可以分点输出，但是不要输出模糊的概念。
"""

ssoconfig = config.get("sso",{})
SSO_LOGIN_URL=ssoconfig.get("login_url",'http://172.17.110.80:30000/#/login?redirect=/portal')
SSO_LOOUT_URL=ssoconfig.get("logout_url", 'http://172.17.110.79:31602/api/internal/auth/logout')
SSO_API_BASE_URL=ssoconfig.get("api_base_url", 'http://172.17.110.79:31602/')
SSO_SYSTEM_CODE=ssoconfig.get("system_code", 'ai_helper')

# chatbi
chatbi_config = config.get("chatbi",{})
CHATBI_API_KEY=chatbi_config.get("api_key", '')
CHATBI_BASE_URL=chatbi_config.get("base_url", '')

CHATBI_CREATE_URL=chatbi_config.get("create_url", '')
CHATBI_EDIT_URL=chatbi_config.get("edit_url", '')



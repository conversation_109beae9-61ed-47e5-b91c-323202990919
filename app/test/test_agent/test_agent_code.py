#awk '{printf "%s\\n", $0}' test_agent_code.py | sed 's/\\n$//' | sed 's/\"/\\\"/g'

import requests
import sys
import json

SYS_PROMPT = "你是一个人工智能助手，请根据用户提问进行回答。"
BASE_URL = "http://172.17.110.105:8000/v1/chat/completions"
LLM_MODEL = "Qwen2.5-72B-Instruct-GPTQ-Int4"

INPUT = sys.stdin.read()

post_headers = {
    "Content-Type": "application/json"
}
messages = [
    {"role": "system", "content": SYS_PROMPT},
    {"role": "user", "content": INPUT}
]

post_data={
    "model": LLM_MODEL,
    "messages": messages,
    "temperature": 0.2,
    "stream": True 
}

response = requests.post(BASE_URL, json=post_data, headers=post_headers, stream=True)
# res = response.json()
# print(res["choices"][0]["message"]["content"])

for line in response.iter_lines():  # 逐行读取流数据
    if line:  # 跳过空行
        chunk = line.decode("utf-8")  # 解码字节为字符串
        # 解析大模型返回的流式数据（根据实际格式调整，如JSON或SSE事件）
        # print(f"流式响应：{decoded_line}")
        if chunk.startswith("data: [DONE]"):
            break
        if not chunk.startswith("data: "):
            continue
        json_data = json.loads(chunk[6:].strip())
        content = json_data["choices"][0]["delta"].get("content", "")
        if content:
            # 流式输出核心代码
            print(content, flush=True)

import asyncio
import json
import aiohttp
import config as conf

FILE_CONTENT = """
今天天气真好！我们去公园玩吧，小明说：“我带了风筝和零食！”大家都很高兴、准备出发的时候。他却说："我不去了"，然后就转身走了，真是让人摸不着头脑？这叫什么，友谊的小船说翻就翻吗！

今天、天气非常好！我和朋友约好去爬山，他说：“我们早上八点在地铁口见面。”结果我等了半小时他才来，真是让我哭笑不得啊！
"""


async def test_agent_route_stream():
    """测试智能体路由流式接口的正常功能"""
    try:
        print("\n开始测试智能体路由流式接口...")
        
        # 准备测试数据
        test_data = {
            "agent_id": 1,  # 假设存在一个文本校对智能体
            "user_query": "请帮我校对这段文本的语法和拼写错误",
            "file_content": FILE_CONTENT,
            "dialog_id": None  # 可选参数
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_data.items():
            print(f"{key}: {value}")
        
        # 发送HTTP流式请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/agent/route/stream',
                json=test_data,
                timeout=60  # 流式请求可能需要更长时间
            ) as response:
                print(f"\n响应状态码: {response.status}")
                print("\n流式响应内容:")
                print("=" * 50)
                
                if response.status == 200:
                    await _process_stream_response(response)
                    print("\n✅ 流式测试完成!")
                else:
                    error_text = await response.text()
                    print(f"\n❌ 测试失败!")
                    print(f"错误信息: {error_text}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def _process_stream_response(response):
    """处理流式响应内容"""
    async for line in response.content:
        line = line.decode('utf-8').strip()
        if line.startswith('data: '):
            try:
                data_str = line[6:]  # 移除 'data: ' 前缀
                data = json.loads(data_str)
                _print_message_by_type(data)
            except json.JSONDecodeError:
                print(f"⚠️  无法解析的数据: {line}")
        elif line:
            print(f"📋 原始数据: {line}")


def _print_message_by_type(data):
    """根据消息类型格式化输出"""
    msg_type = data.get('type', 'unknown')
    message = data.get('message', '')
    
    message_handlers = {
        'start': lambda: print(f"🚀 开始: {message}"),
        'routing': lambda: print(f"🔀 路由: {message}"),
        'agent_start': lambda: print(f"🤖 智能体启动: {message}"),
        'processing': lambda: print(f"⚙️ 处理中: {message}"),
        'content_start': lambda: print(f"📝 开始生成: {message}"),
        'content_delta': lambda: _handle_content_delta(data),
        'content_complete': lambda: _handle_content_complete(data, message),
        'content': lambda: print(f"📝 内容: {message}"),
        'end': lambda: print(f"✅ 完成: {message}"),
        'error': lambda: print(f"❌ 错误: {message}")
    }
    
    handler = message_handlers.get(msg_type, lambda: print(f"📄 {msg_type}: {data}"))
    handler()


def _handle_content_delta(data):
    """处理流式内容片段"""
    content = data.get('content', '')
    if content:
        print(f"📝 [{len(content)}字符]: {repr(content)}")
    else:
        print("📝 空内容片段")


def _handle_content_complete(data, message):
    """处理内容生成完成消息"""
    print(f"\n✅ 生成完成: {message}")
    full_content = data.get('full_content', '')
    if full_content:
        content_preview = (
            f"{full_content[:100]}..." if len(full_content) > 100 
            else full_content
        )
        print(f"📄 完整内容: {content_preview}")


async def test_agent_route_stream_with_invalid_agent():
    """测试无效智能体ID的情况"""
    try:
        print("\n开始测试无效智能体ID...")
        
        test_data = {
            "agent_id": 99999,  # 不存在的智能体ID
            "user_query": "测试查询",
            "file_content": "测试内容"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/agent/route/stream',
                json=test_data,
                timeout=30
            ) as response:
                print(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    await _check_error_response(response, "无效智能体ID")
                                
    except Exception as e:
        print(f"❌ 测试错误: {str(e)}")


async def test_agent_route_stream_missing_params():
    """测试缺少必要参数的情况"""
    try:
        print("\n开始测试缺少参数...")
        
        test_data = {
            # 故意不包含 agent_id
            "user_query": "测试查询",
            "file_content": "测试内容"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/agent/route/stream',
                json=test_data,
                timeout=30
            ) as response:
                print(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    await _check_error_response(response, "缺少agent_id参数")
                                
    except Exception as e:
        print(f"❌ 测试错误: {str(e)}")


async def _check_error_response(response, expected_error_keyword):
    """检查错误响应是否包含预期的错误信息"""
    async for line in response.content:
        line = line.decode('utf-8').strip()
        if line.startswith('data: '):
            try:
                data_str = line[6:]
                data = json.loads(data_str)
                msg_type = data.get('type')
                message = data.get('message', '')
                
                if msg_type == 'error':
                    if expected_error_keyword in message:
                        print(f"✅ 正确捕获错误: {message}")
                    else:
                        print(f"✅ 捕获到错误: {message}")
                    break
            except json.JSONDecodeError:
                pass


async def main():
    """主测试函数"""
    print("开始智能体路由流式接口测试...")
    print("=" * 50)
    
    # 测试正常流式处理
    await test_agent_route_stream()
    
    # 测试错误情况
    await test_agent_route_stream_with_invalid_agent()
    await test_agent_route_stream_missing_params()
    
    print("\n所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
import aiohttp
import asyncio
import json
import os
import config as conf


async def test_agent_file_parser():
    """测试智能体文件解析接口的正常功能"""
    try:
        print("\n开始测试智能体文件解析接口...")
        
        # 准备测试数据
        # test_data = {
        #     "agent_id": 1,
        #     "file_path": "rag_docs/1/original/加快推进服务业扩大开放综合试点工作方案.pdf",  # 测试文件路径
        #     "file_name": "加快推进服务业扩大开放综合试点工作方案.pdf"
        # }
        test_data = {
            "agent_id": 2,
            "file_path": "rag_docs/1/original/fault_report.xlsx",  # 测试文件路径
            "file_name": "fault_report.xlsx"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_data.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/agent/file_parser',
                json=test_data,
                timeout=600  # 文件解析可能需要较长时间
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    """主测试函数"""
    print("开始智能体文件解析接口测试...")
    print("=" * 50)
    
    await test_agent_file_parser()
    
    print("\n所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
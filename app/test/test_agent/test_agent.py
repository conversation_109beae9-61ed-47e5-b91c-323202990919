import aiohttp
import asyncio
import json

# 全局配置
BASE_URL = 'http://localhost:9900/api/v1/agent'
HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_admin_token_here'  # 需要替换为实际的 token
}

async def test_agent_post():
    """测试创建Agent"""
    print("\n=== 测试Agent创建 ===")
    
    test_data = {
        "agent_name": "测试Agent",
        "agent_code": "print('Hello from test agent!')",
        "agent_desc": "这是一个测试用的Agent",
        "agent_icon_path": "http://minio-server/bucket/icons/test_agent.png",
        "dt_dept_code": "DEPT001"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                BASE_URL,
                json=test_data,
                headers=HEADERS,
                timeout=30
            ) as response:
                result = await response.json()
                
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    agent_id = result['data']['agent_id']
                    print(f"✅ Agent创建成功! Agent ID: {agent_id}")
                    return agent_id
                else:
                    print(f"❌ Agent创建失败: {result.get('msg', '未知错误')}")
                    return None
                    
    except Exception as e:
        print(f"❌ 创建Agent时出现错误: {str(e)}")
        return None

async def test_agent_get(agent_id):
    """测试查询Agent"""
    print("\n=== 测试Agent查询 ===")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f'{BASE_URL}?agent_id={agent_id}',
                headers=HEADERS,
                timeout=30
            ) as response:
                result = await response.json()
                
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200:
                    print("✅ Agent查询成功!")
                    return result.get('data')
                else:
                    print(f"❌ Agent查询失败: {result.get('msg', '未知错误')}")
                    return None
                    
    except Exception as e:
        print(f"❌ 查询Agent时出现错误: {str(e)}")
        return None

async def test_agent_put(agent_id):
    """测试修改Agent"""
    print("\n=== 测试Agent修改 ===")
    
    update_data = {
        "agent_id": agent_id,
        "agent_name": "修改后的Agent名称",
        "agent_desc": "修改后的描述",
        "agent_code": "print('Updated agent code!')",
        "agent_icon_path": "http://minio-server/bucket/icons/updated_agent.png"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.put(
                BASE_URL,
                json=update_data,
                headers=HEADERS,
                timeout=30
            ) as response:
                result = await response.json()
                
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200:
                    print("✅ Agent修改成功!")
                    return True
                else:
                    print(f"❌ Agent修改失败: {result.get('msg', '未知错误')}")
                    return False
                    
    except Exception as e:
        print(f"❌ 修改Agent时出现错误: {str(e)}")
        return False

async def test_agent_delete(agent_id):
    """测试删除Agent"""
    print("\n=== 测试Agent删除 ===")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.delete(
                f'{BASE_URL}?agent_id={agent_id}',
                headers=HEADERS,
                timeout=30
            ) as response:
                result = await response.json()
                
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200:
                    print("✅ Agent删除成功!")
                    return True
                else:
                    print(f"❌ Agent删除失败: {result.get('msg', '未知错误')}")
                    return False
                    
    except Exception as e:
        print(f"❌ 删除Agent时出现错误: {str(e)}")
        return False

async def test_agent_handler():
    """完整的Agent测试流程"""
    print("开始测试AgentHandler接口...")
    
    # 1. 测试创建
    agent_id = await test_agent_post()
    if not agent_id:
        print("❌ 创建失败，终止测试")
        return
    
    # 2. 测试查询
    import time
    # time.sleep(1)
    agent_data = await test_agent_get(agent_id)
    if not agent_data:
        print("❌ 查询失败")
    
    # 3. 测试修改
    update_success = await test_agent_put(agent_id)
    if not update_success:
        print("❌ 修改失败")
    
    # 4. 验证修改结果
    if update_success:
        updated_data = await test_agent_get(agent_id)
        if updated_data and updated_data.get('agent_name') == "修改后的Agent名称":
            print("✅ 修改验证成功!")
        else:
            print("❌ 修改验证失败")
    
    # 5. 测试删除
    delete_success = await test_agent_delete(agent_id)
    if not delete_success:
        print("❌ 删除失败")
    
    # 6. 验证删除结果
    if delete_success:
        deleted_check = await test_agent_get(agent_id)
        if deleted_check is None:
            print("✅ 删除验证成功!")
        else:
            print("❌ 删除验证失败")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_agent_handler())
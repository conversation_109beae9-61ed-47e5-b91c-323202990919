import tornado
import tornado.escape
import tornado.httpclient
import logging
import json
import time
import re  # 导入 re 模块


# from .base_handler import BaseHandler # 假设
# from utils.zhiyu import decrypt, encrypt # 假设
# import config # 假设

# 为了独立测试，我将 Handler 和 config 相关的部分注释掉或用桩替换
class MockConfig:
    CHAT_COMPLETION_API_URL = "http://example.com/api"  # 替换为你的URL或一个可访问的测试URL
    CHAT_COMPLETION_API_TOKEN = "test_token"  # 替换为你的Token


config = MockConfig()


class ZhiyuCallbackHandler:  # 简化，只保留关键方法用于测试
    # ... (其他方法 _token_cache, _TOKEN_EXPIRES_IN_SECONDS, response, unpad_pkcs7, _get_zhiyu_token, _send_zhiyu_message 保持不变) ...

    async def _get_chat_completion_answer(self, question: str) -> str | None:
        if not config.CHAT_COMPLETION_API_URL or not config.CHAT_COMPLETION_API_TOKEN:
            logging.error("聊天API的URL或TOKEN未配置。")
            return None

        # --- 为了本地测试这段逻辑，我们直接模拟一个包含那种混合输出的 body_content_str ---
        # 在您的实际代码中，这部分是通过 http_client.fetch 获得的
        simulated_body_content_str = """
        {"code": 401, "message": "无效的访问令牌", "data": {}}data: {"content": "\u622a\u81f3"}
        data: {"content": "\u6211"}
        data: {"content": "\u6240"}
        data: {"code": 200, "message": "success", "data": {"answer": "这是模拟的答案内容。", "references": [], "dialog_id": 3394, "message_id": 7207}}
        """
        # --- 模拟结束 ---

        # 您的实际代码中这里是发起HTTP请求的逻辑
        # http_client = tornado.httpclient.AsyncHTTPClient()
        # ... (构造 request) ...
        # try:
        #     response = await http_client.fetch(request)
        #     # ... (正常处理)
        # except tornado.httpclient.HTTPClientError as e:
        #     if e.response and e.response.code == 401:
        #         body_content_str = e.response.body.decode('utf-8', errors='ignore')
        #         logging.debug(f"401 响应体内容 (str): {body_content_str}")
        #         # ... (接下来的提取逻辑) ...

        # --- 使用模拟数据直接测试提取逻辑 ---
        body_content_str = simulated_body_content_str  # 使用模拟数据
        logging.warning("收到401错误，尝试从响应体中提取后续的成功数据 (临时措施)...")
        logging.debug(f"401 响应体内容 (str):\n{body_content_str}")

        extracted_answer = None
        try:
            # 正则表达式目标：找到最后一个 "data: " 后面跟着的，且包含 "code": 200 和 "answer" 的 JSON 对象
            # 模式解释:
            # (data:\s*)?      # 可选的 "data: " 前缀，非捕获
            # (\{             # 开始捕获 JSON 对象 (捕获组1 -> json_block_str)
            #   [\s\S]*?      # 非贪婪匹配任意字符（包括换行符）
            #   "code":\s*200, # 必须包含 "code": 200,
            #   [\s\S]*?      # 非贪婪匹配任意字符
            #   "answer":\s*".*?" # 必须包含 "answer": "...", (注意 "answer" 的值是字符串)
            #   [\s\S]*?      # 非贪婪匹配任意字符
            # \})             # 结束 JSON 对象
            #
            # 这个正则还是比较复杂，我们先尝试一个能捕获所有 data: {JSON} 的模式，然后再筛选

            # 策略：
            # 1. 找到所有以 "data: " 开头的 JSON 对象字符串。
            # 2. 从后往前解析这些 JSON 字符串。
            # 3. 第一个成功解析且符合条件的即为目标。

            # 正则表达式查找所有 "data: { ...JSON... }" 这样的块
            # 它会捕获花括号内的内容
            # json_candidates = re.findall(r'data:\s*(\{[\s\S]*?\})(?=\s*data:\s*\{|$)', body_content_str)
            # 改进：确保匹配到的是一个完整的JSON对象，特别是对于嵌套括号
            # 一个更简单（但可能不太精确）的方法是，先找到所有可能是JSON对象的东西
            # 然后尝试解析它们

            # 最新的策略：直接找到包含 "code": 200 和 "answer" 的那个最大的 JSON 块，
            # 它可能前面有 "data: "

            # 这个正则表达式尝试匹配一个完整的 JSON 对象，该对象包含 "code":200 和 "answer"
            # 它会捕获整个 JSON 对象（包括最外层的 {}）
            # 它假设 "answer" 字段的值是字符串。如果 answer 可能是其他类型（如null, number, object），需要调整 "answer":\s*".*?" 部分
            regex_pattern = r'(data:\s*)?(\{[\s\S]*?"code":\s*200,[\s\S]*?"answer":\s*"(?:\\.|[^"\\])*"[\s\S]*?\})'
            # (?:\\.|[^"\\])*  这个是用来正确匹配带转义引号的字符串值的

            matches = re.finditer(regex_pattern, body_content_str)

            candidate_jsons = []
            for match in matches:
                # match.group(2) 对应第二个捕获组，即花括号内的 JSON 内容
                json_str = match.group(2)
                if json_str:
                    candidate_jsons.append(json_str)

            if not candidate_jsons:
                logging.warning("未能在401响应体中通过正则找到包含特定成功标志的JSON数据块。")
                return None

            # 从后往前尝试解析，因为我们期望最后一个是目标
            for json_str_to_parse in reversed(candidate_jsons):
                try:
                    logging.debug(f"尝试解析候选JSON片段: {json_str_to_parse[:300]}...")
                    potential_json = json.loads(json_str_to_parse)

                    if isinstance(potential_json, dict) and \
                            potential_json.get("code") == 200 and \
                            "data" in potential_json and \
                            isinstance(potential_json.get("data"), dict) and \
                            "answer" in potential_json.get("data"):

                        answer_value = potential_json["data"]["answer"]
                        if answer_value is not None:  # answer 可以是空字符串 ""
                            extracted_answer = str(answer_value)
                            logging.info(f"从401响应体中成功提取到 answer: '{extracted_answer[:100]}...'")
                            return extracted_answer  # 找到就返回
                        else:
                            logging.warning(f"从401响应体中找到成功JSON块，但 'data.answer' 为 null 或未设置。")
                    else:
                        logging.debug(f"解析的JSON块结构不符合预期的成功响应: {json_str_to_parse[:200]}")
                except json.JSONDecodeError as je_inner:
                    logging.warning(
                        f"尝试解析401响应体中的JSON片段失败: {je_inner}. 片段 (前200字符): {json_str_to_parse[:200]}")
                except Exception as ex_inner:
                    logging.warning(
                        f"处理401响应体中的JSON片段时发生未知错误: {ex_inner}. 片段: {json_str_to_parse[:200]}")

            if extracted_answer is None:
                logging.warning("在401响应体中找到了可能的JSON块，但未能提取到有效的 answer (遍历完所有候选后)。")

        except Exception as ex_outer:
            logging.error(f"处理401响应体时发生预料之外的错误: {ex_outer}", exc_info=True)

        return extracted_answer  # 如果最终没找到，则返回 None


# --- 为了测试上面的方法 ---
async def main_test():
    logging.basicConfig(level=logging.DEBUG)  # 设置日志级别为DEBUG方便查看
    handler = ZhiyuCallbackHandler()
    answer = await handler._get_chat_completion_answer("test question")
    if answer:
        print(f"\n最终提取到的答案: {answer}")
    else:
        print("\n未能提取到答案。")


if __name__ == '__main__':
    # 注意：在Tornado应用中，你不能直接这样运行async函数，需要 ioloop
    # 但为了测试 _get_chat_completion_answer 的纯逻辑，可以这样临时调用
    # 如果涉及到实际的HTTP请求，则需要 Tornado 的 IOLoop
    # tornado.ioloop.IOLoop.current().run_sync(main_test)

    # 由于我们是模拟 body_content_str, 可以直接运行这个测试
    import asyncio

    asyncio.run(main_test())
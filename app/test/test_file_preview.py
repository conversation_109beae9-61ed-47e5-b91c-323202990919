import aiohttp
import asyncio
import json
import os
import config as conf

async def test_file_preview_success():
    """测试正常的文件预览接口"""
    try:
        print("\n开始测试文件预览接口...")
        
        # 准备测试数据
        test_params = {
            "object_path": "rag_docs/1/original/基于深度迁移学习算法的跨域脑电信号解码研究.pdf"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 发送HTTP GET请求
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'http://localhost:9900/api/v1/docs/file_preview',
                params=test_params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"预览URL: {result['data']['preview_url']}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    """主测试函数"""
    print("开始文件预览接口测试...")
    print("=" * 50)
    
    await test_file_preview_success()
    
    print("\n测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
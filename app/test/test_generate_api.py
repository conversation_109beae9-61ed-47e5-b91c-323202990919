import requests
import json
import unittest
import time


class TestGenerateAPI(unittest.TestCase):
    """测试生成大纲和文章的API接口"""
    
    BASE_URL = "http://localhost:9989"
    
    def setUp(self):
        """测试前的准备工作"""
        self.outline_url = f"{self.BASE_URL}/api/v1/generate/outline"
        self.article_url = f"{self.BASE_URL}/api/v1/generate/article"
        self.headers = {"Content-Type": "application/json"}
    
    def test_generate_outline_success(self):
        """测试成功生成大纲的情况"""
        data = {
            "doc_topic": "人工智能在医疗领域的应用",
            "keywords": ["深度学习", "医学影像", "辅助诊断", "智能医疗设备", "医疗数据分析"]
        }
        
        response = requests.post(self.outline_url, headers=self.headers, data=json.dumps(data))
        
        # 验证响应状态码
        self.assertEqual(response.status_code, 200)
        
        # 验证响应内容
        result = response.json()
        self.assertEqual(result["code"], 200)
        self.assertEqual(result["message"], "success")
        
        # 验证返回的数据结构
        self.assertIn("data", result)
        
        # 如果返回的是JSON格式的大纲
        if "primary" in result["data"]:
            self.assertIsInstance(result["data"]["primary"], list)
            if len(result["data"]["primary"]) > 0:
                self.assertIn("title", result["data"]["primary"][0])
                if "secondary" in result["data"]["primary"][0]:
                    self.assertIsInstance(result["data"]["primary"][0]["secondary"], list)
        # 如果返回的是原始文本
        elif "raw_outline" in result["data"]:
            self.assertIsInstance(result["data"]["raw_outline"], str)
    
    def test_generate_outline_with_string_keywords(self):
        """测试使用字符串形式的关键词生成大纲"""
        data = {
            "doc_topic": "可持续发展与环保策略",
            "keywords": "可再生能源,碳中和,环境保护,绿色经济,生态系统"
        }
        
        response = requests.post(self.outline_url, headers=self.headers, data=json.dumps(data))
        
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["code"], 200)
    
    def test_generate_outline_missing_params(self):
        """测试缺少必要参数的情况"""
        # 缺少doc_topic
        data1 = {"keywords": ["关键词1", "关键词2"]}
        response1 = requests.post(self.outline_url, headers=self.headers, data=json.dumps(data1))
        self.assertEqual(response1.status_code, 400)
        
        # 缺少keywords
        data2 = {"doc_topic": "测试主题"}
        response2 = requests.post(self.outline_url, headers=self.headers, data=json.dumps(data2))
        self.assertEqual(response2.status_code, 400)
        
        # 空请求体
        response3 = requests.post(self.outline_url, headers=self.headers, data=json.dumps({}))
        self.assertEqual(response3.status_code, 400)
    
    def test_generate_article_success(self):
        """测试成功生成文章的情况"""
        # 先生成大纲
        outline_data = {
            "doc_topic": "数字化转型对企业的影响",
            "keywords": ["业务流程优化", "数据驱动决策", "客户体验", "创新能力", "组织变革"]
        }
        
        outline_response = requests.post(self.outline_url, headers=self.headers, data=json.dumps(outline_data))
        outline_result = outline_response.json()
        
        # 使用生成的大纲来生成文章
        if outline_response.status_code == 200:
            outline = outline_result["data"]
            
            article_data = {
                "doc_topic": "数字化转型对企业的影响",
                "outline": outline,
                "length": 1500
            }
            
            response = requests.post(self.article_url, headers=self.headers, data=json.dumps(article_data))
            
            # 验证响应状态码
            self.assertEqual(response.status_code, 200)
            
            # 验证响应内容
            result = response.json()
            self.assertEqual(result["code"], 200)
            self.assertEqual(result["message"], "success")
            
            # 验证返回的数据结构
            self.assertIn("data", result)
            self.assertIn("article", result["data"])
            self.assertIsInstance(result["data"]["article"], str)
            
            # 验证文章长度是否符合要求（粗略估计）
            article_length = len(result["data"]["article"])
            self.assertGreater(article_length, 500)  # 文章应该有一定长度
    
    def test_generate_article_with_string_outline(self):
        """测试使用字符串形式的大纲生成文章"""
        outline_str = """{
            "primary": [
                {
                    "title": "数字化转型的概念与重要性",
                    "secondary": [
                        {
                            "title": "数字化转型的定义与范围"
                        },
                        {
                            "title": "数字化转型对企业竞争力的影响"
                        }
                    ]
                },
                {
                    "title": "数字化转型的关键领域",
                    "secondary": [
                        {
                            "title": "业务流程数字化与优化"
                        },
                        {
                            "title": "数据驱动的决策机制"
                        }
                    ]
                }
            ]
        }"""
        
        data = {
            "doc_topic": "数字化转型对企业的影响",
            "outline": outline_str,
            "length": 1000
        }
        
        response = requests.post(self.article_url, headers=self.headers, data=json.dumps(data))
        
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertEqual(result["code"], 200)
    
    def test_generate_article_missing_params(self):
        """测试缺少必要参数的情况"""
        # 缺少doc_topic
        data1 = {"outline": {"primary": []}, "length": 1000}
        response1 = requests.post(self.article_url, headers=self.headers, data=json.dumps(data1))
        self.assertEqual(response1.status_code, 400)
        
        # 缺少outline
        data2 = {"doc_topic": "测试主题", "length": 1000}
        response2 = requests.post(self.article_url, headers=self.headers, data=json.dumps(data2))
        self.assertEqual(response2.status_code, 400)
        
        # 缺少length
        data3 = {"doc_topic": "测试主题", "outline": {"primary": []}}
        response3 = requests.post(self.article_url, headers=self.headers, data=json.dumps(data3))
        self.assertEqual(response3.status_code, 400)
        
        # 空请求体
        response4 = requests.post(self.article_url, headers=self.headers, data=json.dumps({}))
        self.assertEqual(response4.status_code, 400)


if __name__ == "__main__":
    unittest.main()
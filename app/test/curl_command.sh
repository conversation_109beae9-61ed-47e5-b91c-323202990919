# 1. 智能对话接口 (POST)
## 已有对话
curl -X POST http://172.17.110.105:$PORT/api/v1/chat/completion \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"question": "写一篇1000字作文", "enable_rag": false}'

## 新建对话
curl -X POST http://172.17.110.105:$PORT/api/v1/chat/completion/stream \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"question": "入住后如何提前离店", "enable_rag": true}'

## 上传临时文档进行问答
curl -X POST http://172.17.110.105:$PORT/api/v1/chat/completion/stream \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"question": "新丰县有几个内部办公室", "file_objects": ["tmp_docs/4/30a24406-52f5-4621-92eb-692c7cf97070-新丰县人民政府办公室规章制度 (2).pdf"]}'


# 2. 创建对话接口 (POST)
curl -X POST http://172.17.110.105:$PORT/api/v1/dialog \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"title": "test"}'

# 3. 删除对话 （DELTE）
curl -X DELETE "http://172.17.110.105:$PORT/api/v1/dialog?dialog_id=20" \
  -H "Authorization: Bearer $TOKEN"

# 4. 查询对话详情 (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/dialog?dialog_id=1" \
  -H "Authorization: Bearer $TOKEN"

# 5. 更新对话标题 (PUT)
curl -X PUT "http://172.17.110.105:$PORT/api/v1/dialog?dialog_id=20" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"title": "合同咨询"}'

# 6. 对话列表接口 (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/dialogs" \
  -H "Authorization: Bearer $TOKEN"

# 6. 添加常用语接口 (POST)
curl -X POST http://172.17.110.105:$PORT/api/v1/phrase \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"content": "请仔细阅读第3章节"}'

# 7. 删除常用语接口 (DELETE)
curl -X DELETE "http://172.17.110.105:$PORT/api/v1/phrase?phrase_id=2" \
  -H "Authorization: Bearer $TOKEN"

# 8. 常用语列表接口 (GET) 
curl -X GET "http://172.17.110.105:$PORT/api/v1/phrases" \
  -H "Authorization: Bearer $TOKEN"


# 9. 用户注册接口 (POST)
curl -X POST http://172.17.110.105:$PORT/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "123456"}'

# 10. 用户登录接口 (POST)
curl -X POST http://172.17.110.105:$PORT/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "123456"}'

# 11. 获取文件上传URL (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/docs/upload_url?file_name=a.pdf" \
  -H "Authorization: Bearer $TOKEN"

# 12. 文件阅读总结
curl -X POST http://172.17.110.105:$PORT/api/v1/summary \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"file_objects": ["tmp_docs/4/6714850a-4dde-4468-b289-a7fd1ceb76d8-团建 (1).docx"]}'

# 13. 用户列表
curl -X GET http://172.17.110.105:$PORT/api/v1/user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \

# 13. 用户创建
curl -X POST http://172.17.110.105:$PORT/api/v1/user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"username": "xiejq", "password": "123456", "permission_level": "admin"}'

# 14. 用户修改 (用户名、密码、权限)
curl -X PUT http://172.17.110.105:$PORT/api/v1/user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"user_id":10, "permission_level": "admin", "username":"xiejq1"}'

# 15. 用户删除
curl -X DELETE http://172.17.110.105:$PORT/api/v1/user \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"user_id":10}'

# 16. 根据关键词搜索对话 (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/dialogs?key_word=测试" \
  -H "Authorization: Bearer $TOKEN"

# 17. 点赞 (GET)
curl -X PUT http://172.17.110.105:$PORT/api/v1/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"message_id":882, "like":true, "like_reason":"回答准确"}'

# 18. 点赞 (GET)
curl -X PUT http://172.17.110.105:$PORT/api/v1/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"message_id":882, "dislike":true, "dislike_reason":"回答不准确"}'

# 19. query推荐
curl -X GET http://172.17.110.105:$PORT/api/v1/query_rec \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"query": "新丰县人民正式有几个办公室机构"}'

# 20. Agent 创建接口 (POST)
curl -X POST http://172.17.110.105:$PORT/api/v1/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "agent_name": "agent_test",
    "agent_code": "import requests", 
    "agent_desc": "一个用于测试的agent", 
    "agent_icon_path": "xxx",
    "dt_dept_code": "1000000000000000000"
}'

# 21. Agent 删除接口 (DELETE)
curl -X DELETE "http://172.17.110.105:$PORT/api/v1/agent?agent_id=1" \
  -H "Authorization: Bearer $TOKEN"

# 22. 查询单个Agent接口 (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/agent?agent_id=1" \
  -H "Authorization: Bearer $TOKEN"

# 23. 查询Agent列表接口 (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/agents?pageNo=1&pageSize=10&dt_dept_code=IT001" \
  -H "Authorization: Bearer $TOKEN"

# 24. 请求Agent应用
curl -X GET "http://172.17.110.105:$PORT/api/v1/agent/execute/stream?agent_id=1" \
  -H "Authorization: Bearer $TOKEN" \
  -d "帮我用java写个快排代码"

# 25. 更新 Agent 应用
curl -X PUT "http://172.17.110.105:$PORT/api/v1/agent?agent_id=1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "agent_name": "更新后的Agent名称",
    "agent_desc": "这是更新后的描述",
    "agent_code": "print('Hello, Updated Agent!')",
    "agent_icon_path": "https://example.com/new_icon.png",
  }'

# 26. Agent 路由流式接口
curl -N -X POST http://172.17.110.105:$PORT/api/v1/agent/route/stream \
-H "Content-Type: application/json" \
-d '{
  "agent_id": 1,
  "file_content": "xxx",
  "user_query": "xxx",
}'

# 27. Agent 文件解析接口
curl -X POST http://172.17.110.105:$PORT/api/v1/agent/agent_file_parser \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": 101,
    "file_path": "minio_path",
    "file_name": "file_name"
  }'

# 28. 文件预览接口 (GET)
curl -X GET "http://172.17.110.105:$PORT/api/v1/docs/file_preview?object_path=xxxxx" \
  -H "Authorization: Bearer $TOKEN"

import aiohttp
import asyncio
import json

async def test_chatbi_create_basic():
    """测试基本的Chatbi应用创建"""
    try:
        print("\n开始测试Chatbi应用基本创建...")
        
        # 准备测试数据
        test_data = {
            "app_name": "测试ChatBI应用",
            "app_describe": "这是一个用于测试的ChatBI应用",
            "language": "中文",
            "team_mode": "单人模式",
            "dt_dept_code": "DEPT001",
            "app_icon_path": "/icons/test_chatbi.png",
            "team_context": {
                "context_type": "business",
                "domain": "数据分析"
            },
            "param_need": [
                {
                    "name": "数据源",
                    "type": "string",
                    "required": True
                }
            ],
            "recommend_questions": [
                "本月销售数据如何？",
                "用户增长趋势分析"
            ]
        }
        
        print("\n请求数据:")
        print("=" * 50)
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            url = 'http://localhost:9900/api/v1/chatbi'
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer your_admin_token_here'  # 需要管理员权限
            }
            
            async with session.post(url, json=test_data, headers=headers, timeout=30) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                
                if response.status == 201 and result.get('code') == 201:
                    print("\n✅ Chatbi应用创建测试成功!")
                    data = result.get('data', {})
                    print(f"应用ID: {data.get('chatbi_id')}")
                    print(f"应用代码: {data.get('app_code')}")
                    print(f"应用名称: {data.get('app_name')}")
                    print(f"应用描述: {data.get('app_describe')}")
                    print(f"创建时间: {data.get('create_time')}")
                    print(f"用户ID: {data.get('user_id')}")
                else:
                    print("\n❌ Chatbi应用创建测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except Exception as e:
        print(f"\n❌ Chatbi应用创建测试过程中出现错误: {str(e)}")


async def main():
    print("开始ChatbiHandler POST方法测试...")
    print("=" * 50)
    
    # 测试基本创建功能
    await test_chatbi_create_basic()
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
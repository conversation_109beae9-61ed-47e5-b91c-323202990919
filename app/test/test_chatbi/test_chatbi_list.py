import asyncio
import json
import aiohttp

async def test_chatbi_list_basic():
    """测试基本的Chatbi列表查询"""
    try:
        print("\n开始测试Chatbi列表基本查询...")
        
        # 准备测试参数 - 使用URL查询参数
        params = {
            "pageNo": 1,
            "pageSize": 10,
            "dt_dept_code": "DEPT001"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        print(json.dumps(params, indent=2, ensure_ascii=False))
        
        # 发送HTTP请求 - 使用URL查询参数
        async with aiohttp.ClientSession() as session:
            url = 'http://localhost:9900/api/v1/chatbi/list'
            
            # 添加Authorization头部（根据实际需要调整token）
            headers = {
                'Authorization': 'Bearer your_test_token_here'
            }
            
            async with session.get(url, params=params, headers=headers, timeout=30) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 基本查询测试成功!")
                    data = result.get('data', {})
                    print(f"Chatbi总数: {data.get('total', 0)}")
                    print(f"当前页: {data.get('pageNo', 1)}")
                    print(f"每页大小: {data.get('pageSize', 10)}")
                    
                    chatbis = data.get('list', [])
                    print(f"\n返回Chatbi数量: {len(chatbis)}")
                    for i, chatbi in enumerate(chatbis, 1):
                        print(f"{i}. ID: {chatbi.get('chatbi_id')}, 名称: {chatbi.get('app_name')}")
                        print(f"   代码: {chatbi.get('app_code')}")
                        print(f"   描述: {chatbi.get('app_describe', '无描述')}")
                        print(f"   创建时间: {chatbi.get('create_time')}")
                        print(f"   用户ID: {chatbi.get('user_id')}")
                        print()
                else:
                    print("\n❌ 基本查询测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except Exception as e:
        print(f"\n❌ 基本查询测试过程中出现错误: {str(e)}")

async def test_chatbi_list_pagination():
    """测试分页查询"""
    try:
        print("\n开始测试Chatbi列表分页查询...")
        
        # 测试第2页，每页5条
        params = {
            "pageNo": 2,
            "pageSize": 5,
            "dt_dept_code": "DEPT001"
        }
        
        print("\n分页测试参数:")
        print("=" * 50)
        print(json.dumps(params, indent=2, ensure_ascii=False))
        
        async with aiohttp.ClientSession() as session:
            url = 'http://localhost:9900/api/v1/chatbi/list'
            headers = {
                'Authorization': 'Bearer your_test_token_here'
            }
            
            async with session.get(url, params=params, headers=headers, timeout=30) as response:
                result = await response.json()
                
                print("\n分页测试结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                
                if response.status == 200 and result.get('code') == 200:
                    data = result.get('data', {})
                    print(f"✅ 分页查询测试成功!")
                    print(f"总数: {data.get('total', 0)}")
                    print(f"当前页: {data.get('pageNo', 1)}")
                    print(f"每页大小: {data.get('pageSize', 10)}")
                    print(f"当前页Chatbi数量: {len(data.get('list', []))}")
                else:
                    print(f"❌ 分页查询测试失败: {result.get('msg', '未知错误')}")
                
    except Exception as e:
        print(f"\n❌ 分页查询测试过程中出现错误: {str(e)}")

async def test_chatbi_list_default_params():
    """测试默认参数查询"""
    try:
        print("\n开始测试Chatbi列表默认参数查询...")
        
        # 只传必要参数，其他使用默认值
        params = {
            "dt_dept_code": "DEPT001"
        }
        
        print("\n默认参数测试:")
        print("=" * 50)
        print(json.dumps(params, indent=2, ensure_ascii=False))
        
        async with aiohttp.ClientSession() as session:
            url = 'http://localhost:9900/api/v1/chatbi/list'
            headers = {
                'Authorization': 'Bearer your_test_token_here'
            }
            
            async with session.get(url, params=params, headers=headers, timeout=30) as response:
                result = await response.json()
                
                print("\n默认参数测试结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                
                if response.status == 200 and result.get('code') == 200:
                    data = result.get('data', {})
                    print(f"✅ 默认参数查询测试成功!")
                    print(f"默认页码: {data.get('pageNo', 1)}")
                    print(f"默认每页大小: {data.get('pageSize', 10)}")
                    print(f"Chatbi数量: {len(data.get('list', []))}")
                else:
                    print(f"❌ 默认参数查询测试失败: {result.get('msg', '未知错误')}")
                
    except Exception as e:
        print(f"\n❌ 默认参数查询测试过程中出现错误: {str(e)}")


async def main():
    print("开始ChatbiListHandler测试...")
    print("=" * 50)
    
    # 测试基本查询
    await test_chatbi_list_basic()
    
    print("\n" + "=" * 50)
    
    # 测试分页查询
    await test_chatbi_list_pagination()
    
    print("\n" + "=" * 50)
    
    # 测试默认参数
    await test_chatbi_list_default_params()
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
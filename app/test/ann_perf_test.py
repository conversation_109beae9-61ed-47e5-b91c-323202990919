import hnswlib
import numpy as np
import time

# 定义数据集参数
dim = 1024  # 数据维度
num_elements = 1000000  # 数据集中元素的数量
num_queries = 100  # 查询的数量
k = 10  # 每个查询要返回的最近邻数量

# 生成随机数据集
data = np.float32(np.random.random((num_elements, dim)))
queries = np.float32(np.random.random((num_queries, dim)))

# 初始化 HNSW 索引
index = hnswlib.Index(space='ip', dim=dim)

# 开始记录构建索引的时间
start_build = time.time()

# 构建索引
index.init_index(max_elements=num_elements, ef_construction=400, M=20)
index.add_items(data)

# 结束记录构建索引的时间
end_build = time.time()
build_time = end_build - start_build

# 设置查询时的 ef 参数
index.set_ef(50)

# 开始记录查询的时间
start_query = time.time()

# 进行查询
labels, distances = index.knn_query(queries, k=k)

# 结束记录查询的时间
end_query = time.time()
query_time = end_query - start_query

# 输出性能测试结果
print(f"构建索引时间: {build_time:.4f} 秒")
print(f"查询时间: {query_time:.4f} 秒")
print(f"平均每个查询的时间: {query_time / num_queries:.6f} 秒")
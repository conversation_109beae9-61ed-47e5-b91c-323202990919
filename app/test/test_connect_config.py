import asyncio
import aiohttp
import json
import os
from datetime import datetime

# 测试配置
API_HOST = os.getenv('API_HOST', '127.0.0.1')
API_PORT = os.getenv('API_PORT', '9989')
BASE_URL = f'http://{API_HOST}:{API_PORT}'

class ConnectConfigTester:
    def __init__(self):
        self.token = None
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.created_datasource_id = None
    
    async def login(self, session, username="admin", password="123456"):
        """登录获取token"""
        print(f"\n=== 登录获取 Token ===")
        login_data = {
            "username": username,
            "password": password
        }
        
        async with session.post(
            f'{BASE_URL}/api/v1/auth/login',
            json=login_data
        ) as response:
            result = await response.json()
            print(f"登录状态码: {response.status}")
            print(f"登录响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('code') != 200:
                raise Exception(f"登录失败: {result.get('message')}")
            
            self.token = result['data']['access_token']
            self.headers['Authorization'] = f'Bearer {self.token}'
            print(f"登录成功，Token: {self.token[:50]}...")
            return self.token
    
    async def test_get_db_types(self, session):
        """测试获取支持的数据库类型"""
        print("\n=== 测试获取数据库类型 ===")
        async with session.get(
            f"{BASE_URL}/api/v1/datasource/types",
            headers=self.headers
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return response.status == 200
    
    async def test_create_datasource(self, session):
        """测试创建数据源"""
        print("\n=== 测试创建数据源 ===")
        test_data = {
            "type": "mysql",
            "params": {
                "host": "**************",
                "port": 5455,
                "user": "root",
                "database": "rag",
                "driver": "mysql+pymysql",
                "password": "infini_rag_flow",
                "pool_size": 5,
                "max_overflow": 10,
                "pool_timeout": 30,
                "pool_recycle": 3600,
                "pool_pre_ping": True
            },
            "description": "测试数据源"
        }
        
        async with session.post(
            f"{BASE_URL}/api/v1/datasource",
            headers=self.headers,
            json=test_data
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if response.status == 200 and 'data' in result:
                self.created_datasource_id = result['data'].get('id')
                print(f"创建成功，数据源ID: {self.created_datasource_id}")
            
            return response.status == 200
    
    async def test_update_datasource(self, session):
        """测试更新数据源"""
        if not self.created_datasource_id:
            print("\n=== 跳过更新测试（无可用数据源ID） ===")
            return True
            
        print("\n=== 测试更新数据源 ===")
        update_data = {
            "id": self.created_datasource_id,
            "type": "mysql",
            "params": {
                "port": 3307,
                "pool_size": 10,
                "password": "NewPassword456!"
            },
            "description": "更新后的测试数据源"
        }
        
        async with session.put(
            f"{BASE_URL}/api/v1/datasource",
            headers=self.headers,
            json=update_data
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print(f"请求数据: {json.dumps(update_data, ensure_ascii=False, indent=2)}")
            return response.status == 200
    
    async def test_get_datasource_list(self, session):
        """测试获取数据源列表"""
        print("\n=== 测试获取数据源列表 ===")
        async with session.get(
            f"{BASE_URL}/api/v1/datasource?page=1&page_size=10",
            headers=self.headers
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return response.status == 200
    
    async def test_get_datasource_detail(self, session):
        """测试获取数据源详情"""
        if not self.created_datasource_id:
            print("\n=== 跳过获取详情测试（无可用数据源ID） ===")
            return True
            
        print("\n=== 测试获取数据源详情 ===")
        async with session.get(
            f"{BASE_URL}/api/v1/datasource?id={self.created_datasource_id}",
            headers=self.headers
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return response.status == 200
    
    async def test_connection(self, session):
        """测试数据库连接 - 使用外部API"""
        print("\n=== 测试数据库连接（外部API）===")
        test_data = {"type":"mysql","params":{"host":"*************","port":3306,"user":"finance_readonly","database":"chatbi","driver":"mysql+pymysql","password":"SecurePassword123!","pool_size":5,"max_overflow":10,"pool_timeout":30,"pool_recycle":3600,"pool_pre_ping":True},"description":"测试数据源"}
        
        async with session.post(
            f"{BASE_URL}/api/v1/datasource/test",
            json=test_data,
            headers=self.headers
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return response.status == 200
    
    async def test_delete_datasource(self, session):
        """测试删除数据源"""
        if not self.created_datasource_id:
            print("\n=== 跳过删除测试（无可用数据源ID） ===")
            return True
            
        print("\n=== 测试删除数据源 ===")
        delete_data = {
            "id": self.created_datasource_id
        }
        
        async with session.delete(
            f"{BASE_URL}/api/v1/datasource",
            headers=self.headers,
            json=delete_data
        ) as response:
            result = await response.json()
            print(f"状态码: {response.status}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return response.status == 200
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("开始数据源管理功能测试...")
        print(f"测试时间: {datetime.now()}")
        print(f"测试服务器: {BASE_URL}")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 首先登录获取 token
                await self.login(session)
                
                # 定义测试用例
                tests = [
                    ("获取数据库类型", self.test_get_db_types),
                    # ("创建数据源", self.test_create_datasource),
                    ("获取数据源列表", self.test_get_datasource_list),
                    # ("获取数据源详情", self.test_get_datasource_detail),
                    # ("更新数据源", self.test_update_datasource),
                    ("测试数据库连接", self.test_connection),
                    # ("删除数据源", self.test_delete_datasource),
                ]
                
                results = []
                for test_name, test_func in tests:
                    try:
                        result = await test_func(session)
                        results.append((test_name, "通过" if result else "失败"))
                    except Exception as e:
                        print(f"\n测试 '{test_name}' 出现异常: {str(e)}")
                        results.append((test_name, f"异常: {str(e)}"))
                
                print("\n\n=== 测试结果汇总 ===")
                for test_name, status in results:
                    print(f"{test_name}: {status}")
                    
            except Exception as e:
                print(f"登录失败，无法继续测试: {str(e)}")
                print("请检查：")
                print("1. 服务是否正常运行")
                print("2. 用户名密码是否正确")
                print("3. 数据库是否已初始化")

# 运行测试
if __name__ == "__main__":
    tester = ConnectConfigTester()
    asyncio.run(tester.run_all_tests())
import aiohttp
import asyncio
import json
import os

# Use configuration or environment variables for server address
API_HOST = os.getenv('API_HOST', '127.0.0.1')  # Default to localhost if not set
API_PORT = os.getenv('API_PORT', '9901')
BASE_URL = f'http://{API_HOST}:{API_PORT}'

async def login(session, username, password):
    """登录获取token"""
    login_data = {
        "username": username,
        "password": password
    }
    
    async with session.post(
        f'{BASE_URL}/api/v1/auth/login',
        json=login_data
    ) as response:
        result = await response.json()
        if result.get('code') != 200:
            raise Exception(f"登录失败: {result.get('message')}")
        return result['data']['access_token']

async def test_summary():
    # 测试账号信息
    username = "test002"
    password = "test002"
    
    try:
        print("\n开始测试文档总结功能...")
        
        async with aiohttp.ClientSession() as session:
            # 先进行登录获取token
            token = await login(session, username, password)
            
            # 测试文件路径
            test_file_path = "tmp_docs/1/20a4f806-e996-4572-ac02-54d8e5e24fe7-基本情况表素材（合并）测试版.docx"
            file_objects = [test_file_path]  # MinIO中的文件路径
            
            # 构造请求头（使用登录获取的token）
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            
            # 构造请求数据
            request_data = {
                "file_objects": file_objects
            }
            
            # 发送摘要请求
            async with session.post(
                f'{BASE_URL}/api/v1/summary',
                headers=headers,
                json=request_data,
                timeout=60
            ) as response:
                result = await response.json()
                
                if result.get('code') != 200:
                    print(f"请求失败: {result.get('message')}")
                    return
                
                data = result.get('data', {})
                
                print("\n总结结果:")
                print("=" * 50)
                
                print("\n1. 文档总结:")
                print(data.get('summary', ''))
                
                print("\n2. 关键词:")
                keywords = data.get('keywords', [])
                if isinstance(keywords, str):
                    keywords = json.loads(keywords)
                print(", ".join(keywords))
                
                print("\n3. 关键句:")
                keysentences = data.get('keysentences', [])
                if isinstance(keysentences, str):
                    keysentences = json.loads(keysentences)
                for i, sentence in enumerate(keysentences, 1):
                    print(f"{i}. {sentence}")
                
                print("\n4. 关键数据:")
                keydata = data.get('keydata', {})
                if isinstance(keydata, str):
                    keydata = json.loads(keydata)
                for key, value in keydata.items():
                    print(f"- {key}: {value}")
                
                print(f"\n5. 摘要ID: {data.get('summary_id', '')}")
                print(f"6. 输入tokens: {data.get('input_tokens', 0)}")
                print(f"7. 输出tokens: {data.get('output_tokens', 0)}")
                
                print("\n测试完成!")
            
    except FileNotFoundError:
        print(f"错误: 找不到测试文档 {test_file_path}")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_summary())
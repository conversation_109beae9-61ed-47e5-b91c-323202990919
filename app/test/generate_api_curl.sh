#!/bin/bash

# 设置基础URL
BASE_URL="http://localhost:9989"

# ===== 1. 生成大纲接口 (POST) =====

# 1.1 成功生成大纲 - 使用数组形式的关键词
echo "\n===== 测试生成大纲 (数组关键词) ====="
curl -X POST "${BASE_URL}/api/v1/generate/outline" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "人工智能在医疗领域的应用",
    "keywords": ["深度学习", "医学影像", "辅助诊断", "智能医疗设备", "医疗数据分析"]
  }'

# 1.2 成功生成大纲 - 使用字符串形式的关键词
echo "\n\n===== 测试生成大纲 (字符串关键词) ====="
curl -X POST "${BASE_URL}/api/v1/generate/outline" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "可持续发展与环保策略",
    "keywords": "可再生能源,碳中和,环境保护,绿色经济,生态系统"
  }'

# 1.3 参数错误 - 缺少doc_topic
echo "\n\n===== 测试生成大纲 (缺少doc_topic) ====="
curl -X POST "${BASE_URL}/api/v1/generate/outline" \
  -H "Content-Type: application/json" \
  -d '{
    "keywords": ["关键词1", "关键词2"]
  }'

# 1.4 参数错误 - 缺少keywords
echo "\n\n===== 测试生成大纲 (缺少keywords) ====="
curl -X POST "${BASE_URL}/api/v1/generate/outline" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "测试主题"
  }'

# 1.5 参数错误 - 空请求体
echo "\n\n===== 测试生成大纲 (空请求体) ====="
curl -X POST "${BASE_URL}/api/v1/generate/outline" \
  -H "Content-Type: application/json" \
  -d '{}'


# ===== 2. 生成文章接口 (POST) =====

# 2.1 完整流程 - 先生成大纲，再用大纲生成文章
echo "\n\n===== 完整流程测试：先生成大纲，再用大纲生成文章 ====="
echo "\n1. 生成大纲..."
OUTLINE_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/generate/outline" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "数字化转型对企业的影响",
    "keywords": ["业务流程优化", "数据驱动决策", "客户体验", "创新能力", "组织变革"]
  }')

# 提取大纲数据
echo "大纲生成响应:"
echo "$OUTLINE_RESPONSE" | python3 -m json.tool

# 使用jq提取大纲数据（如果系统安装了jq）
# OUTLINE_DATA=$(echo "$OUTLINE_RESPONSE" | jq -r '.data')

# 如果没有安装jq，可以使用Python提取大纲数据
echo "\n2. 使用大纲生成文章..."
echo "$OUTLINE_RESPONSE" | python3 -c '
import sys, json
try:
    data = json.load(sys.stdin)
    outline = data.get("data")
    if outline:
        article_data = {
            "doc_topic": "数字化转型对企业的影响",
            "outline": outline,
            "length": 1500
        }
        print(json.dumps(article_data))
    else:
        print("无法提取大纲数据")
except Exception as e:
    print(f"处理大纲数据时出错: {e}")
' | xargs -0 curl -X POST "${BASE_URL}/api/v1/generate/article" \
  -H "Content-Type: application/json" \
  -d @-

# 2.2 直接使用预定义的大纲生成文章
echo "\n\n===== 测试生成文章 (使用预定义大纲) ====="
curl -X POST "${BASE_URL}/api/v1/generate/article" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "数字化转型对企业的影响",
    "outline": {
      "primary": [
        {
          "title": "数字化转型的概念与重要性",
          "secondary": [
            {
              "title": "数字化转型的定义与范围"
            },
            {
              "title": "数字化转型对企业竞争力的影响"
            }
          ]
        },
        {
          "title": "数字化转型的关键领域",
          "secondary": [
            {
              "title": "业务流程数字化与优化"
            },
            {
              "title": "数据驱动的决策机制"
            }
          ]
        }
      ]
    },
    "length": 1000
  }'

# 2.3 参数错误 - 缺少doc_topic
echo "\n\n===== 测试生成文章 (缺少doc_topic) ====="
curl -X POST "${BASE_URL}/api/v1/generate/article" \
  -H "Content-Type: application/json" \
  -d '{
    "outline": {"primary": []},
    "length": 1000
  }'

# 2.4 参数错误 - 缺少outline
echo "\n\n===== 测试生成文章 (缺少outline) ====="
curl -X POST "${BASE_URL}/api/v1/generate/article" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "测试主题",
    "length": 1000
  }'

# 2.5 参数错误 - 缺少length
echo "\n\n===== 测试生成文章 (缺少length) ====="
curl -X POST "${BASE_URL}/api/v1/generate/article" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_topic": "测试主题",
    "outline": {"primary": []}
  }'

# 2.6 参数错误 - 空请求体
echo "\n\n===== 测试生成文章 (空请求体) ====="
curl -X POST "${BASE_URL}/api/v1/generate/article" \
  -H "Content-Type: application/json" \
  -d '{}'

echo "\n\n测试完成"
import asyncio
from utils.storage import StorageManager
import json
import logging
import os

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "log")
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "test_kb.log")),
        logging.StreamHandler()
    ]
)

async def test_update_kb_doc_ids():
    storage = StorageManager()
    
    logging.info("开始知识库文档测试")
    print("欢迎使用知识库文档测试！")
    while True:
        print("\n请选择操作:")
        print("1. 更新知识库文档ID")
        print("2. 删除知识库文档ID")
        print("3. 获取知识库文档列表")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ")
        
        if choice == "1":
            try:
                kb_id = int(input("请输入知识库ID: "))
                doc_id = int(input("请输入要添加的文档ID: "))
                
                logging.info(f"尝试更新知识库文档: kb_id={kb_id}, doc_id={doc_id}")
                result = await storage.update_kb_doc_ids(kb_id, doc_id)
                print("\n更新结果:")
                if result:
                    logging.info(f"知识库文档更新成功: kb_id={kb_id}, doc_id={doc_id}")
                    print(f"成功更新知识库(ID={kb_id})的文档列表，添加文档ID: {doc_id}")
                else:
                    logging.warning(f"知识库文档更新失败: kb_id={kb_id}, doc_id={doc_id}")
                    print(f"更新失败，请检查知识库ID是否存在")
                    
            except ValueError:
                logging.error("无效的ID格式")
                print("无效的ID格式，请输入数字")
            except Exception as e:
                logging.error(f"更新过程中出现错误: {str(e)}", exc_info=True)
                print(f"更新过程中出现错误: {str(e)}")
                
        elif choice == "2":
            try:
                kb_id = int(input("请输入知识库ID: "))
                doc_id = int(input("请输入要删除的文档ID: "))
                
                logging.info(f"尝试删除知识库文档: kb_id={kb_id}, doc_id={doc_id}")
                result = await storage.delete_kb_doc_id(kb_id, doc_id)
                print("\n删除结果:")
                if result["status"] == 200:
                    logging.info(f"知识库文档删除成功: kb_id={kb_id}, doc_id={doc_id}")
                    print(f"成功从知识库(ID={kb_id})删除文档ID: {doc_id}")
                else:
                    logging.warning(f"知识库文档删除失败: {result['message']}")
                    print(f"删除失败: {result['message']}")
                    
            except ValueError:
                logging.error("无效的ID格式")
                print("无效的ID格式，请输入数字")
            except Exception as e:
                logging.error(f"删除过程中出现错误: {str(e)}", exc_info=True)
                print(f"删除过程中出现错误: {str(e)}")
                
        elif choice == "3":
            try:
                kb_id = int(input("请输入知识库ID: "))
                
                logging.info(f"尝试获取知识库文档列表: kb_id={kb_id}")
                result = await storage.get_kb_doc_ids(kb_id)
                print("\n获取结果:")
                if result["status"] == 200:
                    doc_id_list = result["result"]["doc_id_list"]
                    logging.info(f"知识库文档列表获取成功: kb_id={kb_id}, doc_id_list={doc_id_list}")
                    print(f"知识库(ID={kb_id})的文档ID列表:")
                    print(f"总数: {len(doc_id_list)}")
                    print(f"文档ID: {doc_id_list}")
                else:
                    logging.warning(f"知识库文档列表获取失败: {result['message']}")
                    print(f"获取失败: {result['message']}")
                    
            except ValueError:
                logging.error("无效的ID格式")
                print("无效的ID格式，请输入数字")
            except Exception as e:
                logging.error(f"获取过程中出现错误: {str(e)}", exc_info=True)
                print(f"获取过程中出现错误: {str(e)}")
                
        elif choice == "4":
            logging.info("测试程序退出")
            print("\n感谢使用！再见！")
            break
            
        else:
            logging.warning(f"无效的选择: {choice}")
            print("\n无效的选择，请重试")
        
        input("\n按回车继续...")

if __name__ == "__main__":
    asyncio.run(test_update_kb_doc_ids())
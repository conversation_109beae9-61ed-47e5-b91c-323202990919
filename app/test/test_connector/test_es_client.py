import pytest
import time
from datetime import datetime

from connector.database.elasticsearch.es_client import ElasticSearchClient
from connector.retrieval_document import RetrievalDocument as Document
from connector.field import RetrievalField as Field


class TestElasticSearchClient:
    @pytest.fixture(scope="class")
    def es_client(self):
        client = ElasticSearchClient()
        return client
    
    @pytest.fixture(scope="class")
    def test_index(self):
        return "unitest_knowledge_qa"
    
    @pytest.fixture
    def basic_documents(self):
        return {
            Field.CHUNK_ID.value: ["chunk_1", "chunk_2"],
            Field.DOC_ID.value: "doc_1",
            Field.CHUNK_CONTENT.value: [
                "这是第一个测试文档的内容",
                "这是第二个测试文档的内容"
            ],
            Field.CHUNK_TITLE.value: ["测试文档1", "测试文档2"],
            Field.CHUNK_SOURCE.value: ["test", "test"],
            Field.CREATE_TIME.value: "2024-01-01 01:00:00",
            Field.UPDATE_TIME.value: "2024-01-01 01:00:00",
            Field.SOURCE_INFO.value: [
                {"author": "测试作者1"},
                {"author": "测试作者2"}
            ],
            Field.EXTRA_INFO.value: [
                {"category": "测试分类1"},
                {"category": "测试分类2"}
            ]
        }
    
    @pytest.fixture
    def updated_documents(self):
        return {
                "chunk_id": "chunk_1",
                "doc_id": "doc_1",
                "chunk_content": "这是更新后的内容",
                "chunk_title": "更新后的标题",
                "chunk_source": "test",
                "create_time": "2024-01-01 01:00:00",
                "update_time": "2024-01-02 01:00:00",
                "source_info": {"author": "测试作者1", "updated": True},
                "extra_info": {"category": "更新后的分类"}
            }

    def test_index_operations(self, es_client, test_index):
        """测试索引操作"""
        try:
            # 删除可能存在的索引
            delete_result = es_client.delete_index(test_index)
            assert delete_result["status"] is True
            
            # 创建索引
            create_result = es_client.create_index(test_index)
            assert create_result["status"] is True
            assert "成功" in create_result["message"]
            
            # 测试重复创建
            duplicate_create = es_client.create_index(test_index)
            assert duplicate_create["status"] is True
            assert "已存在" in duplicate_create["message"]
            
            # 测试索引存在检查
            exists = es_client.check_if_index_exist(test_index)
            assert exists == True  # 修改这里，使用 == 而不是 is
            
            # 删除不存在的索引
            non_exist_delete = es_client.delete_index("non_exist_index")
            assert non_exist_delete["status"] is True
            assert "不存在" in non_exist_delete["message"]
            
        except Exception as e:
            pytest.fail(f"索引操作测试失败: {e}")

    def test_document_operations(self, es_client, test_index, basic_documents):
        """测试文档操作"""
        try:
            # 确保索引存在
            es_client.create_index(test_index)
            
            # 测试首次插入
            insert_result = es_client.insert_documents(test_index, basic_documents)
            assert insert_result["status"] is True
            assert "成功插入" in insert_result["message"]
            
            time.sleep(1)  # 等待数据同步
            
            # 测试重复插入
            duplicate_insert = es_client.insert_documents(test_index, basic_documents)
            assert duplicate_insert["status"] is True
            assert "已经存在" in duplicate_insert["message"]
            
        except Exception as e:
            pytest.fail(f"文档操作测试失败: {e}")

    def test_search_operations(self, es_client, test_index, basic_documents):
        """测试搜索操作"""
        try:
            # 确保有数据可搜索
            es_client.insert_documents(test_index, basic_documents)
            time.sleep(1)  # 等待数据同步
            
            # 测试正常搜索
            search_result = es_client.search_by_full_text("测试文档", ["doc_1"], test_index, top_k=2)
            assert len(search_result) > 0
            assert isinstance(search_result[0], Document)
            assert search_result[0].doc_id == "doc_1"
            assert "测试文档" in search_result[0].chunk_content
            
            # 测试空查询
            empty_search = es_client.search_by_full_text("", "doc_1", test_index)
            assert len(empty_search) == 0
            
            # 测试不存在的文档
            non_exist_search = es_client.search_by_full_text("测试", "non_exist_doc", test_index)
            assert len(non_exist_search) == 0
            
        except Exception as e:
            pytest.fail(f"搜索操作测试失败: {e}")

    def test_delete_operations(self, es_client, test_index, basic_documents):
        """测试删除操作"""
        try:
            # 确保有数据可删除
            es_client.insert_documents(test_index, basic_documents)
            time.sleep(1)
            
            # 测试按chunk_id删除
            chunk_delete = es_client.delete_by_chunk_ids(test_index, "doc_1", ["chunk_1"])
            assert chunk_delete["status"] is True
            assert "成功删除" in chunk_delete["message"]
            
            # 测试删除不存在的chunk
            non_exist_chunk = es_client.delete_by_chunk_ids(test_index, "doc_1", ["non_exist_chunk"])
            assert non_exist_chunk["status"] is True
            
            # 测试空chunk_ids
            empty_chunks = es_client.delete_by_chunk_ids(test_index, "doc_1", [])
            assert empty_chunks["status"] is False
            assert "为空" in empty_chunks["message"]
            
            # 测试按doc_id删除
            doc_delete = es_client.delete_by_doc_ids(test_index, ["doc_1"])
            assert doc_delete["status"] is True
            
            # 测试删除不存在的doc
            non_exist_doc = es_client.delete_by_doc_ids(test_index, ["non_exist_doc"])
            assert non_exist_doc["status"] is True
            
        except Exception as e:
            pytest.fail(f"删除操作测试失败: {e}")

    def test_error_handling(self, es_client, test_index):
        """测试错误处理"""
        try:
            # 测试无效的索引名
            invalid_index = es_client.search_by_full_text("测试", "doc_1", "invalid_index")
            assert len(invalid_index) == 0
            
            # 测试无效的文档格式
            invalid_docs = [{"invalid": "document"}]
            invalid_insert = es_client.insert_documents(test_index, invalid_docs)
            assert invalid_insert["status"] is False
            
        except Exception as e:
            pytest.fail(f"错误处理测试失败: {e}")

    def test_index_operations(self, es_client, test_index):
        """测试索引操作"""
        try:
            # 删除可能存在的索引
            delete_result = es_client.delete_index(test_index)
            assert delete_result["status"] is True
            
            # 创建索引
            create_result = es_client.create_index(test_index)
            assert create_result["status"] is True
            assert "成功" in create_result["message"]
            
            # 测试重复创建
            duplicate_create = es_client.create_index(test_index)
            assert duplicate_create["status"] is True
            assert "已存在" in duplicate_create["message"]
            
            # 测试索引存在检查
            exists = es_client.check_if_index_exist(test_index)
            assert exists == True
            
            # 删除不存在的索引
            non_exist_delete = es_client.delete_index("non_exist_index")
            assert non_exist_delete["status"] is True
            assert "不存在" in non_exist_delete["message"]
            
        except Exception as e:
            pytest.fail(f"索引操作测试失败: {e}")

    @pytest.fixture(autouse=True)
    def cleanup(self, es_client, test_index):
        """每个测试方法执行后的清理工作"""
        yield
        es_client.delete_index(test_index)


if __name__ == '__main__':
    import os
    pytest.main(['-v', '-s', os.path.abspath(__file__)])

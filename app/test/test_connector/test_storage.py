import asyncio
from utils.storage import StorageManager
import json

async def test_storage():
    storage = StorageManager()
    
    print("欢迎使用文章存储测试！")
    while True:
        print("\n请选择操作:")
        print("1. 保存新文章")
        print("2. 获取文章信息")
        print("3. 搜索文章")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ")
        
        if choice == "1":
            # 测试保存文章
            content = """# 测试文章标题
            
这是一篇测试文章的内容。

## 第一部分
这是第一部分的内容。

## 第二部分
这是第二部分的内容。
"""
            result = await storage.save_article("测试主题", content)
            print("\n保存文章结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
                
        elif choice == "2":
            # 测试获取文章信息
            article_id = input("请输入要查询的文章ID: ")
            try:
                article_id = int(article_id)
                result = await storage.get_article_info(article_id)
                print("\n文章信息:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
            except ValueError:
                print("无效的文章ID")
                
        elif choice == "3":
            # 测试搜索文章
            keyword = input("请输入搜索关键词（直接回车搜索全部）: ")
            result = await storage.search_articles(
                keyword=keyword if keyword else None,
                limit=10,
                offset=0
            )
            print("\n搜索结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        elif choice == "4":
            print("\n感谢使用！再见！")
            break
            
        else:
            print("\n无效的选择，请重试")
        
        input("\n按回车继续...")

if __name__ == "__main__":
    asyncio.run(test_storage())
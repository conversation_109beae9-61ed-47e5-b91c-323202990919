import pytest
from connector.database.milvus.milvus_client import MilvusClient, Document
import time


class TestMilvusClient:
    @pytest.fixture(scope="class")
    def milvus_client(self):
        client = MilvusClient()
        return client
    
    @pytest.fixture(scope="class")
    def test_collection(self):
        return "unitest_knowledge_qa"
    
    @pytest.fixture(scope="class")
    def test_partition(self):
        return "test_partition"
    
    @pytest.fixture
    def basic_documents(self):
        return {
            'chunk_id': ['test_chunk_1', 'test_chunk_2'],
            'doc_id': 'test_doc_1',
            'chunk_content': ['这是测试内容1。', '这是测试内容2。'],
            'chunk_title': ['测试标题1', '测试标题2'],
            'chunk_source': ['RAW', 'RAW'],
            'dense_vector': [[0.1] * 1024, [0.2] * 1024],
            'sparse_vector': [{1: 0.5, 2: 0.5}, {3: 0.5, 4: 0.5}],
            'create_time': "2024-01-01 00:00:00",
            'update_time': "2024-01-01 00:00:00",
            'source_info': ['{"page": 1}', '{"page": 2}'],
            'extra_info': ['{"test": "info1"}', '{"test": "info2"}']
        }
    
    @pytest.fixture
    def updated_documents(self):
        return {
            'chunk_id': ['test_chunk_1'],
            'doc_id': 'test_doc_1',
            'chunk_content': ['这是更新后的内容。'],
            'chunk_title': ['更新后的标题'],
            'chunk_source': ['LLM_GEN'],
            'dense_vector': [[0.3] * 1024],
            'sparse_vector': [{5: 0.5, 6: 0.5}],
            'create_time': "2024-01-01 01:00:00",
            'update_time': "2024-01-02 01:00:00",
            'source_info': ['{"page": 1, "updated": true}'],
            'extra_info': ['{"test": "updated"}']
        }
    
    def test_insert_documents(self, milvus_client, test_collection, test_partition, basic_documents):
        """测试基本插入功能"""
        try:
            result = milvus_client.insert_documents(test_partition, basic_documents, collection_name=test_collection)
            assert result is not None, "插入操作返回值不能为None"
            assert result['status'] is True, f"插入失败: {result.get('message', '')}"
            
            # 验证数据是否正确插入
            search_result = milvus_client.search_emb_async(
                collection_name=test_collection,
                partition_ids=test_partition,
                dense_emb=[basic_documents['dense_vector'][0]],
                spare_emb=[basic_documents['sparse_vector'][0]],
                expr=f'doc_id == "{basic_documents["doc_id"]}"',
                top_k=2,
                enable_hybrid_search=False
            )
            
            assert search_result is not None, "搜索结果不能为None"
            assert len(search_result) > 0, "未找到插入的数据"
            assert search_result[0].doc_id == basic_documents['doc_id'], "文档ID不匹配"
            
        except Exception as e:
            pytest.fail(f"插入文档测试失败: {e}")
    
    def test_search_documents(self, milvus_client, test_collection, test_partition, basic_documents):
        """测试搜索功能"""
        try:
            # 先确保有数据可搜索
            milvus_client.insert_documents(test_partition, basic_documents, collection_name=test_collection)
            time.sleep(2)  # 等待数据写入
            
            # 测试普通搜索
            results = milvus_client.search_emb_async(
                collection_name=test_collection,
                partition_ids=test_partition,
                dense_emb=[basic_documents['dense_vector'][0]],
                spare_emb=[basic_documents['sparse_vector'][0]],
                expr=f'doc_id == "{basic_documents["doc_id"]}"',
                top_k=5,
                enable_hybrid_search=False
            )
            
            assert results is not None, "搜索结果不能为None"
            assert len(results) > 0, "搜索结果为空"
            assert isinstance(results[0], Document), "搜索结果类型错误"
            assert results[0].doc_id == basic_documents['doc_id'], "搜索结果文档ID不匹配"
            assert results[0].chunk_content in basic_documents['chunk_content'], "搜索结果内容不匹配"
            
            # 测试混合搜索
            hybrid_results = milvus_client.search_emb_async(
                collection_name=test_collection,
                partition_ids=test_partition,
                dense_emb=[basic_documents['dense_vector'][0]],
                spare_emb=[basic_documents['sparse_vector'][0]],
                expr=f'doc_id == "{basic_documents["doc_id"]}"',
                top_k=5,
                enable_hybrid_search=True
            )
            
            assert hybrid_results is not None, "混合搜索结果不能为None"
            print(f"混合搜索结果: {hybrid_results}")  # 调试输出
            
            # 测试 基于 doc_id 搜索
            doc_id_results = milvus_client.search_by_doc_id(
                partition_id=test_partition,
                doc_id=basic_documents['doc_id'],
                collection_name=test_collection
            )

            assert len(doc_id_results) > 0, "按 doc_id 搜索结果为空"
            assert doc_id_results[0].doc_id == basic_documents['doc_id'], "按 doc_id 搜索结果文档ID不匹配"

        except Exception as e:
            pytest.fail(f"搜索文档测试失败: {e}")
    
    def test_delete_by_doc_ids(self, milvus_client, test_collection, test_partition, basic_documents):
        """测试按文档ID删除功能"""
        try:
            # 先确保有数据可以删除
            insert_result = milvus_client.insert_documents(test_partition, basic_documents, collection_name=test_collection)
            assert insert_result['status'] is True, "插入测试数据失败"
            
            # 验证数据存在
            search_before = milvus_client.search_emb_async(
                collection_name=test_collection,
                partition_ids=test_partition,
                dense_emb=[basic_documents['dense_vector'][0]],
                spare_emb=[basic_documents['sparse_vector'][0]],
                expr=f'doc_id == "{basic_documents["doc_id"]}"',
                top_k=2,
                enable_hybrid_search=False
            )
            
            assert search_before is not None and len(search_before) > 0, "测试数据未成功插入"
            
            # 执行删除
            result = milvus_client.delete_by_doc_ids(test_partition, [basic_documents['doc_id']], collection_name=test_collection)
            assert result is not None, "删除操作返回值不能为None"
            assert result['status'] is True, f"删除失败: {result.get('message', '')}"
            
            # 验证数据已被删除
            search_after = milvus_client.search_emb_async(
                collection_name=test_collection,
                partition_ids=test_partition,
                dense_emb=[basic_documents['dense_vector'][0]],
                spare_emb=[basic_documents['sparse_vector'][0]],
                expr=f'doc_id == "{basic_documents["doc_id"]}"',
                top_k=2,
                enable_hybrid_search=False
            )
            
            assert not search_after or len(search_after) == 0, "数据未被成功删除"
            
        except Exception as e:
            pytest.fail(f"删除测试失败: {e}")
    
    def test_update_documents(self, milvus_client, test_collection, test_partition, updated_documents):
        """测试更新文档功能"""
        try:
            milvus_client.insert_documents(test_partition, updated_documents, collection_name=test_collection)
            assert True
        except Exception as e:
            pytest.fail(f"更新文档失败: {e}")
    
    def test_search_documents(self, milvus_client, test_collection, test_partition):
        """测试搜索功能"""
        dense_vector = [0.1] * 1024
        sparse_vector = {1: 0.5, 2: 0.5}
        
        try:
            results = milvus_client.search_emb_async(
                collection_name=test_collection,
                partition_ids=test_partition,
                dense_emb=[dense_vector],
                spare_emb=[sparse_vector],
                expr='doc_id == "test_doc_1"',
                top_k=2,
                enable_hybrid_search=True
            )
            assert results is not None
            if results:
                assert isinstance(results[0], Document)
        except Exception as e:
            pytest.fail(f"搜索文档失败: {e}")
    
    def test_delete_by_doc_ids(self, milvus_client, test_collection, test_partition, basic_documents):
        """测试按文档ID删除功能"""
        try:
            # 先确保有数据可以删除
            milvus_client.insert_documents(test_partition, basic_documents, collection_name=test_collection)
            
            # 尝试删除
            result = milvus_client.delete_by_doc_ids(test_partition, ['test_doc_1'], collection_name=test_collection)
            
            if result is None:
                # 再次查询确认数据是否存在
                search_result = milvus_client.search_emb_async(
                    collection_name=test_collection,
                    partition_ids=test_partition,
                    dense_emb=[[0.1] * 1024],
                    spare_emb=[{1: 0.5, 2: 0.5}],
                    expr='doc_id == "test_doc_1"',
                    top_k=2,
                    enable_hybrid_search=True
                )
                
                if search_result and len(search_result) > 0:
                    pytest.fail("数据存在但删除操作返回None")
                else:
                    assert True
            else:
                assert result['status'] is True
        except Exception as e:
            pytest.fail(f"删除测试失败: {e}")
    
    def test_delete_by_chunk_ids(self, milvus_client, test_collection, test_partition):
        """测试按分片ID删除功能"""
        try:
            milvus_client.delete_by_chunk_ids(
                test_collection, 
                test_partition, 
                'test_doc_1', 
                ['test_chunk_1', 'test_chunk_2']
            )
            assert True
        except Exception as e:
            pytest.fail(f"按分片ID删除失败: {e}")
    
    def test_invalid_documents(self, milvus_client, test_collection, test_partition):
        """测试异常处理"""
        invalid_documents = {
            'chunk_id': ['test_chunk_1'],
            'doc_id': 'test_doc_1',
            'chunk_content': ['测试内容']
            # 缺少必要字段
        }
        
        try:
            result = milvus_client.insert_documents(test_partition, invalid_documents, collection_name=test_collection)
            assert result is False or result is None, "应该插入失败但返回了成功"
        except Exception:
            # 如果抛出异常，测试也通过
            pass
    def test_search_with_invalid_params(self, milvus_client, test_collection, test_partition):
        """测试搜索功能的异常参数处理"""
        # 测试无效的向量维度
        invalid_vector = [0.1] * 100  # 不是1024维
        
        # 不再使用 pytest.raises，因为方法内部捕获了异常
        results = milvus_client.search_emb_async(
            collection_name=test_collection,
            partition_ids=test_partition,
            dense_emb=[invalid_vector],
            spare_emb=[{1: 0.5}],
            expr='doc_id == "test_doc_1"',
            top_k=5
        )
        
        # 验证结果为None（因为内部捕获了异常）
        assert results == [], "向量维度不匹配时应返回None"

    def test_delete_nonexistent_doc(self, milvus_client, test_collection, test_partition):
        """测试删除不存在的文档"""
        try:
            result = milvus_client.delete_by_doc_ids(
                test_partition, 
                ['nonexistent_doc_id'],
                collection_name=test_collection,
            )
            assert result is None or result['status'] is False
        except Exception:
            assert True

if __name__ == '__main__':
    import os
    pytest.main(['-v', '-s', os.path.abspath(__file__)])

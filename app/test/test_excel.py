import pandas as pd


def excel_to_html(excel_file_path, html_file_path):
    try:
        # 读取 Excel 文件
        df = pd.read_excel(excel_file_path)
        # 将 DataFrame 转换为 HTML 表格
        html_table = df.to_html()
        # 将 HTML 表格写入文件
        with open(html_file_path, 'w', encoding='utf-8') as file:
            file.write(html_table)
        print(f"成功将 {excel_file_path} 转换为 {html_file_path}")
    except FileNotFoundError:
        print(f"错误: 未找到 {excel_file_path} 文件。")
    except Exception as e:
        print(f"发生未知错误: {e}")


if __name__ == "__main__":
    # 请替换为你的 Excel 文件路径
    excel_file = 'test.xls'
    # 请替换为你想保存的 HTML 文件路径
    html_file = 'output.html'
    excel_to_html(excel_file, html_file)
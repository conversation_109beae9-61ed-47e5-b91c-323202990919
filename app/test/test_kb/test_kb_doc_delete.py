import aiohttp
import asyncio
import json

async def test_kb_doc_delete():
    try:
        print("\n开始测试文档删除接口...")
        
        # 准备测试数据
        test_data = {
            "kb_id": 2,
            "doc_id": 12
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_data.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/kb/doc_del',
                json=test_data,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"删除的文档ID: {result['data']['doc_id']}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_kb_doc_delete())

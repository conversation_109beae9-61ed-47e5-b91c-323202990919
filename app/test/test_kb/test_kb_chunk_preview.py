import aiohttp
import asyncio
import json

async def test_kb_chunk_preview():
    try:
        print("\n开始测试切片预览接口...")
        
        # 准备测试数据
        test_params = {
            "kb_id": 2,
            "doc_id": "17"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 构建URL和查询参数
        url = 'http://localhost:9900/api/v1/kb/chunk_preview'
        query_params = {
            'kb_id': test_params['kb_id'],
            'doc_id': test_params['doc_id']
        }
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.get(
                url,
                params=query_params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"获取到的切片数量: {len(result['data'])}")
                    # 打印第一个切片的内容预览
                    if result['data']:
                        first_chunk = result['data'][0]
                        print("\n第一个切片预览:")
                        print(f"chunk_id: {first_chunk.get('chunk_id')}")
                        print(f"content: {first_chunk.get('chunk_content')[:100]}...")  # 只显示前100个字符
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_kb_chunk_preview())
    
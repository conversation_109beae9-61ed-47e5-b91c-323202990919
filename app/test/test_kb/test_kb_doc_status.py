import aiohttp
import asyncio
import json
import os

async def test_kb_doc_status():
    try:
        print("\n开始测试文档状态查询接口...")
        
        # 准备测试参数
        test_params = {
            "doc_id": 1
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            params = "&".join([f"{k}={v}" for k, v in test_params.items()])
            url = f'http://localhost:9900/api/v1/kb/doc_parse_status?{params}'
            
            async with session.get(url, timeout=30) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(result)
                    print(f"解析状态: {result['data'].get('parse_status')}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

async def main():
    await test_kb_doc_status()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
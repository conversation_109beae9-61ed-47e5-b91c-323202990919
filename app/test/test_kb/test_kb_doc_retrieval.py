import aiohttp
import asyncio
import json
import os
import config as conf

async def test_doc_retrieval():
    try:
        print("\n开始测试文档检索接口...")
        
        # 准备测试数据
        test_params = {
            "user_id": 1,
            "doc_name": "2023",
            "doc_keyword": ["测试", "关键词"]  # 多个关键词
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 构建查询字符串
        query_params = {
            "user_id": test_params["user_id"],
            "doc_name": test_params["doc_name"]
        }
        # 添加多个关键词参数
        for keyword in test_params["doc_keyword"]:
            query_params["doc_keyword"] = keyword
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'http://localhost:9900/api/v1/kb/doc_retrieval',
                params=query_params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"文档信息: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    print("开始文档检索接口测试...")
    print("=" * 50)
    
    # 测试文档检索
    await test_doc_retrieval()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
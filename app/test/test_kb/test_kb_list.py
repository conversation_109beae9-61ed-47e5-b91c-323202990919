import asyncio
import json
import aiohttp

import config as conf

async def test_kb_list():
    try:
        print("\n开始测试知识库列表查询接口...")
        
        # 准备测试数据 - 改为URL查询参数
        params = {
            "pageNo": 1,
            "pageSize": 10,
            "dt_dept_code": "DEPT003"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in params.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求 - 使用URL查询参数而不是JSON请求体
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'http://localhost:9900/api/v1/kb/kb_list',
                params=params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    data = result['data']
                    print(f"总数: {data['total']}")
                    print(f"当前页: {data['pageNo']}")
                    print(f"页大小: {data['pageSize']}")
                    print(f"知识库数量: {len(data['list'])}")
                    
                    # 显示知识库列表
                    if data['list']:
                        print("\n知识库列表:")
                        for i, kb in enumerate(data['list'], 1):
                            print(f"{i}. {kb['kb_name']} (ID: {kb['kb_id']})")
                    else:
                        print("\n暂无知识库数据")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

async def test_kb_list_pagination():
    """测试分页功能"""
    try:
        print("\n开始测试知识库列表分页功能...")
        
        # 测试第2页，每页5条 - 改为URL查询参数
        params = {
            "pageNo": 2,
            "pageSize": 5,
            "dt_dept_code": "DEPT001"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in params.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求 - 使用URL查询参数而不是JSON请求体
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'http://localhost:9900/api/v1/kb/kb_list',
                params=params,  # 改为params参数
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 分页测试成功!")
                    data = result['data']
                    print(f"总数: {data['total']}")
                    print(f"当前页: {data['pageNo']}")
                    print(f"页大小: {data['pageSize']}")
                    print(f"当前页知识库数量: {len(data['list'])}")
                else:
                    print("\n❌ 分页测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

async def test_kb_list_all():
    """运行所有知识库列表测试"""
    print("=" * 60)
    print("\n开始运行知识库列表测试套件")
    print("=" * 60)
    
    # 基本查询测试
    await test_kb_list()
    
    # 分页功能测试
    await test_kb_list_pagination()
    
    print("=" * 60)
    print("\n知识库列表测试套件完成")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_kb_list_all())
import aiohttp
import asyncio
import json
import os
import config as conf

async def test_kb_chunk_manage(operation='insert'):
    try:
        print(f"\n开始测试切片{operation}操作...")
        
        # 准备测试数据
        test_data = {
            "doc_id": 1,
            "kb_id": 2,
            "chunk_id": "17_0" if operation != 'insert' else None,
            "operation": operation,
            "insert_type": "raw" if operation == 'insert' else None,
            "chunk_content": "这是一个测试切片内容" if operation in ['insert', 'update'] else None,
            "collection_name": "test_knowledge_qa"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_data.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/kb/chunk_manage',
                json=test_data,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"切片ID: {result['data']['chunk_id']}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    print("开始切片管理接口测试...")
    print("=" * 50)
    
    # 测试插入切片
    await test_kb_chunk_manage('insert')
    
    # 测试更新切片
    await test_kb_chunk_manage('update')
    
    # 测试删除切片
    await test_kb_chunk_manage('delete')
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
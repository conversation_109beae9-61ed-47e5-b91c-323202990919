import aiohttp
import asyncio
import json
import os
import config as conf

async def test_doc_preview():
    try:
        print("\n开始测试文档预览接口...")
        
        # 准备测试数据
        test_params = {
            "user_id": 1,
            "doc_id": 7
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'http://localhost:9900/api/v1/kb/doc_preview',
                params=test_params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_doc_preview())
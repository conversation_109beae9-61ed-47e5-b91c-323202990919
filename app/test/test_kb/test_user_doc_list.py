import aiohttp
import asyncio
import json
import config as conf

async def test_user_doc_list():
    try:
        print("\n开始测试用户文档列表接口...")
        
        # 准备测试数据
        test_params = {
            "user_id": 1,
            "pageNo": 1,
            "pageSize": 10
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 构建查询参数
        query_params = {
            "user_id": test_params["user_id"],
            "pageNo": test_params["pageNo"],
            "pageSize": test_params["pageSize"]
        }
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'http://localhost:9900/api/v1/kb/user_doc_list',
                params=query_params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"总文档数: {result['data']['total']}")
                    print(f"当前页文档数: {len(result['data']['list'])}")
                    # 打印第一个文档的信息（如果存在）
                    if result['data']['list']:
                        first_doc = result['data']['list'][0]
                        print("\n第一个文档信息:")
                        print(f"文档ID: {first_doc.get('doc_id')}")
                        print(f"文档名称: {first_doc.get('doc_name')}")
                        print(f"文档类型: {first_doc.get('doc_type')}")
                        print(f"文档状态: {first_doc.get('doc_status')}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

async def main():
    print("开始用户文档列表接口测试...")
    print("=" * 50)
    
    # 测试用户文档列表
    await test_user_doc_list()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
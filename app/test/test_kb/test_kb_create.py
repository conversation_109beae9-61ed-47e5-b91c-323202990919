import asyncio
import json
import aiohttp
import config as conf

async def test_kb_create():
    try:
        print("\n开始测试知识库创建接口...")
        
        # 准备测试数据
        test_data = {
            "kb_name": "测试知识库",
            "kb_desc": "这是一个测试用的知识库",
            "kb_tag": ["测试", "demo"],
            "kb_parse_strategy": "normal",
            "kb_auth_level": "employee",
            "emb_model": "bge-m3",
            "rerank_model": "bge-reranker-v2-m3",
            "dt_dept_code": "DEPT003"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_data.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/kb/kb_create',
                json=test_data,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"知识库ID: {result['data']['kb_id']}")
                    print(f"创建时间: {result['data']['create_time']}")
                    return result['data']['kb_id']  # 返回kb_id供其他测试使用
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                    return None
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
        return None
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(test_kb_create())
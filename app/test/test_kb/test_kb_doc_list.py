import aiohttp
import asyncio
import json
import os

async def test_kb_doc_list():
    try:
        print("\n开始测试知识库文档列表接口...")
        
        # 准备测试参数
        test_params = {
            "kb_id": 2
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            params = "&".join([f"{k}={v}" for k, v in test_params.items()])
            url = f'http://localhost:9900/api/v1/kb/kb_doc_list?{params}'
            
            async with session.get(url, timeout=30) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"文档总数: {result['data'].get('total', 0)}")
                    print("\n文档列表:")
                    for i, (doc_id, doc_name, doc_path) in enumerate(zip(
                        result['data'].get('doc_id_list', []),
                        result['data'].get('doc_name_list', []),
                        result['data'].get('doc_path_list', [])
                    )):
                        print(f"{i+1}. ID: {doc_id}, 名称: {doc_name}, 路径: {doc_path}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

async def main():
    await test_kb_doc_list()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
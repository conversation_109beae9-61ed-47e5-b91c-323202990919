import aiohttp
import asyncio
import json
import os
import config as conf

async def test_kb_doc_add():
    try:
        print("\n开始测试文档添加接口...")
        
        # 准备测试数据
        test_data = {
            "user_id": 8,
            "kb_id": 2,
            "file_path": "test_doc/2023_PDF.pdf",
            "file_name": "2023_PDF.pdf",
            "bucket_name": conf.MINIO_BUCKET_NAME
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_data.items():
            print(f"{key}: {value}")
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:9900/api/v1/kb/kb_add_doc',
                json=test_data,
                timeout=30
            ) as response:
                print(await response.text())
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    print(f"文档ID: {result['data']['doc_id']}")
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


async def main():
    print("开始文档添加接口测试...")
    print("=" * 50)
    
    # 测试正常添加文档
    await test_kb_doc_add()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())

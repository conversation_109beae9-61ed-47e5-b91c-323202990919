import aiohttp
import asyncio
import json
import config as conf

async def test_kb_chunk_retrieval():
    try:
        print("\n开始测试切片检索接口...")
        
        # 准备测试数据
        test_params = {
            "kb_id": 2,
            # "doc_ids": ["17", "18"],  # 测试多个文档ID
            "query": "测试查询内容",
            "top_k": 5,
            "retrieval_type": "vector",
            "collection_name": "knowledge_qa"
        }
        
        print("\n请求参数:")
        print("=" * 50)
        for key, value in test_params.items():
            print(f"{key}: {value}")
        
        # 构建URL和查询参数
        url = 'http://localhost:9900/api/v1/kb/chunk_retrival'
        query_params = {
            'kb_id': test_params['kb_id'],
            'query': test_params['query'],
            'top_k': test_params['top_k'],
            'retrieval_type': test_params['retrieval_type'],
            'collection_name': test_params['collection_name']
        }
        
        # 发送HTTP请求
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                params=query_params,
                timeout=30
            ) as response:
                result = await response.json()
                
                print("\n响应结果:")
                print("=" * 50)
                print(f"状态码: {response.status}")
                print(f"响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('code') == 200:
                    print("\n✅ 测试成功!")
                    
                else:
                    print("\n❌ 测试失败!")
                    print(f"错误信息: {result.get('msg', '未知错误')}")
                
    except aiohttp.ClientError as e:
        print(f"\n❌ 网络请求错误: {str(e)}")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")

async def main():
    print("开始切片检索接口测试...")
    print("=" * 50)
    
    # 测试切片检索
    await test_kb_chunk_retrieval()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from services.connect_config_service import ConnectConfigService
from models.connect_config import ConnectConfig

class TestConnectConfigService:
    
    @pytest.mark.asyncio
    async def test_get_supported_db_types(self):
        """测试获取支持的数据库类型"""
        types = ConnectConfigService.get_supported_db_types()
        assert len(types) > 0
        assert any(t['value'] == 'mysql' for t in types)
        assert any(t['value'] == 'postgresql' for t in types)
    
    @pytest.mark.asyncio
    @patch('services.connect_config_service.ConnectConfigService._call_external_api')
    async def test_create_datasource(self, mock_external_api):
        """测试创建数据源"""
        # Mock外部API响应
        mock_external_api.return_value = {'id': 'ext_123'}
        
        test_data = {
            'db_type': 'mysql',
            'db_name': 'test_db',
            'db_host': 'localhost',
            'db_port': 3306,
            'db_user': 'test_user',
            'db_pwd': 'test_pwd'
        }
        
        # 这里需要mock数据库操作
        with patch('models.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # 测试创建逻辑（需要根据实际实现调整）
            # result = await ConnectConfigService.create_datasource(1, 'test_user', test_data)
            # assert result is not None
    
    @pytest.mark.asyncio
    async def test_test_connection_mysql(self):
        """测试MySQL连接测试"""
        test_data = {
            'db_type': 'mysql',
            'db_host': 'invalid_host',
            'db_port': 3306,
            'db_user': 'test',
            'db_pwd': 'test',
            'db_name': 'test'
        }
        
        result = await ConnectConfigService.test_connection(test_data)
        # 由于是无效主机，应该返回失败
        assert result['success'] is False
        assert 'message' in result
    
    @pytest.mark.asyncio
    async def test_test_connection_sqlite(self):
        """测试SQLite连接测试"""
        test_data = {
            'db_type': 'sqlite',
            'db_path': ':memory:'  # 内存数据库
        }
        
        result = await ConnectConfigService.test_connection(test_data)
        # 内存数据库应该连接成功
        assert result['success'] is True

# 运行测试
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
import sys
import os
import json
import elasticsearch
import config

from models import Chunk, Document, Session
from utils.doc_parser import parse_doc_task
from utils.emb import get_embedding
from utils.preprocess import extract_content_with_titles
# from app.utils.hnswlib_ann import index
# from utils import milvus
from utils.minio import upload_file
from utils import es

allowed_extensions = ['.pdf', '.doc', '.docx']

def import_document(file_path, name = None, permission_level = None):
    print("------------------------import start:{}------------------------".format(file_path))

    if name is None:
        file_name = os.path.basename(file_path)
    else:
        file_name = name

    session = Session()

    # 删除同名文件旧数据
    old_docs = session.query(Document).filter(Document.name == file_name).all()
    if old_docs:
        old_doc = old_docs[0]
        old_doc_id = old_doc.doc_id
        old_chunks = session.query(Chunk).filter(Chunk.doc_id == old_doc_id).all()
        for old_chunk in old_chunks:
            # milvus.delete([old_chunk.chunk_id])
            session.delete(old_chunk)
        try:
            es.delete(old_doc_id)
        except elasticsearch.NotFoundError:
            pass
        session.delete(old_doc)
        session.commit()

    # 解析 中间结果会上传到minio
    md_content, content_list_content, tmp_pdf_path, _ = parse_doc_task(file_path)
    chunks = extract_content_with_titles(content_list_content)
    
    # 向量化
    dense_embs, sparse_embs = get_embedding('bge-m3', [json.dumps(chunk) for chunk in chunks])

    version = "1.0"
    origin_file_path = f"rag_docs/{file_name}/{version}/"+os.path.basename(file_path)
    preview_file_path = f"rag_docs/{file_name}/{version}/"+os.path.basename(tmp_pdf_path)

    # 写入数据库
    new_doc = Document(
        path = file_path, # TODO 改为minio path
        name = file_name,
        markdown = md_content,
        permission_level = "admin",
        version = version,
        minio_config = json.dumps({
            "bucket_name": config.MINIO_BUCKET_NAME,
            "origin_file_path": origin_file_path,
            "preview_file_path": preview_file_path,
        })
    )
    session.add(new_doc)
    session.commit()
    es.upsert(new_doc.doc_id, new_doc.name, md_content)
    sql_chunks = []
    for i, chunk in enumerate(chunks):
        sql_chunk = Chunk(doc_id = new_doc.doc_id,
            chunk_content = chunk["content"],
            chunk_title = chunk["title"],
            dense_vector = json.dumps(dense_embs[i]),
            sparse_vector = json.dumps(sparse_embs[i])
        )
        session.add(sql_chunk)
        sql_chunks.append(sql_chunk)
    session.commit()

    # 写入索引
    # index.add_items(embeddings, [chunk.chunk_id for chunk in sql_chunks], replace_deleted = True)
    # milvus.upsert([{
    #     "chunk_id": chunk.chunk_id,
    #     "sparse_vector": sparse_embs[i],
    #     "dense_vector": dense_embs[i]
    # } for i, chunk in enumerate(sql_chunks)])

    # 上传至minio
    # bucuket/file_name/version/file
    upload_file(file_path, origin_file_path)
    upload_file(tmp_pdf_path, preview_file_path)

    print("------------------------import success:{}------------------------".format(file_path))

def get_documents(path):
    files = []
    allowed_extensions = ['.pdf', '.doc', '.docx']
    if os.path.isdir(path):
        for root, _, filenames in os.walk(path):
            for filename in filenames:
                file_extension = os.path.splitext(filename)[1].lower()
                if file_extension in allowed_extensions:
                    files.append(os.path.join(root, filename))
    elif os.path.isfile(path):
        file_extension = os.path.splitext(path)[1].lower()
        if file_extension in allowed_extensions:
            files.append(path)

    return files

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python script.py <文档本地路径>")
        sys.exit(1)
    path = sys.argv[1]

    for file in get_documents(path):
        import_document(file)
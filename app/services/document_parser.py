import asyncio
import os
import hashlib
import traceback
import logging
import json
from concurrent.futures import ThreadPoolExecutor
from functools import partial

from services.document_manager import DocumentManager
from services.vectorindex_manger import VectorIndexManager  # 新建的向量存储服务
from utils.minio import download_file, resave_tmp_file, upload_file
from deepdoc.file_parser import FileParser
from deepdoc import PARSER_FACTORY, RegulationStrategy
import config as conf
from deepdoc.base_extractor import ExtractorResult

file_parser = FileParser()
logger = logging.getLogger(__name__)

# 限制并发处理文档的数量
MAX_CONCURRENT_TASKS = conf.MINERU_POOL_SIZE
# 创建信号量来控制并发数
parse_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)


class DocumentParser:
    """文档解析器"""
    # 用于跟踪活跃的任务
    _active_tasks = set()
    _task_lock = asyncio.Lock()

    @staticmethod
    async def parse_document(doc_id: int, kb_id:int, bucket_name: str, minio_path: str, file_name: str, parse_strategy: str, emb_model: str) -> bool:
        try:
            async with parse_semaphore:  # 使用信号量控制并发
                logger.info(f"开始解析文档 - doc_id: {doc_id}, bucket: {bucket_name}, path: {minio_path}")
                
                # 1. 从 MinIO 下载文件
                origin_file_path = f"rag_docs/{kb_id}/original/{file_name}"
                await resave_tmp_file(minio_path, origin_file_path)

                os.makedirs(conf.TEMP_DIR, exist_ok=True)  # 确保临时目录存在
                local_path = os.path.join(conf.TEMP_DIR, f"{doc_id}_{file_name}")
                logger.info(f"下载文件到本地 - local_path: {origin_file_path}")
                await download_file(origin_file_path, local_path)
                
                doc_status_param = {"doc_status": str(10)}
                await DocumentManager.update_params(doc_id, doc_status_param)

                # 2. 解析文档内容
                logger.info(f"开始解析文档内容 - doc_id: {doc_id}")
                # 使用进程池执行CPU密集型任务
                extract_res = await file_parser.extract(local_path)
                preview_file_object_path = f"rag_docs/{kb_id}/preview/{os.path.basename(extract_res.preview_file_local_path)}"
                upload_file(extract_res.preview_file_local_path, preview_file_object_path)   
                os.remove(extract_res.preview_file_local_path) 

                doc_checksum = hashlib.md5(json.dumps(extract_res.chunks, sort_keys=True).encode("utf-8")).hexdigest()
                chunk_cnt = len(extract_res.chunks)

                logger.info(f"文档解析完成 - doc_id: {doc_id}, chunks: {chunk_cnt}")

                # 3. 使用不同的解析策略处理
                strategy = PARSER_FACTORY.get(parse_strategy, RegulationStrategy)
                summary = await strategy(extract_res).summarize()
                if summary is not None:
                    extract_res.md_content += "\n" + summary
                    extract_res.chunks.append({"title": "summary", "content": summary})
                 # todo 将 parse_result 存入数据库，需要修改第四步
                doc_status_param = {"doc_status": str(60)}
                await DocumentManager.update_params(doc_id, doc_status_param)
                
                # 4. 解析结果入库
                logger.info(f"文件开始入库 - doc_id: {doc_id}")
                vector_store = VectorIndexManager()
                await vector_store.store_document_chunks(doc_id, kb_id % 32, extract_res.chunks, extract_res.md_content, file_name)
                
                doc_status_param = {"doc_status": str(80)}
                await DocumentManager.update_params(doc_id, doc_status_param)
                logger.info(f"文件入库完成 - doc_id: {doc_id}")

                # 5. 清理临时文件
                if os.path.exists(local_path):
                    os.remove(local_path)

                logger.info(f"临时文件清理完成 - doc_id: {doc_id}")

                # 6. 解析成功，更新状态 - 确保在同一事件循环中执行
                params = {
                    "parse_status": 1,
                    "doc_status": str(100),
                    "doc_checksum": doc_checksum,
                    "chunk_cnt": chunk_cnt,
                    "minio_doc_path": origin_file_path,
                    "minio_preview_doc_path": preview_file_object_path
                }
                await DocumentManager.update_params(doc_id, params)

                logger.info(f"文档解析全部完成 - doc_id: {doc_id}")

                return True
            
        except Exception as e:
            logger.error(f"文档解析失败 - doc_id: {doc_id}, 错误: {str(e)}\n{traceback.format_exc()}")
            # 解析失败，更新状态为3 - 确保在同一事件循环中执行
            await DocumentManager.update_params(doc_id, {"parse_status": 3})
            return False
        finally:
            # 确保任务从活跃集合中移除
            async with DocumentParser._task_lock:
                if task := asyncio.current_task():
                    DocumentParser._active_tasks.discard(task)

    @staticmethod
    async def start_async_parse(doc_id: int, kb_id: int, bucket_name: str, file_path: str, file_name: str, parse_strategy: str, emb_model: str):
        """启动异步解析任务"""
        try:
            # 创建任务并跟踪它
            task = asyncio.create_task(DocumentParser.parse_document(doc_id, kb_id, bucket_name, file_path, file_name, parse_strategy, emb_model))
            
            # 将任务添加到活跃任务集合中
            async with DocumentParser._task_lock:
                DocumentParser._active_tasks.add(task)
                
            # 添加完成回调以确保任务被正确清理
            task.add_done_callback(lambda t: asyncio.create_task(DocumentParser._cleanup_task(t)))
            
        except Exception as e:
            logger.error(f"异步解析任务启动失败 - doc_id: {doc_id}, 错误: {str(e)}\n{traceback.format_exc()}")

    @staticmethod
    async def _cleanup_task(task):
        """清理已完成的任务"""
        async with DocumentParser._task_lock:
            DocumentParser._active_tasks.discard(task)
            
        # 处理任务中的异常，避免未捕获的异常
        try:
            # 获取任务结果，如果有异常会在这里抛出
            if not task.cancelled():
                task.result()
        except Exception as e:
            logger.error(f"任务执行异常: {str(e)}\n{traceback.format_exc()}")
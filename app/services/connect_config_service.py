import json
import logging
import aiohttp
from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from models import async_session, pwd_context
from models.connect_config import ConnectConfig
from config import config

logger = logging.getLogger(__name__)

class ConnectConfigService:
    """数据源配置服务"""
    
    @staticmethod
    def get_external_api_config():
        """获取外部系统API配置"""
        external_config = config.get('external_datasource_api', {})
        return {
            'url': external_config.get('url', ''),
            'token': external_config.get('bearer_token', '')
        }
    
    @staticmethod
    async def _call_external_api(method: str, endpoint: str, data: Dict = None) -> Dict:
        """调用外部系统API"""
        api_config = ConnectConfigService.get_external_api_config()
        if not api_config['url'] or not api_config['token']:
            raise Exception("外部系统API配置不完整")
            
        url = f"{api_config['url'].rstrip('/')}/{endpoint.lstrip('/')}"
        headers = {
            'Authorization': f'Bearer {api_config["token"]}',
            'Content-Type': 'application/json'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.request(method, url, headers=headers, json=data) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    raise Exception(f"外部系统API调用失败: {response.status} - {error_text}")
                return await response.json()
    
    @staticmethod
    async def create_datasource(user_id: int, data: Dict[str, Any]) -> ConnectConfig:
        """创建数据源配置"""
        async with async_session() as session:
            async with session.begin():
                try:
                    # 解析新的数据格式
                    db_type = data.get('type')
                    params = data.get('params', {})
                    description = data.get('description', '')
                    
                    # 构建ext_config
                    ext_config = {
                        'driver': params.get('driver'),
                        'pool_size': params.get('pool_size'),
                        'max_overflow': params.get('max_overflow'),
                        'pool_timeout': params.get('pool_timeout'),
                        'pool_recycle': params.get('pool_recycle'),
                        'pool_pre_ping': params.get('pool_pre_ping')
                    }
                    # 移除None值
                    ext_config = {k: v for k, v in ext_config.items() if v is not None}
                    
                    # 保存原始JSON记录
                    original_json = json.dumps(data, ensure_ascii=False)
                    db_name = params.get('database')
                    if not db_name:
                        db_name = params.get('space')
                    # 创建本地数据源记录
                    datasource = ConnectConfig(
                        db_type=db_type,
                        db_name=db_name,
                        db_path=params.get('path'),
                        db_host=params.get('host'),
                        db_port=params.get('port'),
                        db_user=params.get('user'),
                        db_pwd=params.get('password'),
                        comment=description,
                        sys_code=data.get('sys_code', ''),
                        user_id=user_id,
                        ext_config=json.dumps(ext_config) if ext_config else None,
                        original_json=original_json
                    )
                    
                    session.add(datasource)
                    await session.flush()  # 获取生成的ID
                    
                    # 调用外部系统API创建数据源（直接使用原始JSON）
                    external_data = data.copy()  # 复制原始数据
                    external_result = await ConnectConfigService._call_external_api(
                        'POST', '/api/v2/serve/datasources', external_data
                    )
                    # 保存外部系统返回的ID
                    datasource.external_id = external_result.get('data').get('id')
                    
                    await session.commit()
                    return datasource
                    
                except Exception as e:
                    await session.rollback()
                    logger.error(f"创建数据源失败: {str(e)}")
                    raise
    
    @staticmethod
    async def update_datasource(datasource_id: int, user_id: int, data: Dict[str, Any]) -> ConnectConfig:
        """更新数据源配置"""
        async with async_session() as session:
            async with session.begin():
                try:
                    # 查询现有数据源
                    result = await session.execute(
                        select(ConnectConfig).where(ConnectConfig.id == datasource_id)
                    )
                    datasource = result.scalar_one_or_none()
                    
                    if not datasource:
                        raise Exception("数据源不存在")
                    
                    # 保存原始JSON记录
                    datasource.original_json = json.dumps(data, ensure_ascii=False)
                    
                    # 解析新的数据格式
                    db_type = data.get('type')
                    params = data.get('params', {})
                    description = data.get('description')
                    
                    # 更新本地数据源
                    if db_type:
                        datasource.db_type = db_type
                    if params.get('database'):
                        datasource.db_name = params['database']
                    else:
                        datasource.db_name = params.get('space')
                    if params.get('path'):
                        datasource.db_path = params['path']
                    if params.get('host'):
                        datasource.db_host = params['host']
                    if params.get('port'):
                        datasource.db_port = params['port']
                    if params.get('user'):
                        datasource.db_user = params['user']
                    if params.get('password'):
                        # 加密新密码
                        datasource.db_pwd = params['password']
                    if description is not None:
                        datasource.comment = description
                    # 更新ext_config
                    if params:
                        current_ext_config = json.loads(datasource.ext_config) if datasource.ext_config else {}
                        ext_config_updates = {
                            'driver': params.get('driver'),
                            'pool_size': params.get('pool_size'),
                            'max_overflow': params.get('max_overflow'),
                            'pool_timeout': params.get('pool_timeout'),
                            'pool_recycle': params.get('pool_recycle'),
                            'pool_pre_ping': params.get('pool_pre_ping')
                        }
                        # 更新非None值
                        for k, v in ext_config_updates.items():
                            if v is not None:
                                current_ext_config[k] = v
                        
                        datasource.ext_config = json.dumps(current_ext_config)
                    
                    # 调用外部系统API更新数据源（直接使用原始JSON）
                    if datasource.external_id:
                        external_data = data.copy()  # 复制原始数据
                        external_data['id'] = datasource.external_id  # 添加外部系统ID
                        
                        await ConnectConfigService._call_external_api(
                            'PUT', f'/api/v2/serve/datasources', external_data
                        )
                        
                    await session.commit()
                    return datasource
                        
                except Exception as e:
                    await session.rollback()
                    logger.error(f"更新数据源失败: {str(e)}")
                    raise e
    
    @staticmethod
    async def delete_datasource(datasource_id: int, user_id: int) -> bool:
        """删除数据源配置"""
        async with async_session() as session:
            async with session.begin():
                try:
                    # 查询现有数据源
                    result = await session.execute(
                        select(ConnectConfig).where(ConnectConfig.id == datasource_id)
                    )
                    datasource = result.scalar_one_or_none()
                    
                    if not datasource:
                        raise Exception("数据源不存在")
                    
                    # 调用外部系统API删除数据源
                    if datasource.external_id:
                        await ConnectConfigService._call_external_api(
                            'DELETE', f'/api/v2/serve/datasources/{datasource.external_id}'
                        )
                    
                    # 删除本地数据源
                    await session.delete(datasource)
                    await session.flush()
                    await session.commit()
                    return True
                    
                except Exception as e:
                    await session.rollback()
                    logger.error(f"删除数据源失败: {str(e)}")
                    raise e
    
    @staticmethod
    async def get_datasource_list(user_id: int, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取数据源列表"""
        async with async_session() as session:
            # 构建查询
            query = select(ConnectConfig).where(ConnectConfig.user_id == user_id)
            
            # 分页
            offset = (page - 1) * page_size
            query = query.offset(offset).limit(page_size)
            
            result = await session.execute(query)
            datasources = result.scalars().all()
            
            # 获取总数
            count_query = select(func.count(ConnectConfig.id)).where(ConnectConfig.user_id == user_id)
            count_result = await session.execute(count_query)
            total = count_result.scalar()
            datasource_list = []
            for ds in datasources:
                # 解析ext_config
                ext_config = {}
                if ds.ext_config:
                    try:
                        ext_config = json.loads(ds.ext_config)
                    except (json.JSONDecodeError, TypeError):
                        ext_config = {}
                # 解析original_json
                original_data = {}
                if ds.original_json:
                    try:
                        original_data = json.loads(ds.original_json)
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse original_json for datasource {ds.id}")
                item = {
                    'id': ds.id,
                    'type': ds.db_type,
                    'database': ds.db_name,
                    'host': ds.db_host,
                    'port': ds.db_port,
                    'user': ds.db_user,
                    'password': ds.db_pwd,
                    'sys_code': ds.sys_code,
                    'description': ds.comment,
                    'gmt_created': ds.gmt_created.isoformat() if ds.gmt_created else None,
                    'gmt_modified': ds.gmt_modified.isoformat() if ds.gmt_modified else None,
                    'external_id': ds.external_id,
                    'driver': ext_config.get('driver', ''),
                    'original_json': original_data
                }
                
                # 添加其他ext_config字段（除了driver）
                for k, v in ext_config.items():
                    if k != 'driver':
                        item[k] = v
                        
                datasource_list.append(item)
            
            return {
                'list': datasource_list,
                'total': total,
                'page': page,
                'page_size': page_size
            }
    
    @staticmethod
    async def get_datasource_detail(datasource_id: int, user_id: int) -> Dict[str, Any]:
        """获取数据源详情"""
        async with async_session() as session:
            result = await session.execute(
                select(ConnectConfig).where(
                    and_(ConnectConfig.id == datasource_id, ConnectConfig.user_id == user_id)
                )
            )
            datasource = result.scalar_one_or_none()
            
            if not datasource:
                return None
            
            # 解析ext_config
            ext_config_dict = {}
            if datasource.ext_config:
                try:
                    ext_config_dict = json.loads(datasource.ext_config)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse ext_config for datasource {datasource_id}")
            
            # 解析original_json
            original_data = {}
            if datasource.original_json:
                try:
                    original_data = json.loads(datasource.original_json)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse original_json for datasource {datasource_id}")
            
            return {
                'id': datasource.id,
                'type': datasource.db_type,
                'database': datasource.db_name,
                'host': datasource.db_host,
                'port': datasource.db_port,
                'user': datasource.db_user,
                'password': datasource.db_pwd,
                'description': datasource.comment,
                'sys_code': datasource.sys_code,
                'gmt_created': datasource.gmt_created.isoformat() if datasource.gmt_created else None,
                'gmt_modified': datasource.gmt_modified.isoformat() if datasource.gmt_modified else None,
                'external_id': datasource.external_id,
                'driver': ext_config_dict.get('driver'),
                'original_json': original_data,
                **{k: v for k, v in ext_config_dict.items() if k != 'driver'}
            }
    

    
    @staticmethod
    async def test_external_connection(data: Dict[str, Any]) -> Dict[str, Any]:
        """调用外部系统测试连接"""
        try:
            result = await ConnectConfigService._call_external_api(
                'POST', '/api/v2/serve/datasources/test-connection', data
            )
            return {'success': True, 'message': '外部系统连接测试成功', 'data': result}
        except Exception as e:
            logger.error(f"外部系统连接测试失败: {str(e)}")
            return {'success': False, 'message': f'外部系统连接测试失败: {str(e)}'}
    
    @staticmethod
    async def refresh_datasource(datasource_id: int, user_id: int) -> Dict[str, Any]:
        """刷新数据源"""
        async with async_session() as session:
            try:
                # 查询现有数据源
                result = await session.execute(
                    select(ConnectConfig).where(ConnectConfig.id == datasource_id)
                )
                datasource = result.scalar_one_or_none()
                
                if not datasource or not datasource.external_id:
                    raise Exception("数据源不存在或未同步到外部系统")
                
                # 调用外部系统刷新接口
                refresh_result = await ConnectConfigService._call_external_api(
                    'POST', f'/api/v2/serve/datasources/{datasource.external_id}/refresh'
                )
                
                return {'success': True, 'message': '数据源刷新成功', 'data': refresh_result}
                
            except Exception as e:
                logger.error(f"刷新数据源失败: {str(e)}")
                raise
    
    @staticmethod
    async def get_external_datasource_types() -> List[Dict[str, str]]:
        """获取外部系统支持的数据库类型"""
        try:
            result = await ConnectConfigService._call_external_api(
                'GET', '/api/v2/serve/datasource-types'
            )
            return result
        except Exception as e:
            logger.error(f"获取外部系统数据库类型失败: {str(e)}")
            # 返回默认支持的类型
            return ConnectConfigService.get_supported_db_types()
    
    @staticmethod
    def get_supported_db_types() -> List[Dict[str, str]]:
        """获取支持的数据库类型"""
        return [
            {
                "name": "mysql",
                "label": "MySQL datasource",
                "description": "Fast, reliable, scalable open-source relational database management system.",
                "parameters": [
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "host",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "string",
                        "label": "host",
                        "description": "数据库主机，例如：localhost",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 0
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "port",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "integer",
                        "label": "port",
                        "description": "数据库端口，例如：3306",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 1
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "user",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "string",
                        "label": "user",
                        "description": "用于连接数据库的用户",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 2
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "database",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "string",
                        "label": "database",
                        "description": "数据库名称",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 3
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "driver",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "string",
                        "label": "driver",
                        "description": "MySQL 的驱动名称，默认是 mysql+pymysql。",
                        "default_value": "mysql+pymysql",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 4
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "password",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "string",
                        "label": "password",
                        "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}",
                        "default_value": "${env:DBGPT_DB_PASSWORD}",
                        "valid_values": None,
                        "ext_metadata": {
                            "tags": "privacy"
                        },
                        "nested_fields": None,
                        "param_order": 5
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_size",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "integer",
                        "label": "pool_size",
                        "description": "连接池大小，默认为 5",
                        "default_value": 5,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 6
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "max_overflow",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "integer",
                        "label": "max_overflow",
                        "description": "最大溢出连接数，默认为 10",
                        "default_value": 10,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 7
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_timeout",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "integer",
                        "label": "pool_timeout",
                        "description": "连接池超时时间，默认为 30 秒",
                        "default_value": 30,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 8
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_recycle",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "integer",
                        "label": "pool_recycle",
                        "description": "连接池回收时间，默认为 3600 秒",
                        "default_value": 3600,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 9
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_pre_ping",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters",
                        "param_type": "boolean",
                        "label": "pool_pre_ping",
                        "description": "连接池预检查，默认开启",
                        "default_value": True,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 10
                    }
                ]
            },
 {
                "name": "hive",
                "label": "Apache Hive datasource",
                "description": "A distributed fault-tolerant data warehouse system.",
                "parameters": [
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "host",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "host",
                        "description": "Hive 服务器主机",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 0
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "port",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "integer",
                        "label": "port",
                        "description": "Hive 服务器端口，默认为 10000",
                        "default_value": 10000,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 1
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "database",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "database",
                        "description": "数据库名称，默认为 'default'",
                        "default_value": "default",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 2
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "auth",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "auth",
                        "description": "认证模式：NONE、NOSASL、LDAP、KERBEROS、CUSTOM",
                        "default_value": "NONE",
                        "valid_values": [
                            "NONE",
                            "NOSASL",
                            "LDAP",
                            "KERBEROS",
                            "CUSTOM"
                        ],
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 3
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "username",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "username",
                        "description": "用于认证的用户名",
                        "default_value": "",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 4
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "password",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "password",
                        "description": "LDAP 或 CUSTOM 认证的密码",
                        "default_value": "",
                        "valid_values": None,
                        "ext_metadata": {
                            "tags": "privacy"
                        },
                        "nested_fields": None,
                        "param_order": 5
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "kerberos_service_name",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "kerberos_service_name",
                        "description": "Kerberos 服务名称",
                        "default_value": "hive",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 6
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "transport_mode",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "transport_mode",
                        "description": "传输模式：二进制或 HTTP",
                        "default_value": "binary",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 7
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "driver",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters",
                        "param_type": "string",
                        "label": "driver",
                        "description": "Hive 的驱动程序名称，默认为 hive。",
                        "default_value": "hive",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 8
                    }
                ]
            },
{
                "name": "doris",
                "label": "Apache Doris datasource",
                "description": "A new-generation open-source real-time data warehouse.",
                "parameters": [
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "host",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "string",
                        "label": "host",
                        "description": "数据库主机，例如：localhost",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 0
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "port",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "integer",
                        "label": "port",
                        "description": "数据库端口，例如：3306",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 1
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "user",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "string",
                        "label": "user",
                        "description": "用于连接数据库的用户",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 2
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "database",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "string",
                        "label": "database",
                        "description": "数据库名称",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 3
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "driver",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "string",
                        "label": "driver",
                        "description": "Driver name for Doris, default is mysql+pymysql.",
                        "default_value": "mysql+pymysql",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 4
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "password",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "string",
                        "label": "password",
                        "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}",
                        "default_value": "${env:DBGPT_DB_PASSWORD}",
                        "valid_values": None,
                        "ext_metadata": {
                            "tags": "privacy"
                        },
                        "nested_fields": None,
                        "param_order": 5
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_size",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "integer",
                        "label": "pool_size",
                        "description": "连接池大小，默认为 5",
                        "default_value": 5,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 6
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "max_overflow",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "integer",
                        "label": "max_overflow",
                        "description": "最大溢出连接数，默认为 10",
                        "default_value": 10,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 7
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_timeout",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "integer",
                        "label": "pool_timeout",
                        "description": "连接池超时时间，默认为 30 秒",
                        "default_value": 30,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 8
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_recycle",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "integer",
                        "label": "pool_recycle",
                        "description": "连接池回收时间，默认为 3600 秒",
                        "default_value": 3600,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 9
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_pre_ping",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters",
                        "param_type": "boolean",
                        "label": "pool_pre_ping",
                        "description": "连接池预检查，默认开启",
                        "default_value": True,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 10
                    }
                ]
            },
{
                "name": "nebula",
                "label": "Nebula Graph datasource",
                "description": "Nebula Graph is a distributed, fast open-source graph database featuring horizontal scalability and high availability.",
                "parameters": [
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "host",
                        "param_class": "dbgpt_ext.datasource.conn_nebula.NebulaParameters",
                        "param_type": "string",
                        "label": "host",
                        "description": "Nebula Graph server host",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 0
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "user",
                        "param_class": "dbgpt_ext.datasource.conn_nebula.NebulaParameters",
                        "param_type": "string",
                        "label": "user",
                        "description": "Nebula Graph server user",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 1
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "password",
                        "param_class": "dbgpt_ext.datasource.conn_nebula.NebulaParameters",
                        "param_type": "string",
                        "label": "password",
                        "description": "数据库密码，您可以直接输入密码，当然也可以使用环境变量，例如 ${env:DBGPT_DB_PASSWORD}。",
                        "default_value": "${env:DBGPT_DB_PASSWORD}",
                        "valid_values": None,
                        "ext_metadata": {
                            "tags": "privacy"
                        },
                        "nested_fields": None,
                        "param_order": 2
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "port",
                        "param_class": "dbgpt_ext.datasource.conn_nebula.NebulaParameters",
                        "param_type": "integer",
                        "label": "port",
                        "description": "Nebula Graph server port, default 9669",
                        "default_value": 9669,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 3
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "space",
                        "param_class": "dbgpt_ext.datasource.conn_nebula.NebulaParameters",
                        "param_type": "string",
                        "label": "space",
                        "description": "Space name, default 'default'",
                        "default_value": "default",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 4
                    }
                ]
            },
 {
                "name": "kingbase",
                "label": "Kingbase datasource",
                "description": "Enterprise-level relational database compatible with PostgreSQL syntax.",
                "parameters": [
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "host",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "string",
                        "label": "host",
                        "description": "数据库主机，例如：localhost",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 0
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "port",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "integer",
                        "label": "port",
                        "description": "数据库端口，例如：3306",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 1
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "user",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "string",
                        "label": "user",
                        "description": "用于连接数据库的用户",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 2
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "database",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "string",
                        "label": "database",
                        "description": "数据库名称",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 3
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "driver",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "string",
                        "label": "driver",
                        "description": "Driver name for Kingbase, default is kingbase+psycopg2. Also supports postgresql+psycopg2.",
                        "default_value": "kingbase+psycopg2",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 4
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "password",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "string",
                        "label": "password",
                        "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}",
                        "default_value": "${env:DBGPT_DB_PASSWORD}",
                        "valid_values": None,
                        "ext_metadata": {
                            "tags": "privacy"
                        },
                        "nested_fields": None,
                        "param_order": 5
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_size",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "integer",
                        "label": "pool_size",
                        "description": "连接池大小，默认为 5",
                        "default_value": 5,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 6
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "max_overflow",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "integer",
                        "label": "max_overflow",
                        "description": "最大溢出连接数，默认为 10",
                        "default_value": 10,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 7
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_timeout",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "integer",
                        "label": "pool_timeout",
                        "description": "连接池超时时间，默认为 30 秒",
                        "default_value": 30,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 8
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_recycle",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "integer",
                        "label": "pool_recycle",
                        "description": "连接池回收时间，默认为 3600 秒",
                        "default_value": 3600,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 9
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_pre_ping",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "boolean",
                        "label": "pool_pre_ping",
                        "description": "连接池预检查，默认开启",
                        "default_value": True,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 10
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "schema",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_kingbase.KingbaseParameters",
                        "param_type": "string",
                        "label": "schema",
                        "description": "数据库模式，默认为 'public'",
                        "default_value": "public",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 11
                    }
                ]
            },
{
                "name": "postgresql",
                "label": "PostreSQL datasource",
                "description": "Powerful open-source relational database with extensibility and SQL standards.",
                "parameters": [
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "host",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "string",
                        "label": "host",
                        "description": "数据库主机，例如：localhost",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 0
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "port",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "integer",
                        "label": "port",
                        "description": "数据库端口，例如：3306",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 1
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "user",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "string",
                        "label": "user",
                        "description": "用于连接数据库的用户",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 2
                    },
                    {
                        "required": True,
                        "is_array": False,
                        "param_name": "database",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "string",
                        "label": "database",
                        "description": "数据库名称",
                        "default_value": None,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 3
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "driver",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "string",
                        "label": "driver",
                        "description": "Postgres 驱动程序名称，默认为 postgresql+psycopg2。",
                        "default_value": "postgresql+psycopg2",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 4
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "password",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "string",
                        "label": "password",
                        "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}",
                        "default_value": "${env:DBGPT_DB_PASSWORD}",
                        "valid_values": None,
                        "ext_metadata": {
                            "tags": "privacy"
                        },
                        "nested_fields": None,
                        "param_order": 5
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_size",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "integer",
                        "label": "pool_size",
                        "description": "连接池大小，默认为 5",
                        "default_value": 5,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 6
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "max_overflow",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "integer",
                        "label": "max_overflow",
                        "description": "最大溢出连接数，默认为 10",
                        "default_value": 10,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 7
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_timeout",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "integer",
                        "label": "pool_timeout",
                        "description": "连接池超时时间，默认为 30 秒",
                        "default_value": 30,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 8
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_recycle",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "integer",
                        "label": "pool_recycle",
                        "description": "连接池回收时间，默认为 3600 秒",
                        "default_value": 3600,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 9
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "pool_pre_ping",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "boolean",
                        "label": "pool_pre_ping",
                        "description": "连接池预检查，默认开启",
                        "default_value": True,
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 10
                    },
                    {
                        "required": False,
                        "is_array": False,
                        "param_name": "schema",
                        "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters",
                        "param_type": "string",
                        "label": "schema",
                        "description": "数据库模式，默认为 'public'",
                        "default_value": "public",
                        "valid_values": None,
                        "ext_metadata": {},
                        "nested_fields": None,
                        "param_order": 11
                    }
                ]
            }
        ]
from typing import List, Dict
import config
import time
import json
from datetime import datetime
import logging
import asyncio
import traceback

from connector.database.milvus.milvus_client import MilvusClient
from connector.database.elasticsearch.es_client import ElasticSearchClient
from connector.retrieval_document import RetrievalDocument as Document
from connector.field import Retrieval<PERSON><PERSON> as Field
from utils.emb import get_embedding
from constant.constant import ContentSourceType as cst


class VectorIndexManager:
    def __init__(self):
        self.milvus_client = MilvusClient()
        self.es_client = ElasticSearchClient()
        self.logger = logging.getLogger(__name__)

    async def store_document_chunks(
            self, 
            doc_id: int, 
            partition_id: str, 
            chunks: List[dict], 
            file_content: str,
            file_name: str,
            collection_name: str = None,
            emb_model: str = "bge-m3"
            ) -> bool:
        """存储文档分块到向量数据库"""
        try:
            # 1. 处理向量嵌入
            dense_emb, sparse_emb = get_embedding(emb_model, [json.dumps(chunk, ensure_ascii=False) for chunk in chunks])
            
            # 2. 构造分块文档
            if chunks:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                document_chunks = {
                    Field.CHUNK_ID.value: [f"{doc_id}_chunk_{i}" for i in range(len(chunks))],
                    Field.DOC_ID.value: str(doc_id),
                    Field.CHUNK_CONTENT.value: [chunk['content'] for chunk in chunks],
                    Field.CHUNK_TITLE.value: [chunk['title'][:1024] for chunk in chunks],
                    Field.CHUNK_SOURCE.value: [cst.RAW] * len(chunks),
                    Field.DENSE_VECTOR.value: dense_emb,
                    Field.SPARSE_VECTOR.value: sparse_emb,
                    Field.CREATE_TIME.value: current_time,
                    Field.UPDATE_TIME.value: current_time,
                    Field.SOURCE_INFO.value: ["{}"] * len(chunks),
                    Field.EXTRA_INFO.value: ["{}"] * len(chunks)
                }
                
                # 3. 并行存储到 Milvus 和 ES
                milvus_task = asyncio.to_thread(
                    self.milvus_client.insert_documents,
                    partition_id=partition_id,
                    documents=document_chunks,
                    collection_name=collection_name
                )

            document_full = {
                Field.CHUNK_ID.value: ["ful"],
                Field.DOC_ID.value: str(doc_id),
                Field.CHUNK_CONTENT.value: [file_content],
                Field.CHUNK_TITLE.value: [file_name],
                Field.CHUNK_SOURCE.value: [cst.FULL],
                Field.CREATE_TIME.value: current_time,
                Field.UPDATE_TIME.value: current_time,
                Field.SOURCE_INFO.value: ["{}"],
                Field.EXTRA_INFO.value: ["{}"]
            }

            es_task = asyncio.to_thread(
                self.es_client.insert_documents,
                index_name=config.DEFAULT_INDEX_NAME,
                documents=document_full
            )

            milvus_result, es_result = await asyncio.gather(milvus_task, es_task)
            
            # 检查结果
            milvus_failed = False
            es_failed = False
            if not milvus_result.get("status"):
                self.logger.error(f"Milvus 存储失败: {milvus_result.get('message')}")
                milvus_failed = True
            
            if not es_result.get("status"):
                self.logger.error(f"ES 存储失败: {es_result.get('message')}")
                es_failed = True

            if milvus_failed and es_failed:
                return False

            return True

        except Exception as e:
            self.logger.error(f"doc_id {doc_id} 存储文档分块失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

    async def delete_by_doc_id(
            self,
            doc_id: int,
            partition_id: str,
            collection_name: str = None
            ) -> bool:
        """根据文档 ID 删除向量数据库中的文档"""
        try:
            # 1. 构造查询条件
            query_expr = f"{Field.DOC_ID.value}: {doc_id}"
            
            # 2. 并行删除 Milvus 和 ES
            milvus_task = self.milvus_client.delete_by_doc_ids(
                partition_id=partition_id,
                doc_ids=[str(doc_id)],
                collection_name=collection_name
            )
            es_task = self.es_client.delete_by_doc_ids(
                index_name=config.DEFAULT_INDEX_NAME,
                doc_ids=[str(doc_id)]
            )
            
            # 等待两个任务都完成
            milvus_result, es_result = await asyncio.gather(milvus_task, es_task)
            
            # 检查结果
            milvus_failed = False
            es_failed = False
            if not milvus_result.get("status"):
                self.logger.error(f"Milvus 删除失败: {milvus_result.get('message')}")
                milvus_failed = True
            
            if not es_result.get("status"):
                self.logger.error(f"ES 删除失败: {es_result.get('message')}")
                es_failed = True

            if milvus_failed and es_failed:
                return False

            return True

        except Exception as e:
            self.logger.error(f"doc_id {doc_id} 删除文档失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False
        
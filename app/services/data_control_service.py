# app/services/data_control_service.py

from models.data_control import DataControl
from database import db_session
import json

class DataControlService:
    @staticmethod
    def create_data_control(data):
        if isinstance(data, str):
            data = json.loads(data)
        new_data_control = DataControl(**data)
        db_session.add(new_data_control)
        db_session.commit()
        db_session.refresh(new_data_control)
        return new_data_control

    @staticmethod
    def get_data_control_by_id(dt_id):
        return db_session.query(DataControl).get(dt_id)

    @staticmethod
    def get_data_control_by_data_type_id(data_type, data_id):
        return db_session.query(DataControl).filter(DataControl.data_type == data_type, DataControl.data_id == data_id).first()


    @staticmethod
    def update_data_control(dt_id, data):
        if isinstance(data, str):
            data = json.loads(data)
        data_control = db_session.query(DataControl).get(dt_id)
        if not data_control:
            return None
        for key, value in data.items():
            setattr(data_control, key, value)
        db_session.commit()
        return data_control

    @staticmethod
    def delete_data_control(dt_id):
        data_control = db_session.query(DataControl).get(dt_id)
        if not data_control:
            return False
        db_session.delete(data_control)
        db_session.commit()
        return True

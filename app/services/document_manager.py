from sqlalchemy import update, select, func
from typing import Optional, List, Dict
import datetime
import logging
import traceback
import json

from connector.database.milvus.milvus_client import MilvusClient
from connector.database.elasticsearch.es_client import ElasticSearchClient
from connector.field import Retrieval<PERSON><PERSON> as Field
from models import RAGDocument, async_session
from utils.emb import get_embedding
from utils.rerank import rerank
import config

logger = logging.getLogger(__name__)

class DocumentManager:
    """文档管理器"""

    @staticmethod
    async def add_document(
        user_id: int,
        kb_id: int,
        bucket_name: str,
        file_path: str,
        file_name: str
    ) -> Optional[RAGDocument]:
        """添加文档到知识库"""
        try:
            async with async_session() as session:
                document = RAGDocument(
                    doc_name=file_name,
                    doc_type=file_path.split('.')[-1],
                    doc_status='0',
                    kb_id=kb_id,
                    minio_doc_path=file_path,
                    minio_bucket_name=bucket_name,
                    user_id=user_id,
                    parse_status=2,
                )
                session.add(document)
                await session.commit()
                await session.refresh(document)
                return {
                    "doc_id": document.doc_id,
                }
        except Exception as e:
            error_msg = f"添加文档失败: {str(e)}, user_id={user_id}, kb_id={kb_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None

    @staticmethod
    async def get_parse_status(
        doc_id: int
    ) -> Optional[Dict]:
        """获取文档解析状态"""
        try:
            async with async_session() as session:
                query = select(RAGDocument).where(
                    RAGDocument.doc_id == doc_id
                )
                result = await session.execute(query)
                doc = result.scalar_one_or_none()
                
                if doc is None:
                    logger.info(f'未找到文档: doc_id={doc_id}')
                    return None
                
                logger.info(f'获取 doc_id={doc_id} 的解析状态: {doc.parse_status}')
                return {
                    "doc_id": doc.doc_id,
                    "parse_status": doc.parse_status,
                }
        except Exception as e:
            error_msg = f"获取解析状态失败: {str(e)}, doc_id={doc_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None

    @staticmethod
    async def delete_document(
        doc_id: int
    ) -> bool:
        """软删除文档"""
        try:
            async with async_session() as session:
                # 先查询文档状态
                query = select(RAGDocument).where(
                    RAGDocument.doc_id == doc_id
                )
                result = await session.execute(query)
                doc = result.scalar_one_or_none()
                
                if not doc:
                    logger.info(f"文档不存在: doc_id={doc_id}")
                    return {"status": False, "message": "文档不存在"}
                
                if doc.is_deleted == 1:
                    logger.warning(f"文档已经被删除, 无需重复删除: doc_id={doc_id}")
                    return {"status": False, "message": "文档已经被删除"}

                result = await session.execute(
                    update(RAGDocument)
                    .where(
                        RAGDocument.doc_id == doc_id
                    )
                    .values(is_deleted=1)
                )
                await session.commit()
                return {"status": result.rowcount > 0, "message": "删除文档成功"}
        except Exception as e:
            error_msg = f"删除文档失败: {str(e)}, doc_id={doc_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return {"status": False, "message": "删除文档失败"}
        
    @staticmethod
    async def retrieval_documents(
        user_id: int,
        kb_id: int,
        doc_name: str,
        doc_keyword: Optional[List[str]], 
    ) -> List[Dict]:
        """搜索文档
        Args:
            user_id: 用户ID，可选
            doc_name: 文档名称，支持模糊查询，可选
            doc_keyword: 文档关键词列表，可选
        Returns:
            文档列表
        """
        try:
            async with async_session() as session:
                query = select(RAGDocument).where(
                    RAGDocument.user_id == user_id,
                    RAGDocument.kb_id == kb_id,
                    RAGDocument.is_deleted == 0
                    )
                
                # 添加文档名称模糊查询
                query = query.where(RAGDocument.doc_name.like(f"%{doc_name}%"))
                
                # 执行查询
                result = await session.execute(query)
                documents = result.scalars().all()
                
                # 转换为字典列表
                return [
                    {
                        "doc_id": doc.doc_id,
                        "bucket_name": doc.minio_bucket_name,
                        "file_path": doc.minio_doc_path,
                        "file_name": doc.doc_name,
                    }
                    for doc in documents
                ]
                
        except Exception as e:
            error_msg = f"搜索文档失败: {str(e)}, user_id={user_id}, doc_name={doc_name}, doc_keyword={doc_keyword}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None
    
    @staticmethod
    async def update_params(
        doc_id: int,
        params: Dict
    ) -> bool:
        """更新文档参数
        Args:
            doc_id: 文档ID
            params: 需要更新的参数字典，如 {"doc_name": "新名称", "kb_id": 2}
        """
        try:
            async with async_session() as session:
                result = await session.execute(
                    update(RAGDocument)
                    .where(RAGDocument.doc_id == doc_id)
                    .values(**params)
                )
                await session.commit()
                return result.rowcount > 0
        except Exception as e:
            error_msg = f"更新文档参数失败: {str(e)}, doc_id={doc_id}, params={params}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return False
        
    @staticmethod
    async def get_doc_info(user_id: int, doc_id: str) -> Dict:
        """获取文档预览信息"""
        try:
            async with async_session() as session:
                # 查询文档信息
                query = select(RAGDocument).where(
                    RAGDocument.doc_id == doc_id,
                )
                result = await session.execute(query)
                doc = result.scalar_one_or_none()
                
                if not doc:
                    logger.warning(f'未找到文档: doc_id={doc_id}')
                    return None
                
                # 检查用户权限
                if doc.user_id != user_id:
                    logger.warning(f'用户无权限访问文档: user_id={user_id}, doc_id={doc_id}')
                    return None
                
                return {
                    "bucket_name": doc.minio_bucket_name,
                    "file_path": doc.minio_doc_path,
                    "preview_file_path": doc.minio_preview_doc_path if doc.minio_preview_doc_path else doc.minio_doc_path,
                    "file_name": doc.doc_name,
                    "parse_status": doc.parse_status,
                    "kb_id": doc.kb_id,
                    "doc_status": doc.doc_status,
                }
        except Exception as e:
            error_msg = f"获取文档预览信息失败: {str(e)}, user_id={user_id}, doc_id={doc_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None
    
    @staticmethod
    async def manage_chunk(
        doc_id: int,
        kb_id: int,
        chunk_id: str,
        operation: str,
        insert_type: Optional[str] = None,
        chunk_content: Optional[str] = None,
        collection_name: Optional[str] = None
    ) -> Dict:
        """管理文档切片
        
        Args:
            doc_id: 文档ID
            kb_id: 知识库ID
            chunk_id: 切片ID
            operation: 操作类型 (insert/update/delete)
            insert_type: 插入类型 (raw/seq/summary)，仅在 operation=insert 时需要
            chunk_content: 切片内容，在 insert/update 操作时需要
            collection_name: 切片所属的collection名称，默认值为None，当为None时，使用默认的collection名称
            
        Returns:
            Dict: 包含操作结果的字典
        """
        try:
            async with async_session() as session:
                # 检查文档是否存在
                query = select(RAGDocument).where(
                    RAGDocument.doc_id == doc_id,
                    RAGDocument.kb_id == kb_id,
                    RAGDocument.is_deleted == 0
                )
                result = await session.execute(query)
                doc = result.scalar_one_or_none()
                
                if not doc:
                    return {"status": False, "message": "文档不存在或已删除"}
                
                milvus_client = MilvusClient()
                es_client = ElasticSearchClient()
                
                # 根据操作类型处理
                if operation == "delete":
                    # 删除切片
                    milvus_client.delete_by_chunk_ids(kb_id % 32, doc_id, [chunk_id])
                    es_client.delete_by_chunk_ids(config.DEFAULT_INDEX_NAME, doc_id, [chunk_id])
                    await DocumentManager.update_params(doc_id, {"chunk_cnt": doc.chunk_cnt - 1})
                    
                elif operation == "update":
                    # 先检索出来原始的chunk内容
                    original_docs = milvus_client.search_by_chunk_id(kb_id % 32, doc_id, chunk_id)
                    if not original_docs:
                        return {"status": False, "message": "未找到原始切片"}
                    
                    original_doc = original_docs[0]  # 获取第一个文档
                    # 更新切片
                    dense_emb, sparse_emb = get_embedding('bge-m3', [chunk_content])
                    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    chunk_data = {
                        Field.CHUNK_ID.value: [chunk_id],
                        Field.DOC_ID.value: str(doc_id),
                        Field.CHUNK_CONTENT.value: [chunk_content],
                        Field.CHUNK_TITLE.value: [''],
                        Field.CHUNK_SOURCE.value: ['raw'],
                        Field.DENSE_VECTOR.value: dense_emb,
                        Field.SPARSE_VECTOR.value: sparse_emb,
                        Field.CREATE_TIME.value: original_doc.meta_data.get('create_time', now),  # 从meta_data获取
                        Field.UPDATE_TIME.value: now,
                        Field.SOURCE_INFO.value: [original_doc.meta_data.get('source_info', json.dumps({}))],  # 从meta_data获取
                        Field.EXTRA_INFO.value: [original_doc.meta_data.get('extra_info', json.dumps({}))]  # 从meta_data获取
                    }
                    milvus_client.insert_documents(kb_id % 32, chunk_data, collection_name=collection_name)
                    es_client.insert_documents(collection_name, chunk_data)
                    
                elif operation == "insert":
                    # 生成新的chunk_id
                    new_chunk_id = f"{doc_id}_{doc.chunk_cnt + 1}"
                    
                    # 准备插入数据
                    dense_emb, sparse_emb = get_embedding('bge-m3', [chunk_content])
                    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    chunk_data = {
                        Field.CHUNK_ID.value: [new_chunk_id],
                        Field.DOC_ID.value: str(doc_id),
                        Field.CHUNK_CONTENT.value: [chunk_content],
                        Field.CHUNK_TITLE.value: [''],
                        Field.CHUNK_SOURCE.value: [insert_type],
                        Field.DENSE_VECTOR.value: dense_emb,
                        Field.SPARSE_VECTOR.value: sparse_emb,
                        Field.CREATE_TIME.value: now,
                        Field.UPDATE_TIME.value: now,
                        Field.SOURCE_INFO.value: [json.dumps({})],  # 空SOURCE_INFO
                        Field.EXTRA_INFO.value: [json.dumps({})]
                    }
                    milvus_client.insert_documents(kb_id % 32, chunk_data, collection_name=collection_name)
                    es_client.insert_documents(collection_name, chunk_data)
                    
                    # 更新文档的chunk计数
                    await DocumentManager.update_params(doc_id, {"chunk_cnt": doc.chunk_cnt + 1})
                    chunk_id = new_chunk_id
                
                return {
                    "status": True,
                    "chunk_id": chunk_id,
                    "operation": operation
                }
                
        except Exception as e:
            error_msg = f"切片管理失败: {str(e)}, doc_id={doc_id}, chunk_id={chunk_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return {"status": False, "message": error_msg}
        
    @staticmethod
    async def retrieval_chunks(
        kb_id: int,
        doc_ids: List[int],
        query: str,
        collection_name: str,
        top_k: int = 10,
        retrieval_type: str = 'vector',
        rerank_model: str = 'bge-reranker-v2-m3', 
        emb_model: str = 'bge-m3',
        score_threshold: float = 0.5,
    ) -> List[Dict]:
        """检索文档切片
        
        Args:
            kb_id: 知识库ID
            query: 查询文本
            top_k: 返回结果数量
            retrieval_type: 检索类型 (vector/keyword/hybrid)
            
        Returns:
            List[Dict]: 切片列表
        """
        try:
            milvus_client = MilvusClient()
            es_client = ElasticSearchClient()
            
            # 获取文本向量
            dense_emb, spare_emb = get_embedding(emb_model, [query])
            
            if retrieval_type == 'vector':
                doc_ids_str = '","'.join(map(str, doc_ids))
                expr = f'doc_id in ["{doc_ids_str}"]'
                results = milvus_client.search_emb_async(
                    partition_ids=[kb_id % 32],
                    dense_emb=dense_emb,
                    sparse_emb=spare_emb,
                    expr=expr,
                    collection_name=collection_name,
                    top_k=top_k
                )
                results, _ = rerank(rerank_model, query, results, score_threshold)
            elif retrieval_type == 'keyword':
                # 关键词检索
                results = es_client.search_by_full_text(
                    query_text=query,
                    doc_ids=doc_ids,
                    index_name=collection_name,
                    top_k=top_k
                )
                results, _ = rerank(rerank_model, query, results, score_threshold)
            else:
                # 混合检索
                doc_ids_str = '","'.join(map(str, doc_ids))
                expr = f'doc_id in ["{doc_ids_str}"]'
                vector_results = milvus_client.search_emb_async(
                    partition_ids=[kb_id % 32],
                    dense_emb=dense_emb,
                    sparse_emb=spare_emb,
                    expr=expr,
                    collection_name=collection_name,
                    top_k=top_k
                )
                keyword_results = es_client.search_by_full_text(
                    query_text=query,
                    doc_ids=doc_ids,
                    index_name=collection_name,
                    top_k=top_k
                )
                # 合并结果
                results = vector_results + keyword_results
                results, _ = rerank(rerank_model, query, results, score_threshold)
                # 按相关度排序并取前 top_k 个
                results = sorted(results, key=lambda x: x.meta_data.get('score', 0), reverse=True)[:top_k]
            
            return results
            
        except Exception as e:
            error_msg = f"切片检索失败: {str(e)}, kb_id={kb_id}, query={query}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None
    
    @staticmethod
    async def get_user_docs(
        user_id: int,
        page_no: int = 1,
        page_size: int = 10
    ) -> Dict:
        """获取用户文档列表
        
        Args:
            user_id: 用户ID
            page_no: 页码
            page_size: 每页数量
            
        Returns:
            Dict: 包含文档列表和总数的字典
        """
        try:
            async with async_session() as session:
                # 查询该用户的所有文档总数
                count_query = select(func.count()).select_from(RAGDocument).where(
                    RAGDocument.user_id == user_id,
                    RAGDocument.is_deleted == 0
                )
                total = await session.scalar(count_query)
                
                # 计算分页偏移量
                offset = (page_no - 1) * page_size
                
                # 查询分页数据
                query = select(RAGDocument).where(
                    RAGDocument.user_id == user_id,
                    RAGDocument.is_deleted == 0
                ).offset(offset).limit(page_size)
                
                result = await session.execute(query)
                doc_list = result.scalars().all()
                
                # 整理返回数据
                doc_list_result = []
                for doc in doc_list:
                    doc_dict = {
                        "doc_id": doc.doc_id,
                        "doc_name": doc.doc_name,
                        "doc_type": doc.doc_type,
                        "doc_status": doc.doc_status,
                        "kb_id": doc.kb_id,
                        "chunk_cnt": doc.chunk_cnt,
                        "create_time": doc.create_time.strftime("%Y-%m-%dT%H:%M:%SZ") if doc.create_time else None
                    }
                    doc_list_result.append(doc_dict)
                
                return {
                    "list": doc_list_result,
                    "total": total
                }
        except Exception as e:
            error_msg = f"获取用户文档列表失败: {str(e)}, user_id={user_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None

    @staticmethod
    async def preview_chunks(
        kb_id: int,
        doc_id: str,
    ) -> List[Dict]:
        """预览文档的所有切片
        
        Args:
            kb_id: 知识库ID
            doc_id: 文档ID
            
        Returns:
            List[Dict]: 切片列表
        """
        try:
            milvus_client = MilvusClient()
            
            # 使用 search_by_doc_id 获取所有切片
            chunks = milvus_client.search_by_doc_id(
                partition_id=kb_id % 32,
                doc_id=doc_id
            )
            
            if not chunks:
                logger.warning(f"未找到文档切片: kb_id={kb_id}, doc_id={doc_id}")
                return []
                
            return chunks
            
        except Exception as e:
            error_msg = f"预览文档切片失败: {str(e)}, kb_id={kb_id}, doc_id={doc_id}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return None


async def agent_exec_code(code: str) -> str:
    """
    Executes the provided Python code and returns the output.
    
    Args:
        code (str): The Python code to be executed.
        
    Returns:
        str: The output of the executed code or an error message if execution fails.
    """
    try:
        # Execute the code and capture the output
        local_vars = {}
        exec(code, {}, local_vars)
        return str(local_vars)
    except Exception as e:
        return f"Error executing code: {str(e)}"
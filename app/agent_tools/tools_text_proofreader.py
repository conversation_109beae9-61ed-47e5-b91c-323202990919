
import json
import aiohttp
import config


async def agent_text_proofreader_stream(user_query, file_content, user_id, stream_writer):
    try:
        segments = file_content.split('\n\n')
        if not segments:
            await stream_writer({'content': '文档内容为空'})
            return

        post_headers = {
            "Authorization": f"Bearer {config.API_KEY}",
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as http_session:
            for idx, segment in enumerate(segments):
                # 构造 prompt
                history_messages = [
                    {"role": "system", "content": config.TEXT_PROOFREDER_PROMPT_TEMPLATE.format(raw_text=segment)},
                    {"role": "user", "content": user_query}
                ]
                post_data = {
                    "model": config.LLM_MODEL,
                    "messages": history_messages,
                    "temperature": 0.2,
                    "stream": True
                }

                try:
                    async with http_session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=600) as response:
                        if response.status == 200:
                            async for chunk in response.content.iter_chunked(1024):
                                chunk_str = chunk.decode('utf-8')
                                lines = chunk_str.split('\n')
                                for line in lines:
                                    line = line.strip()
                                    if line.startswith('data: '):
                                        data_str = line[6:]
                                        if data_str == '[DONE]':
                                            break
                                        try:
                                            data = json.loads(data_str)
                                            delta = data['choices'][0].get('delta', {})
                                            if 'content' in delta:
                                                await stream_writer({
                                                    'content': delta['content']
                                                })
                                        except json.JSONDecodeError:
                                            continue
                        else:
                            await stream_writer({'content': f'API请求失败: {response.status}'})
                except Exception as e:
                    await stream_writer({'content': f'第 {idx+1} 段处理失败: {str(e)}'})
                
                await stream_writer({'content': '\n\n'})

    except Exception as e:
        await stream_writer({'content': f'文本校对失败: {str(e)}'})


if __name__ == '__main__':
    async def async_print(msg):
        print(msg)

    file_content = """
    今天天气真好！我们去公园玩吧，小明说：“我带了风筝和零食！”大家都很高兴、准备出发的时候。他却说："我不去了"，然后就转身走了，真是让人摸不着头脑？这叫什么，友谊的小船说翻就翻吗！
    
    今天、天气非常好！我和朋友约好去爬山，他说：“我们早上八点在地铁口见面。”结果我等了半小时他才来，真是让我哭笑不得啊！
    """
    async def test_stream():
        await agent_text_proofreader_stream(
            user_query="请校对文本",
            file_content=file_content,
            user_id=1,
            stream_writer=async_print
        )

    import asyncio
    asyncio.run(test_stream())
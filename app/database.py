# app/database.py

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import datetime
import os
import config

# 数据库连接 URI（根据你的实际配置修改）
user = config.DB_CONFIG['user']
password = config.DB_CONFIG['password']
host = config.DB_CONFIG['host']
port = config.DB_CONFIG['port']
db = config.DB_CONFIG['db']

DATABASE_URI = f"mysql+pymysql://{user}:{password}@{host}:{port}/{db}"
# 创建数据库引擎
engine = create_engine(DATABASE_URI, echo=True)  # echo=True 可打印 SQL 日志，生产环境建议关闭

# 创建 SessionLocal 类
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建 db_session 实例
db_session = SessionLocal()

# 声明基类
Base = declarative_base()

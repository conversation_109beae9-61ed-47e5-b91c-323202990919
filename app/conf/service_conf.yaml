rag_server:
  host: '0.0.0.0'
  port: 9989
mysql:
  db: 'rag'
  user: 'root'
  password: 'infini_rag_flow'
  host: '**************'
  port: 5455
  max_connections: 100
  stale_timeout: 30
minio:
  user: 'rag_flow'
  password: 'infini_rag_flow'
  host: '**************:9000'
  bucket: 'rag'
milvus:
  uri: "http://**************:19530"
es:
  host: 'http://**************:1200'
  user: 'elastic'
  password: 'infini_rag_flow'
embedding:
  bge-m3: '/home/<USER>/huggingface.co/BAAI/bge-m3'
rerank:
  bge-reranker-v2-m3: '/home/<USER>/huggingface.co/BAAI/bge-reranker-v2-m3'

#embedding:
#  bge-m3: '/Users/<USER>/jisuansuo/BAAI/bge-m3'
#rerank:
#  bge-reranker-v2-m3: '/Users/<USER>/jisuansuo/BAAI/bge-reranker-v2-m3'
llm:
  api_key: '123456'
  base_url: 'http://**************:8000/v1/chat/completions'
  model: 'Qwen2.5-72B-Instruct-GPTQ-Int4'
  endpoint_id: 'ep-m-20250221110813-rvpzw'
zhiyu:
  #织语开放测试平台
  app_key: 'dfUQR8ucGCFSO6eHFLQGvGY3AK5sSMJ6'
  app_id: '36772066938397777'
  access_token: ''
  zhiyu_api_base_url: 'http://*************:8888'
  #访问知识库平台自己的接口
  chat_completion_api_url: 'http://**************:9989/api/v1/chat/completion/stream'
  chat_completion_api_token:  'a23sfxasf2323sfsadfs9923rkjsafhds'
  user_id: '1' #应用默认的用户ID

sso:
  login_url: 'http://*************:30000/#/login?redirect=/portal'
  logout_url: 'http://172.17.110.79:31602/api/internal/auth/logout'
  api_base_url: 'http://172.17.110.79:31602/'
  system_code: 'project_ai'
model_num: 2
chatbi:
  api_key: '123456'
  base_url: 'http://**************:5670'
  create_url: 'http://**************:5670/api/v1/app/create'
  edit_url: 'http://**************:5670/api/v1/app/edit'

# 添加外部数据源API配置
external_datasource_api:
  url: 'http://**************:5670'  # 替换为实际的外部API地址
  bearer_token: '123456'    # 替换为实际的认证token

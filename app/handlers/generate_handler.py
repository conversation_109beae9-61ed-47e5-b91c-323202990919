import tornado.web
import json
import aiohttp
from utils.response import make_response
from utils.storage import StorageManager  # 添加这行导入
import config
import config_prompt
import time
import logging
import re
import os
from utils.minio import download_file
from handlers.base_handler import BaseHandler
from utils.extractor import extract_text
from models import async_session, Article, User, ArticleTemplate
import datetime
import asyncio
from sqlalchemy import select, func, and_
from typing import List, Dict, Any
import hashlib


async def retrive_by_uploaded_file(file_object_list):
    # 下载
    start_ts = int(time.time()*1000)
    for file_object_path in file_object_list:
        local_file_path = os.path.join(config.TEMP_DIR, os.path.basename(file_object_path))  
        await download_file(file_object_path, local_file_path)
    end_ts = int(time.time()*1000)

    # 解析
    file_md_list = []
    start_ts = end_ts
    for file_object_path in file_object_list:
        local_file_path = os.path.join(config.TEMP_DIR, os.path.basename(file_object_path))  
        # md_content, content_list_content, tmp_pdf_path = await asyncio.to_thread(parse_doc, local_file_path)
        # file_md_list.append(md_content)
        # os.remove(tmp_pdf_path)
        # if tmp_pdf_path != local_file_path:
        #     os.remove(local_file_path)
        text = extract_text(local_file_path)
        file_md_list.append(text)
        os.remove(local_file_path)
    end_ts = int(time.time()*1000)

    sys_propmt = "\n".join(file_md_list)
    # 如果文档内容过长，则截取前6000个字符+后6000个字符
    if len(sys_propmt) > 12000:
        sys_propmt = sys_propmt[:6000] + "......" + sys_propmt[-6000:]
    return sys_propmt

class GenerateOutline(BaseHandler):
    def initialize(self):
        self.storage_manager = StorageManager()

    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'doc_topic' not in data or 'keywords' not in data:
                make_response(self, 400, "参数错误")
                return
            
            doc_topic = data['doc_topic']
            keywords = data['keywords']
            file_objects = data.get('file_objects', [])
            
            # 构建关键字字符串
            keywords_str = "；".join(keywords) if isinstance(keywords, list) else keywords
            
            # 使用配置文件中的提示词模板
            prompt = config_prompt.GENERATE_OUTLINE_PROMPT.format(
                doc_topic=doc_topic,
                keywords_str=keywords_str,
                Documents=await retrive_by_uploaded_file(file_objects)
            )
            # 调用大模型生成大纲
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.7
                    }
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=30) as response:
                        response.raise_for_status()
                        result = await response.json()
                        outline = result["choices"][0]["message"]["content"]
                        # 获取tokens数量
                        input_tokens = result.get("usage", {}).get("prompt_tokens", 0)
                        output_tokens = result.get("usage", {}).get("completion_tokens", 0)
                end_ts = int(time.time()*1000)
                logging.info(f"Generate outline took {end_ts - start_ts}ms")
                
                # 尝试解析大模型返回的JSON
                try:
                    # 提取JSON部分（如果大模型返回的不只是JSON）
                    json_match = re.search(r'\{[\s\S]*\}', outline)
                    if json_match:
                        outline_json = json.loads(json_match.group(0))
                    else:
                        outline_json = json.loads(outline)
                    
                    # 保存到数据库
                    storage_info = await self.storage_manager.save_article_outline(
                        user_id=user_id,
                        doc_topic=doc_topic,
                        keywords=keywords_str,
                        outline=outline,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens
                    )
                    
                    # 返回响应 
                    outline_json["article_id"] = storage_info["id"]
                    make_response(self, 200, "success", outline_json)
                except json.JSONDecodeError as e:
                    # 如果无法解析为JSON，仍然保存原始文本
                    storage_info = await self.storage_manager.save_article_outline(
                        user_id=user_id,
                        doc_topic=doc_topic,
                        keywords=keywords_str,
                        outline=outline,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens
                    )
                    make_response(self, 200, "success", {
                        "raw_outline": outline,
                        "article_id": storage_info["id"]
                    })
                    
            except Exception as e:
                make_response(self, 500, f"LLM请求失败: {str(e)}")
                return
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class GenerateArticle(BaseHandler):
    def initialize(self):
        self.storage_manager = StorageManager()

    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'doc_topic' not in data or 'outline' not in data or 'article_id' not in data:
                make_response(self, 400, "参数错误")
                return
            
            doc_topic = data['doc_topic']
            outline = data['outline']
            article_id = data['article_id']
            length = data.get('length', 2000)
            keywords = data.get('keywords', '')
            wcount = data.get('wcount', length)
            file_objects = data.get('file_objects', [])
            
            # 将outline转换为字符串（如果是JSON对象）
            if isinstance(outline, dict):
                outline_str = json.dumps(outline, ensure_ascii=False)
            else:
                outline_str = outline
            
            # 使用配置文件中的提示词模板
            prompt = config_prompt.GENERATE_ARTICLE_PROMPT.format(
                doc_topic=doc_topic,
                outline_str=outline_str,
                length=length,
                Documents=await retrive_by_uploaded_file(file_objects)
            )

            # 调用大模型生成文章
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.7
                    }
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
                        response.raise_for_status()
                        result = await response.json()
                        article = result["choices"][0]["message"]["content"]
                        input_tokens = result.get("usage", {}).get("prompt_tokens", 0)
                        output_tokens = result.get("usage", {}).get("completion_tokens", 0)
                end_ts = int(time.time()*1000)
                logging.info(f"Generate article took {end_ts - start_ts}ms")
                
                try:
                    # 确保 keywords 是字符串类型
                    keywords_str = '；'.join(keywords) if isinstance(keywords, list) else keywords
                    
                    storage_info = await self.storage_manager.update_article(
                        id=article_id,
                        doc_topic=doc_topic,
                        keywords=keywords_str,
                        wcount=wcount,
                        outline=outline_str,
                        content=article,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens
                    )
                    response_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "article": article,
                            "article_id": article_id,
                            "storage": storage_info
                        }
                    }
                except Exception as storage_error:
                    logging.error(f"更新文章失败: {str(storage_error)}")
                    response_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "article": article,
                            "article_id": article_id,
                            "storage_error": str(storage_error)
                        }
                    }
                
                # 发送最终完整响应
                self.write(f"final: {json.dumps(response_data, ensure_ascii=False)}\n\n")
                await self.finish()
                    
            except Exception as e:
                make_response(self, 500, f"LLM请求失败: {str(e)}")
                return
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class GenerateArticleStream(BaseHandler):
    def initialize(self):
        self.storage_manager = StorageManager()

    # 设置流式响应头
    def set_default_headers(self):
        self.set_header("Content-Type", "text/event-stream")
        self.set_header("Cache-Control", "no-cache")
        self.set_header("Connection", "keep-alive")

    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'doc_topic' not in data or 'outline' not in data or 'article_id' not in data:
                make_response(self, 400, "参数错误")
                return
            
            doc_topic = data['doc_topic']
            outline = data['outline']
            article_id = data['article_id']
            length = data.get('length', 2000)
            keywords = data.get('keywords', '')
            wcount = data.get('wcount', length)
            file_objects = data.get('file_objects', [])
            keywords = "；".join(keywords) if isinstance(keywords, list) else keywords
            
            # 将outline转换为字符串（如果是JSON对象）
            if isinstance(outline, dict):
                outline_str = json.dumps(outline, ensure_ascii=False)
            else:
                outline_str = outline
            
            # 使用配置文件中的提示词模板
            prompt = config_prompt.GENERATE_ARTICLE_PROMPT.format(
                doc_topic=doc_topic,
                outline_str=outline_str,
                length=length,
                Documents=await retrive_by_uploaded_file(file_objects)
            )
            
            article = ""
            input_tokens = 0
            output_tokens = 0
            
            # 流式调用大模型生成文章
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.7,
                        "stream": True  # 启用流式模式
                    }
                    
                    # 估算输入tokens
                    input_tokens = len(prompt) // 4  # 简单估算
                    
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
                        # 逐块读取流式响应
                        async for chunk in response.content:
                            if self.request.connection.stream.closed():
                                break  # 连接中断时终止
                            
                            # 解析大模型返回的chunk
                            if chunk.startswith(b'data: '):
                                try:
                                    chunk = chunk.decode('utf-8')
                                    if chunk.startswith('data: [DONE]'):
                                        break
                                    if not chunk.startswith('data: '):
                                        continue
                                    json_data = json.loads(chunk[6:].strip())
                                    content = json_data['choices'][0]['delta'].get('content', '')
                                    if content:
                                        article += content
                                        output_tokens += 1  # 简单累计输出tokens
                                        # 流式输出核心代码
                                        self.write(f"data: {json.dumps({'content': content})}\n\n")
                                        await self.flush()  # 必须立即刷新缓冲区‌
                                except json.JSONDecodeError:
                                    self.write(f"data: {json.dumps({'error': 'JSONDecodeError'})}\n\n")
                                    await self.flush()
                
                end_ts = int(time.time()*1000)
                logging.info(f"Generate article stream took {end_ts - start_ts}ms")
                
                # 更新数据库
                try:
                    storage_info = await self.storage_manager.update_article(
                        id=article_id,
                        doc_topic=doc_topic,
                        keywords=keywords,
                        wcount=wcount,
                        outline=outline_str,
                        content=article,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens
                    )
                    response_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "article": article,
                            "article_id": article_id,
                            "storage": storage_info
                        }
                    }
                except Exception as storage_error:
                    logging.error(f"更新文章失败: {str(storage_error)}")
                    response_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "article": article,
                            "article_id": article_id,
                            "storage_error": str(storage_error)
                        }
                    }
                
                # 发送最终完整响应
                self.write(f"final: {json.dumps(response_data, ensure_ascii=False)}\n\n")
                await self.finish()
                    
            except Exception as e:
                if not self._finished:  # Check if response is already finished
                    self.write(f"data: {json.dumps({'error': str(e)})}\n\n")
                    await self.flush()
                logging.error(f"Error in generate handler: {str(e)}")
                return
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            self.set_status(500)
            self.write(f"data: {json.dumps({'error': str(e)})}\n\n")

class GenerateArticleStreamWithTemplate(BaseHandler):
    def initialize(self):
        self.storage_manager = StorageManager()

    # 设置流式响应头
    def set_default_headers(self):
        self.set_header("Content-Type", "text/event-stream")
        self.set_header("Cache-Control", "no-cache")
        self.set_header("Connection", "keep-alive")

    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'doc_topic' not in data or 'template_id' not in data:
                make_response(self, 400, "参数错误")
                return
            
            doc_topic = data['doc_topic']
            template_id = data['template_id']
            length = data.get('length', 2000)
            keywords = data.get('keywords', '')
            wcount = data.get('wcount', length)
            file_objects = data.get('file_objects', [])

            keywords = "；".join(keywords) if isinstance(keywords, list) else keywords
            template_id = int(template_id)
            
            # 从数据库获取模板内容
            async with async_session() as session:
                template = await session.get(ArticleTemplate, template_id)
                if not template or template.is_deleted == 1:
                    logging.error(f"模板不存在或已删除: {template_id}")
                    make_response(self, 404, "模板不存在")
                    return
                
                template_content = template.template_content
                template_type = template.template_type
            
            # 构建outline字符串
            outline_str = f"template_id={template_id}"
            
            # 先保存文章大纲，获取article_id
            article_info = await self.storage_manager.save_article_outline(
                user_id=user_id,
                doc_topic=doc_topic,
                keywords=keywords,
                outline=outline_str
            )
            article_id = article_info["id"]
            
            # 使用配置文件中的提示词模板
            prompt = config_prompt.GENERATE_ARTICLE_WITH_TEMPLATE_PROMPT.format(
                doc_topic=doc_topic,
                template_content=template_content,
                template_type=template_type,
                keywords_str=keywords,
                length=length,
                Documents=await retrive_by_uploaded_file(file_objects)
            )
            
            article = ""
            input_tokens = 0
            output_tokens = 0
            
            # 流式调用大模型生成文章
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.7,
                        "stream": True  # 启用流式模式
                    }
                    
                    # 估算输入tokens
                    input_tokens = len(prompt) // 4  # 简单估算
                    
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
                        # 逐块读取流式响应
                        async for chunk in response.content:
                            if self.request.connection.stream.closed():
                                break  # 连接中断时终止
                            
                            # 解析大模型返回的chunk
                            if chunk.startswith(b'data: '):
                                try:
                                    chunk = chunk.decode('utf-8')
                                    if chunk.startswith('data: [DONE]'):
                                        break
                                    if not chunk.startswith('data: '):
                                        continue
                                    json_data = json.loads(chunk[6:].strip())
                                    content = json_data['choices'][0]['delta'].get('content', '')
                                    if content:
                                        article += content
                                        output_tokens += 1  # 简单累计输出tokens
                                        # 流式输出核心代码
                                        self.write(f"data: {json.dumps({'content': content})}\n\n")
                                        await self.flush()  # 必须立即刷新缓冲区‌
                                except json.JSONDecodeError:
                                    self.write(f"data: {json.dumps({'error': 'JSONDecodeError'})}\n\n")
                                    await self.flush()
                
                end_ts = int(time.time()*1000)
                logging.info(f"Generate article stream took {end_ts - start_ts}ms")
                
            except Exception as e:
                if not self._finished:  # Check if response is already finished
                    self.write(f"data: {json.dumps({'error': str(e)})}\n\n")
                    await self.flush()
                logging.error(f"Error in generate handler: {e}")
                # return
            
            # 写入数据库
            try:
                storage_info = await self.storage_manager.update_article(
                    id=article_id,  # 添加id参数
                    doc_topic=doc_topic,
                    keywords=keywords,
                    wcount=wcount,
                    outline=outline_str,  # 使用包含template_id的outline
                    content=article,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens
                )
                response_data = {
                    "code": 200,
                    "message": "success",
                    "data": {
                        "article": article,
                        "article_id": storage_info["id"],
                        "storage": storage_info
                    }
                }
            except Exception as storage_error:
                logging.error(f"更新文章失败: {str(storage_error)}")
                response_data = {
                    "code": 200,
                    "message": "success",
                    "data": {
                        "article": article,
                        "article_id": article_id,
                        "storage_error": str(storage_error)
                    }
                }
            
            # 发送最终完整响应
            self.write(f"final: {json.dumps(response_data, ensure_ascii=False)}\n\n")
            await self.finish()

        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            self.set_status(500)
            self.write(f"data: {json.dumps({'error': str(e)})}\n\n")

class ManagerArticle(BaseHandler):
    """文章管理处理器"""
    def initialize(self):
        self.storage_manager = StorageManager()
    
    async def get(self):
        """获取文章列表或单个文章详情"""
        try:
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            article_id = self.get_argument('article_id', None)
            
            async with async_session() as session:
                if article_id:
                    # 获取单个文章详情
                    article = await session.get(Article, article_id)
                    if not article or article.is_deleted == 1:
                        make_response(self, 404, "文章不存在")
                        return
                    
                    if article.user_id != user_id:
                        make_response(self, 403, "无权访问该文章")
                        return
                    try:
                        file_content = await retrive_by_uploaded_file([article.file_path])
                    except Exception as e:
                        logging.error(f"获取文件内容失败: {str(e)}")
                        file_content = "文件内容获取失败"

                    result = {
                        "article_id": article.id,
                        "doc_topic": article.doc_topic,
                        "keywords": article.keywords,
                        "content": file_content,
                        "create_time": article.create_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "update_time": article.update_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    }
                    make_response(self, 200, "success", result)
                else:
                    # 获取文章列表
                    query = select(Article).where(
                        and_(
                            Article.user_id == user_id,
                            Article.is_deleted == 0,
                            Article.file_name.isnot(None),  # 过滤file_name为NULL的记录
                            Article.file_name != ""        # 过滤file_name为空字符串的记录
                        )
                    ).order_by(Article.create_time.desc())
                    
                    result = await session.execute(query)
                    articles = result.scalars().all()
                    
                    article_list = []
                    for article in articles:
                        # 从minio获取文件内容
                        file_content = ""
                        if article.file_path:
                            try:
                                file_content = await retrive_by_uploaded_file([article.file_path])
                            except Exception as e:
                                logging.error(f"获取文件内容失败: {str(e)}")
                                file_content = "文件内容获取失败"
                        
                        article_dict = {
                            "article_id": article.id,
                            "doc_topic": article.doc_topic,
                            "keywords": article.keywords,
                            "create_time": article.create_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                            "update_time": article.update_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                            "content": file_content  # 添加文件内容
                        }
                        article_list.append(article_dict)
                    
                    make_response(self, 200, "success", {
                        "total": len(article_list),
                        "articles": article_list
                    })
                    
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

    async def post(self):
        """更新文章内容"""
        try:
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'article_id' not in data or 'content' not in data:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            article_id = data['article_id']
            content = data['content']
            
            async with async_session() as session:
                article = await session.get(Article, article_id)
                if not article or article.is_deleted == 1:
                    make_response(self, 404, "文章不存在")
                    return
                
                if article.user_id != user_id:
                    make_response(self, 403, "无权修改该文章")
                    return
                
                # 使用 storage_manager 更新文章内容
                try:
                    storage_info = await self.storage_manager.update_article(
                        id=article_id,
                        doc_topic=article.doc_topic,
                        keywords=article.keywords,
                        wcount=article.wcount,
                        outline=article.outline,
                        content=content,  # 新的内容
                        input_tokens=article.input_tokens,
                        output_tokens=article.output_tokens
                    )
                    
                    make_response(self, 200, "更新成功", {
                        "article_id": article_id,
                        "update_time": datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "storage": storage_info
                    })
                except Exception as e:
                    logging.error(f"更新文章内容失败: {str(e)}")
                    make_response(self, 500, f"更新文章失败: {str(e)}")
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

    
class DeleteArticle(BaseHandler):
    """文章删除处理器"""
        
    async def post(self):
        """删除文章"""
        try:
            user_id = self.get_user_id()
            if user_id is None:
                return
                
            data = json.loads(self.request.body)
            if not data or 'article_id' not in data:
                make_response(self, 400, "参数错误：缺少文章ID")
                return
                
            article_id = data['article_id']
                
            async with async_session() as session:
                article = await session.get(Article, article_id)
                if not article or article.is_deleted == 1:
                    make_response(self, 404, "文章不存在")
                    return
                    
                if article.user_id != user_id:
                    make_response(self, 403, "无权删除该文章")
                    return
                    
                article.is_deleted = 1
                article.update_time = datetime.datetime.utcnow()
                    
                await session.commit()
                    
                make_response(self, 200, "删除成功", {
                    "article_id": article_id
                })
                    
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")


class LongArticleGenerator:
    """长文章生成器 - 支持数万字文章生成"""

    def __init__(self):
        self.storage_manager = StorageManager()
        self.max_section_length = 3000  # 每个章节最大字数
        self.max_concurrent_sections = 3  # 最大并发生成章节数
        self.retry_attempts = 3  # 重试次数

    async def parse_outline_to_sections(self, outline: Dict[str, Any]) -> List[Dict[str, Any]]:
        """将大纲解析为独立的章节"""
        sections = []

        if isinstance(outline, dict) and "primary" in outline:
            for i, primary in enumerate(outline["primary"]):
                # 一级标题作为主要章节
                section = {
                    "section_id": f"section_{i+1}",
                    "title": primary.get("title", f"第{i+1}章"),
                    "level": 1,
                    "content_outline": primary,
                    "estimated_length": self.max_section_length
                }
                sections.append(section)

                # 如果有二级标题，可以作为子章节
                if "secondary" in primary:
                    for j, secondary in enumerate(primary["secondary"]):
                        sub_section = {
                            "section_id": f"section_{i+1}_{j+1}",
                            "title": secondary.get("title", f"第{i+1}.{j+1}节"),
                            "level": 2,
                            "parent_section": f"section_{i+1}",
                            "content_outline": secondary,
                            "estimated_length": self.max_section_length // 2
                        }
                        sections.append(sub_section)
        else:
            # 如果大纲格式不标准，创建默认章节
            sections = [
                {
                    "section_id": "section_1",
                    "title": "主要内容",
                    "level": 1,
                    "content_outline": outline,
                    "estimated_length": self.max_section_length
                }
            ]

        return sections

    async def generate_section_content(self, section: Dict[str, Any], doc_topic: str,
                                     documents: str, session: aiohttp.ClientSession) -> Dict[str, Any]:
        """生成单个章节的内容"""
        section_prompt = config_prompt.GENERATE_LONG_ARTICLE_SECTION_PROMPT.format(
            doc_topic=doc_topic,
            section_title=section['title'],
            section_outline=json.dumps(section['content_outline'], ensure_ascii=False),
            expected_length=section['estimated_length'],
            documents=documents
        )

        for attempt in range(self.retry_attempts):
            try:
                post_data = {
                    "model": config.LLM_MODEL,
                    "messages": [{"role": "user", "content": section_prompt}],
                    "temperature": 0.7,
                    "max_tokens": 4000  # 增加最大token数
                }

                async with session.post(
                    config.BASE_URL,
                    headers={
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    },
                    json=post_data,
                    timeout=120  # 增加超时时间
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    content = result["choices"][0]["message"]["content"]

                    return {
                        "section_id": section["section_id"],
                        "title": section["title"],
                        "content": content,
                        "length": len(content),
                        "input_tokens": result.get("usage", {}).get("prompt_tokens", 0),
                        "output_tokens": result.get("usage", {}).get("completion_tokens", 0),
                        "success": True
                    }

            except Exception as e:
                logging.error(f"生成章节 {section['section_id']} 失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == self.retry_attempts - 1:
                    return {
                        "section_id": section["section_id"],
                        "title": section["title"],
                        "content": f"# {section['title']}\n\n[生成失败，请重试]",
                        "length": 0,
                        "input_tokens": 0,
                        "output_tokens": 0,
                        "success": False,
                        "error": str(e)
                    }
                await asyncio.sleep(2 ** attempt)  # 指数退避

    async def generate_long_article(self, doc_topic: str, outline: Dict[str, Any],
                                  documents: str, target_length: int = 10000) -> Dict[str, Any]:
        """生成长文章的主要方法"""
        start_time = time.time()

        # 解析大纲为章节
        sections = await self.parse_outline_to_sections(outline)

        # 根据目标长度调整章节长度
        if target_length > 5000:
            section_length = min(target_length // len(sections), 5000)
            for section in sections:
                section["estimated_length"] = section_length

        # 并发生成章节内容
        generated_sections = []
        total_input_tokens = 0
        total_output_tokens = 0

        async with aiohttp.ClientSession() as session:
            # 分批并发处理章节
            for i in range(0, len(sections), self.max_concurrent_sections):
                batch = sections[i:i + self.max_concurrent_sections]

                # 并发生成当前批次的章节
                tasks = [
                    self.generate_section_content(section, doc_topic, documents, session)
                    for section in batch
                ]

                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                for result in batch_results:
                    if isinstance(result, Exception):
                        logging.error(f"章节生成异常: {str(result)}")
                        continue

                    generated_sections.append(result)
                    total_input_tokens += result.get("input_tokens", 0)
                    total_output_tokens += result.get("output_tokens", 0)

        # 组装最终文章
        article_content = self.assemble_article(generated_sections, doc_topic)

        end_time = time.time()

        return {
            "content": article_content,
            "sections": generated_sections,
            "total_length": len(article_content),
            "section_count": len(generated_sections),
            "input_tokens": total_input_tokens,
            "output_tokens": total_output_tokens,
            "generation_time": end_time - start_time,
            "success": True
        }

    def assemble_article(self, sections: List[Dict[str, Any]], doc_topic: str) -> str:
        """组装章节为完整文章"""
        article_parts = [f"# {doc_topic}\n\n"]

        # 按section_id排序
        sections.sort(key=lambda x: x["section_id"])

        for section in sections:
            if section.get("success", False):
                article_parts.append(section["content"])
                article_parts.append("\n\n")

        return "".join(article_parts)


class GenerateLongArticleStream(BaseHandler):
    """长文章流式生成处理器 - 支持数万字文章"""

    def initialize(self):
        self.storage_manager = StorageManager()
        self.long_generator = LongArticleGenerator()

    def set_default_headers(self):
        self.set_header("Content-Type", "text/event-stream")
        self.set_header("Cache-Control", "no-cache")
        self.set_header("Connection", "keep-alive")

    async def post(self):
        try:
            # 用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return

            data = json.loads(self.request.body)
            if not data or 'doc_topic' not in data or 'outline' not in data or 'article_id' not in data:
                make_response(self, 400, "参数错误")
                return

            doc_topic = data['doc_topic']
            outline = data['outline']
            article_id = data['article_id']
            length = data.get('length', 10000)  # 默认1万字
            keywords = data.get('keywords', '')
            wcount = data.get('wcount', length)
            file_objects = data.get('file_objects', [])
            keywords = "；".join(keywords) if isinstance(keywords, list) else keywords

            # 将outline转换为字典（如果是字符串）
            if isinstance(outline, str):
                try:
                    outline = json.loads(outline)
                except json.JSONDecodeError:
                    outline = {"primary": [{"title": "主要内容"}]}

            # 获取参考文档
            documents = await retrive_by_uploaded_file(file_objects)

            # 发送开始信号
            self.write(f"data: {json.dumps({'status': 'started', 'message': '开始生成长文章...'})}\n\n")
            await self.flush()

            try:
                # 解析大纲为章节
                sections = await self.long_generator.parse_outline_to_sections(outline)

                # 发送章节信息
                self.write(f"data: {json.dumps({'status': 'sections_parsed', 'sections': len(sections), 'message': f'已解析出{len(sections)}个章节'})}\n\n")
                await self.flush()

                # 根据目标长度调整章节长度
                if length > 5000:
                    section_length = min(length // len(sections), 5000)
                    for section in sections:
                        section["estimated_length"] = section_length

                generated_sections = []
                total_input_tokens = 0
                total_output_tokens = 0
                article_content = f"# {doc_topic}\n\n"

                # 逐个生成章节（改为串行以便实时反馈）
                async with aiohttp.ClientSession() as session:
                    for i, section in enumerate(sections):
                        # 发送当前章节生成状态
                        status_msg = {
                            'status': 'generating_section',
                            'section_index': i+1,
                            'section_title': section['title'],
                            'message': f'正在生成第{i+1}章节: {section["title"]}'
                        }
                        self.write(f"data: {json.dumps(status_msg)}\n\n")
                        await self.flush()

                        # 生成章节内容
                        section_result = await self.long_generator.generate_section_content(
                            section, doc_topic, documents, session
                        )

                        if section_result.get("success", False):
                            generated_sections.append(section_result)
                            total_input_tokens += section_result.get("input_tokens", 0)
                            total_output_tokens += section_result.get("output_tokens", 0)

                            # 将章节内容添加到文章中
                            article_content += section_result["content"] + "\n\n"

                            # 发送章节完成状态和内容
                            complete_msg = {
                                'status': 'section_completed',
                                'section_index': i+1,
                                'section_title': section['title'],
                                'content': section_result['content'],
                                'length': section_result['length']
                            }
                            self.write(f"data: {json.dumps(complete_msg)}\n\n")
                            await self.flush()
                        else:
                            # 发送章节失败状态
                            error_msg = {
                                'status': 'section_failed',
                                'section_index': i+1,
                                'section_title': section['title'],
                                'error': section_result.get('error', '未知错误')
                            }
                            self.write(f"data: {json.dumps(error_msg)}\n\n")
                            await self.flush()

                # 发送生成完成状态
                completion_msg = {
                    'status': 'generation_completed',
                    'total_length': len(article_content),
                    'sections_completed': len(generated_sections),
                    'message': '文章生成完成'
                }
                self.write(f"data: {json.dumps(completion_msg)}\n\n")
                await self.flush()

                # 更新数据库
                try:
                    storage_info = await self.storage_manager.update_article(
                        id=article_id,
                        doc_topic=doc_topic,
                        keywords=keywords,
                        wcount=wcount,
                        outline=json.dumps(outline, ensure_ascii=False),
                        content=article_content,
                        input_tokens=total_input_tokens,
                        output_tokens=total_output_tokens
                    )

                    response_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "article": article_content,
                            "article_id": storage_info["id"],
                            "total_length": len(article_content),
                            "sections_count": len(generated_sections),
                            "input_tokens": total_input_tokens,
                            "output_tokens": total_output_tokens,
                            "storage": storage_info
                        }
                    }
                except Exception as storage_error:
                    logging.error(f"更新文章失败: {str(storage_error)}")
                    response_data = {
                        "code": 200,
                        "message": "success",
                        "data": {
                            "article": article_content,
                            "article_id": article_id,
                            "total_length": len(article_content),
                            "sections_count": len(generated_sections),
                            "input_tokens": total_input_tokens,
                            "output_tokens": total_output_tokens,
                            "storage_error": str(storage_error)
                        }
                    }

                # 发送最终完整响应
                self.write(f"final: {json.dumps(response_data, ensure_ascii=False)}\n\n")
                await self.finish()

            except Exception as e:
                if not self._finished:
                    error_msg = {'status': 'error', 'error': str(e)}
                    self.write(f"data: {json.dumps(error_msg)}\n\n")
                    await self.flush()
                logging.error(f"长文章生成错误: {str(e)}")
                return

        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            self.set_status(500)
            error_msg = {'status': 'error', 'error': str(e)}
            self.write(f"data: {json.dumps(error_msg)}\n\n")
import tornado.web
import json
import datetime
import logging
from sqlalchemy import update, select, func, delete, and_, or_

from utils.response import make_response
from handlers.base_handler import BaseHandler
from models.knowledge_base import KnowledgeBase
from models.user import User
from models import async_session
from models.kb_share_relation import KBShareRelation
from utils.emb import EMB_MODELS
from utils.rerank import RERANK_MODELS
from models.data_control import DataControl

KB_DATA_TYPE = "知识库"


# 在 KnowledgeBaseCreateHandler 中添加 is_deleted 字段
class KnowledgeBaseCreateHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_name' not in data:
                make_response(self, 400, "参数错误：缺少知识库名称")
                return
            
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            # 获取参数，设置默认值
            kb_name = data.get('kb_name')
            kb_parse_strategy = data.get('kb_parse_strategy', 'normal')
            kb_auth_level = data.get('kb_auth_level', 'employee')
            emb_model = data.get('emb_model', 'bge-m3')
            rerank_model = data.get('rerank_model', 'bge-reranker-v2-m3')
            dt_dept_code = data.get('dt_dept_code', None)

            if not dt_dept_code:
                make_response(self, 400, "参数错误：缺少部门编码")
                return
            if emb_model not in EMB_MODELS:
                make_response(self, 400, "参数错误：不支持的嵌入模型")
                return
            if rerank_model not in RERANK_MODELS:
                make_response(self, 400, "参数错误：不支持的重排模型")
                return

            # 处理标签：如果是列表则转换为以|分隔的字符串
            kb_tag_raw = data.get('kb_tag', '')
            if isinstance(kb_tag_raw, list):
                kb_tag = '|'.join(kb_tag_raw)
            else:
                kb_tag = kb_tag_raw
                
            kb_desc = data.get('kb_desc', '')
            
            # 创建知识库对象
            kb = KnowledgeBase(
                kb_name=kb_name,
                kb_tags=kb_tag,
                kb_description=kb_desc,
                kb_parse_strategy=kb_parse_strategy,
                user_id=user_id,  # 使用从session获取的user_id
                permission_level=kb_auth_level,
                doc_ids=[],  # 初始为空列表
                is_deleted=0,  # 明确设置为未删除状态
                create_time=datetime.datetime.utcnow(),
                update_time=datetime.datetime.utcnow(),
                extro_info={
                    "rerank_model": rerank_model,
                    "emb_model": emb_model
                }
            )
            
            # 保存到数据库
            async with async_session() as session:
                session.add(kb)
                await session.commit()
                await session.refresh(kb)
                
                # 创建DataControl记录
                new_data_control = DataControl(
                    data_type=KB_DATA_TYPE,
                    data_id=kb.kb_id,
                    dt_type='所在部门',
                    dt_dept_code=dt_dept_code,
                    owner_id=user_id,
                )
                session.add(new_data_control)
                await session.commit()  # 提交DataControl记录
                
                # 返回创建成功的知识库信息
                result = {
                    "kb_id": kb.kb_id,
                    "create_time": kb.create_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
                make_response(self, 200, "知识库创建成功", result)
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

# 在 KnowledgeBaseInfoHandler 中添加对 is_deleted 的检查
class KnowledgeBaseInfoHandler(BaseHandler):
    async def get(self):
        try:
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            kb_id = self.get_argument('kb_id', None)
            if not kb_id:
                make_response(self, 400, "参数错误：缺少kb_id")
                return
            
            async with async_session() as session:
                # 查询知识库信息
                kb = await session.get(KnowledgeBase, int(kb_id))
                
                if not kb or kb.is_deleted == 1:
                    make_response(self, 404, "知识库不存在")
                    return
                
                # 验证用户权限
                # 1. 检查用户是否为知识库创建者
                if str(kb.user_id) == str(user_id):
                    # 用户是知识库创建者，有权访问
                    pass
                else:
                    # 如果是system类型的知识库，只有创建者才能访问
                    if kb.kb_type == 'system':
                        make_response(self, 403, "无权访问该系统知识库")
                        return
                    
                    # 2. 检查知识库是否分享给了该用户
                    # 获取用户所属部门
                    user_query = select(User).where(User.user_id == user_id)
                    user_result = await session.execute(user_query)
                    user = user_result.scalar()
                    if not user:
                        make_response(self, 404, "用户不存在")
                        return
                    
                    user_department = user.department
                    
                    # 查询分享关系
                    from sqlalchemy import or_
                    share_query = select(KBShareRelation).where(
                        and_(
                            KBShareRelation.kb_id == kb_id,
                            or_(
                                KBShareRelation.to_user_id == user_id,  # 个人分享
                                and_(  # 部门分享
                                    KBShareRelation.to_department == user_department,
                                    user_department != None  # 确保用户有部门
                                )
                            )
                        )
                    )
                    share_result = await session.execute(share_query)
                    share = share_result.scalar_one_or_none()
                    
                    if not share:
                        make_response(self, 403, "无权访问该知识库")
                        return
                
                emb_model = "bge-m3"
                rerank_model = "bge-reranker-v2-m3"
                search_config = {
                    "topk": 10,
                    "search_type": "vector",
                    "score_threshold": 0.5,
                    "token_max": 10000
                }
                prompt_text = ""
                if kb.extro_info:
                    extro_info = kb.extro_info
                    if "emb_model" in extro_info:
                        emb_model = extro_info["emb_model"]
                    if "rerank_model" in extro_info:
                        rerank_model = extro_info["rerank_model"]
                    if "search_config" in extro_info:
                        search_config = extro_info["search_config"]
                    if "prompt_text" in extro_info:
                        prompt_text = extro_info["prompt_text"]

                # 返回知识库信息
                result = {
                    "kb_id": kb.kb_id,
                    "kb_name": kb.kb_name,
                    "kb_type": kb.kb_type,
                    "kb_parse_strategy": kb.kb_parse_strategy,
                    "kb_auth_level": kb.permission_level,
                    "kb_tag": kb.kb_tags.split('|') if kb.kb_tags else [],
                    "kb_desc": kb.kb_description,
                    "create_time": kb.create_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "emb_model": emb_model,
                    "rerank_model": rerank_model,
                    "search_config": search_config,
                    "prompt_text": prompt_text
                }
                make_response(self, 200, "success", result)
        except ValueError:
            make_response(self, 400, "无效的知识库ID")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

# 在 KnowledgeBaseListHandler 中添加对 is_deleted 的过滤
class KnowledgeBaseListHandler(BaseHandler):
    async def get(self):
        user_id = self.get_user_id()
        if not user_id:
            return

        try:
            # 从 URL 查询参数获取数据，而不是从请求体
            page_no = max(int(self.get_argument('pageNo', '1')), 1)
            page_size = max(int(self.get_argument('pageSize', '10')), 1)
            dt_dept_code = self.get_argument('dt_dept_code', None)
            
            if not dt_dept_code:
                make_response(self, 400, '缺少dt_dept_code参数')
                return
            
            # 计算偏移量
            offset = (page_no - 1) * page_size
            
            async with async_session() as session:           
                # 1. 查询所有知识库及其权限控制信息
                query = select(KnowledgeBase, DataControl).outerjoin(
                    DataControl,
                    and_(
                        DataControl.data_id == KnowledgeBase.kb_id,
                        DataControl.data_type == KB_DATA_TYPE
                    )
                ).where(
                    KnowledgeBase.is_deleted == 0  # 只查询未删除的知识库
                )
                
                # 2. 添加权限过滤条件
                permission_conditions = or_(
                    # 没有权限控制记录的知识库（默认可见）
                    DataControl.dt_id.is_(None),
                    
                    DataControl.dt_type == '公开',  # 公开的知识库
                    
                    and_(
                        DataControl.dt_type == '私有',  # 私有但是owner是当前用户的知识库
                        DataControl.owner_id == user_id
                    ),
                    
                    and_(
                        DataControl.dt_type == '所在部门',  # 所在部门：dt_dept_code与用户部门完全一致
                        DataControl.dt_dept_code == dt_dept_code,
                    ),

                    and_(
                        DataControl.dt_type == '指定部门',  # 指定部门：用户部门在dt_dept_code_list中
                        or_(
                            DataControl.dt_dept_code_list.like(f'%,{dt_dept_code},%'),  # 包含用户部门
                        ),
                    )
                )
                
                query = query.where(
                    and_(
                        DataControl.data_type == KB_DATA_TYPE,
                        permission_conditions
                    )
                )
                
                # 先查询总数
                count_query = select(func.count()).select_from(
                    query.subquery()
                )
                count_result = await session.execute(count_query)
                total = count_result.scalar()
                
                # 查询分页数据
                result = await session.execute(
                    query.offset(offset).limit(page_size)
                )
                kb_data = result.all()
                
                # 整理返回数据
                kb_list_result = []
                for kb, data_control in kb_data:
                    kb_dict = {
                        "kb_id": kb.kb_id,
                        "kb_name": kb.kb_name,
                        "kb_parse_strategy": kb.kb_parse_strategy,
                        "kb_auth_level": kb.permission_level,
                        "kb_tag": kb.kb_tags.split('|') if kb.kb_tags else [],
                        "kb_desc": kb.kb_description,
                        "create_time": kb.create_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "editable": kb.user_id == user_id,
                    }
                    kb_list_result.append(kb_dict)
                
                # 返回包含total和list的数据结构
                response_data = {
                    'total': total,
                    'list': kb_list_result,
                    'pageNo': page_no,
                    'pageSize': page_size
                }
                
                make_response(self, 200, '查询成功', response_data)
            
        except Exception as e:
            logging.error(f"知识库列表查询失败: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'查询失败: {str(e)}')

# 在 KnowledgeBaseEditHandler 中添加对 is_deleted 的检查
class KnowledgeBaseEditHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_id' not in data:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            kb_id = data.get('kb_id')
            kb_name = data.get('kb_name', '')
            kb_auth_level = data.get('kb_auth_level', '')
            search_config = data.get('search_config', None)
            
            # 处理标签：如果是列表则转换为以|分隔的字符串
            kb_tag_raw = data.get('kb_tag', '')
            if isinstance(kb_tag_raw, list):
                kb_tag = '|'.join(kb_tag_raw)
            else:
                kb_tag = kb_tag_raw
                
            kb_desc = data.get('kb_desc', '')
            
            async with async_session() as session:
                # 查询知识库是否存在且未被删除
                kb = await session.get(KnowledgeBase, int(kb_id))
                if not kb or kb.is_deleted == 1:
                    make_response(self, 404, "知识库不存在")
                    return
                
                # 验证用户权限 暂时不关注user_id
                if str(kb.user_id) != str(user_id):
                    make_response(self, 403, "无权修改该知识库")
                    return
                
                # 更新知识库信息
                if kb_name:
                    kb.kb_name = kb_name
                if kb_auth_level:
                    kb.permission_level = kb_auth_level
                if kb_tag:
                    kb.kb_tags = kb_tag
                if kb_desc:
                    kb.kb_description = kb_desc
                kb.update_time = datetime.datetime.utcnow()
                
                if search_config:
                    if kb.extro_info is None:
                        kb.extro_info = {"search_config": search_config}
                    else:
                        kb.extro_info["search_config"] = search_config

                # 提交更新
                await session.commit()
                
                result = {
                    "kb_id": kb.kb_id,
                    "update_time": kb.update_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
                make_response(self, 200, "更新成功", result)
        except ValueError:
            make_response(self, 400, "无效的知识库ID")
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

# 修改 KnowledgeBaseDeleteHandler 实现软删除
class KnowledgeBaseDeleteHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_id' not in data:
                make_response(self, 400, "参数错误：缺少kb_id")
                return
            
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            kb_id = data.get('kb_id')
            
            async with async_session() as session:
                async with session.begin():  # 添加事务
                    # 查询知识库是否存在且未被删除
                    kb = await session.get(KnowledgeBase, int(kb_id))
                    if not kb or kb.is_deleted == 1:
                        make_response(self, 404, "知识库不存在")
                        return
                    
                    # 验证用户权限 暂时不关注user_id
                    if str(kb.user_id) != str(user_id):
                        make_response(self, 403, "无权删除该知识库")
                        return
                    
                    # 系统内置知识库，不允许删除
                    if kb.kb_type == "system":
                        make_response(self, 403, "系统内置知识库不能删除")
                        return

                    # 删除相关的分享关系
                    query = select(KBShareRelation).where(KBShareRelation.kb_id == kb_id)
                    result = await session.execute(query)
                    share_relations = result.scalars().all()
                    
                    for share_relation in share_relations:
                        await session.delete(share_relation)
                    
                    # 软删除知识库，将is_deleted设为1
                    kb.is_deleted = 1
                    kb.update_time = datetime.datetime.utcnow()
                    
                    # 事务会自动提交或回滚
                
                result = {
                    "kb_id": kb_id
                }
                make_response(self, 200, "删除成功", result)
        except ValueError:
            make_response(self, 400, "无效的知识库ID")
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

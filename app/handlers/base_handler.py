import logging

import jwt
import tornado
import config
from utils.response import make_response

class BaseHandler(tornado.web.RequestHandler):
    def get_user_id(self):
        auth_header = self.request.headers.get("Authorization")
        access_token = self.request.cookies.get("access_token")
        
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
        elif access_token:
            token = access_token.value
        else:
            make_response(self, 401, "无效的认证凭证")
            return None
        
        try:
            payload = jwt.decode(jwt = token, key = config.SECRET_KEY, algorithms = config.ALGORITHM)
            return int(payload["sub"])
        except (jwt.ExpiredSignatureError, jwt.DecodeError, KeyError):
            make_response(self, 401, "无效的访问令牌")
            return None

    def get_sso_token(self):
        auth_header = self.request.headers.get("Authorization")
        access_token = self.request.cookies.get("access_token")

        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
        elif access_token:
            token = access_token.value
        else:
            make_response(self, 401, "无效的认证凭证")
            return None
        if token == "undefined":
            logging.error("token解析失败: 无效的认证凭证")
            return None
        try:
            payload = jwt.decode(jwt = token, key = config.SECRET_KEY, algorithms = config.ALGORITHM)
            return str(payload["token"])
        # except (jwt.ExpiredSignatureError, jwt.DecodeError, KeyError):
        except Exception as e:
            logging.error(f"token解析失败: {e}, {logging.traceback.format_exc()}")
            make_response(self, 401, "无效的访问令牌")
            return None

    def get_zhiyu_api_token(self):
        auth_header = self.request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
            return token
        return ""
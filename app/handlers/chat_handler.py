import traceback
import json
import aiohttp
from utils.response import make_response
from models import Dialog, DialogMessage, async_session, RAGDocument
from models import DialogMessageToken
# from models import Chunk, Document
from utils.emb import get_embedding
# from app.utils.hnswlib_ann import index
# from utils import milvus
from utils import es
from utils.minio import generate_presigned_url
import config
import asyncio
import time
import logging
from handlers.base_handler import BaseHandler
from utils.minio import download_file
from utils.doc_parser import parse_doc_task
import os
import asyncio
from utils.extractor import extract_text
from sqlalchemy import select, func
from models.knowledge_base import KnowledgeBase
from utils.storage import StorageManager
from services.document_manager import DocumentManager
from services.document_parser import file_parser

storage_manager = StorageManager()

# async def retrive_by_milvus(trace_log, question):
#     all_docs = {}

#     # 向量化
#     start_ts = int(time.time()*1000)
#     dense_vectors, sparse_vectors = get_embedding('bge-m3', [question])
#     end_ts = int(time.time()*1000)
#     trace_log["get_embedding"] = end_ts - start_ts

#     # 调用向量数据库
#     start_ts = end_ts
#     # labels, scores = index.knn_query(query_vector, k=10)
#     chunk_ids, scores = milvus.hybrid_search(
#         dense_embedding=dense_vectors[0], 
#         sparse_embedding=sparse_vectors[0],
#         sparse_weight=1.0,
#         dense_weight=0,
#         limit=10)
#     end_ts = int(time.time()*1000)
#     trace_log["search"] = end_ts - start_ts
#     trace_log["search_result"] = [chunk_ids, scores]

#     # 查询分片
#     start_ts = end_ts
#     ref_doc_ids = set()
#     async with async_session() as session:
#         result = await session.execute(
#             Chunk.__table__.select().filter(Chunk.chunk_id.in_(chunk_ids)
#             )
#         )
#         all_chunks = result.all()
#     for chunk in all_chunks:
#         ref_doc_ids.add(chunk.doc_id)
#     end_ts = int(time.time()*1000)
#     trace_log["query_chunks"] = end_ts - start_ts

#     # 查询全文
#     start_ts = end_ts
#     async with async_session() as session:
#         result = await session.execute(
#             Document.__table__.select().filter(Document.doc_id.in_(ref_doc_ids)
#             )
#         )
#         for doc in result.all():
#             all_docs[doc.doc_id] = doc
#     end_ts = int(time.time()*1000)
#     trace_log["query_docs"] = end_ts - start_ts

#     # 生成引用文档
#     start_ts = end_ts
#     ref_content = []
#     ref_md_doc_ids = set()
#     for chunk in all_chunks:
#         if chunk.chunk_content is None:
#             continue
#         if chunk.doc_id in ref_md_doc_ids:
#             continue
#         doc = all_docs.get(chunk.doc_id, None)
#         if doc and len(doc.markdown) < 10000:
#             ref_content.append(doc.markdown)
#             ref_md_doc_ids.add(chunk.doc_id)
#         else:
#             ref_content.append(json.dumps({
#                 "title": chunk.chunk_title,
#                 "content": chunk.chunk_content,
#             }, ensure_ascii=False))
    
#     sys_propmt = config.RAG_PROMPT_TEMPLATE.format(Documents="\n".join(ref_content))
#     end_ts = int(time.time()*1000)
#     trace_log["sys_prompt"] = end_ts - start_ts

#     return sys_propmt, all_docs


# async def retrive_by_es(trace_log, question):
#     all_docs = {}

#     # 调用es
#     start_ts = int(time.time()*1000)
#     # labels, scores = index.knn_query(query_vector, k=10)
#     doc_ids, scores, _ = es.search(question, limit=3)
#     end_ts = int(time.time()*1000)
#     trace_log["search"] = end_ts - start_ts
#     trace_log["search_result"] = [doc_ids, scores]

#     if len(scores) > 0:
#         threshold = sum(scores) / len(scores)
#         filtered_pairs = [(doc_id, score) for doc_id, score in zip(doc_ids, scores) if score >= threshold]
#         filtered_doc_ids = [doc_id for doc_id, _ in filtered_pairs]
#         trace_log["filtered_search_result"] = filtered_pairs  # 保持原始结果记录
#     else:
#         filtered_doc_ids = []
#         trace_log["filtered_search_result"] = []

#     # 查询全文
#     start_ts = end_ts
#     async with async_session() as session:
#         result = await session.execute(
#             Document.__table__.select().filter(Document.doc_id.in_(filtered_doc_ids)
#             )
#         )
#         for doc in result.all():
#             all_docs[doc.doc_id] = doc
#     end_ts = int(time.time()*1000)
#     trace_log["query_docs"] = end_ts - start_ts

#     # 生成引用文档
#     start_ts = end_ts
#     ref_content = []
#     sorted_docids = []
#     for doc_id in filtered_doc_ids:
#         if doc_id in all_docs:
#             ref_content.append(all_docs[doc_id].markdown)
#             sorted_docids.append(doc_id)
    
#     sys_propmt = config.RAG_PROMPT_TEMPLATE.format(Documents="\n".join(ref_content))
#     end_ts = int(time.time()*1000)
#     trace_log["sys_prompt"] = end_ts - start_ts

#     return sys_propmt, all_docs, sorted_docids

async def retrive_by_kb(trace_log, question, kb_id = None):
    # TODO 根据question识别出需要检索的知识库

    # 检索
    start_ts = int(time.time()*1000)
    kb_list = []
    if kb_id is None:
        async with async_session() as session:
            kb_query = select(KnowledgeBase).where(
                KnowledgeBase.kb_type == "system",
                KnowledgeBase.is_deleted == 0
            )
            result = await session.execute(kb_query)
            kb_list = result.scalars().all()
    else:
        async with async_session() as session:
            kb = await session.get(KnowledgeBase, kb_id)
            if kb:
                kb_list.append(kb)
    if len(kb_list) <= 0:
        return [], [], []
    
    chunks = []
    for kb in kb_list:
        doc_ids = json.loads(kb.doc_ids) if kb.doc_ids else []

        emb_model = "bge-m3"
        rerank_model = "bge-reranker-v2-m3"
        search_config = {}
        if kb.extro_info:
            if "emb_model" in kb.extro_info:
                emb_model = kb.extro_info["emb_model"]
            if "rerank_model" in kb.extro_info:
                rerank_model = kb.extro_info["rerank_model"]
            if "search_config" in kb.extro_info:
                search_config = kb.extro_info["search_config"]

        # 调用文档管理器进行检索（这里增加分页参数）  topk, search_type, token_max, score_threshold
        kb_chunks = await DocumentManager.retrieval_chunks(
            kb_id=int(kb.kb_id),
            doc_ids=doc_ids,
            query=question,
            collection_name=config.DEFAULT_INDEX_NAME,
            top_k=search_config.get("topk", 10),
            retrieval_type=search_config.get("search_type", "hybrid"),
            rerank_model=rerank_model,
            emb_model=emb_model,
            score_threshold=search_config.get("score_threshold", 0.5)
        )
        if kb_chunks: 
            chunks.extend(kb_chunks)

    if not chunks:
        return [], [], []

    # 按doc_id聚合content（新增逻辑）
    content_aggregator = {}  # key: doc_id, value: {"content": [], "scores": []}
    for chunk in chunks:
        doc_id = chunk.doc_id
        if doc_id not in content_aggregator:
            content_aggregator[doc_id] = {"content": [], "scores": []}
        content_aggregator[doc_id]["content"].append(chunk.chunk_content)
        content_aggregator[doc_id]["scores"].append(chunk.meta_data.get('score', ''))

    # 合并content并计算聚合后的scores（取最高得分）
    doc_ids = []
    contents = []
    scores = []
    for doc_id, data in content_aggregator.items():
        doc_ids.append(doc_id)
        contents.append("\n".join(data["content"]))
        max_score = max(data["scores"]) if data["scores"] else 0
        scores.append(max_score)

    # 按scores降序排序，保持doc_ids和contents对应顺序
    combined = list(zip(doc_ids, contents, scores))
    combined.sort(key=lambda x: x[2], reverse=True)
    doc_ids, contents, scores = zip(*combined)
    doc_ids = list(doc_ids)
    contents = list(contents)
    scores = list(scores)

    end_ts = int(time.time()*1000)
    trace_log["search"] = end_ts - start_ts
    trace_log["search_result"] = [doc_ids, scores]
    
    end_ts = int(time.time()*1000)
    trace_log["sys_prompt"] = end_ts - start_ts

    return contents, doc_ids, scores

async def retrive_by_uploaded_file(trace_log, question, file_object_list):
    # 下载
    start_ts = int(time.time()*1000)
    for file_object_path in file_object_list:
        local_file_path = os.path.join(config.TEMP_DIR, os.path.basename(file_object_path))  
        await download_file(file_object_path, local_file_path)
    end_ts = int(time.time()*1000)
    trace_log["dowload_file"] = end_ts - start_ts

    # 解析
    file_md_list = []
    start_ts = end_ts
    for file_object_path in file_object_list:
        local_file_path = os.path.join(config.TEMP_DIR, os.path.basename(file_object_path))  
        extract_res = await file_parser.extract(local_file_path)
        file_md_list.append(extract_res.md_content)
        try:
            os.remove(extract_res.preview_file_local_path)
            if local_file_path != extract_res.preview_file_local_path:
                os.remove(local_file_path)
        except Exception:
            pass
        # text = extract_text(local_file_path)
        # file_md_list.append(text)
        # os.remove(local_file_path)
    end_ts = int(time.time()*1000)
    trace_log["parse_file"] = end_ts - start_ts

    return file_md_list, []


# class ChatCompletionHandler(BaseHandler):
#     # async def retrive(self, question, dialog_id):
#     #     # 向量化
#     #     dense_vectors, sparse_vectors = get_embedding('bge-m3', [question])

#     async def post(self):
#         try:
#             data = json.loads(self.request.body)
#             if not data or 'question' not in data:
#                 make_response(self, 400, "invalid request")
#                 return
#             question = data['question']
#             enable_rag = data.get('enable_rag', None)
#             dialog_id = data.get("dialog_id", None)
#             file_objects = data.get("file_objects", None)

#             userid = self.get_user_id()
#             if userid is None:return

#             if not dialog_id:
#                 dialog = Dialog(title=question, user_id=userid)
#                 async with async_session() as session:
#                     session.add(dialog)
#                     await session.commit()
#             else:
#                 async with async_session() as session:
#                     dialog = await session.get(Dialog, dialog_id)
#                     if not dialog or dialog.user_id != userid:
#                         make_response(self, 404, "dialog not found")
#                         return
            
#             trace_log = {}
#             sorted_docids = []
#             if file_objects:
#                 sys_propmt, sorted_docids = await retrive_by_uploaded_file(trace_log, question, file_objects)
#             elif enable_rag:
#                 sys_propmt, sorted_docids = await retrive_by_kb(trace_log, question)
#             else:
#                 sys_propmt = config.BASE_PROMPT_TEMPLATE
#             trace_log["sys_prompt_content"] = sys_propmt

#             # 异步调用大模型
#             try:
#                 start_ts = int(time.time()*1000)
#                 async with aiohttp.ClientSession() as session:
#                     post_headers = {
#                         "Authorization": f"Bearer {config.API_KEY}",
#                         "Content-Type": "application/json"
#                     }
#                     messages = [
#                         {"role": "system", "content": sys_propmt},
#                         {"role": "user", "content": question}
#                     ]
#                     post_data={
#                         "model": config.LLM_MODEL,
#                         "messages": messages,
#                         "temperature": 0.2
#                     }
#                     async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
#                         response.raise_for_status()
#                         result = await response.json()
#                         answer = result["choices"][0]["message"]["content"]
#                         # 获取tokens统计信息
#                         usage = result.get("usage", {})
#                         prompt_tokens = usage.get("prompt_tokens", 0)
#                         completion_tokens = usage.get("completion_tokens", 0)
#                 end_ts = int(time.time()*1000)
#                 trace_log["generate"] = end_ts - start_ts

#             except Exception as e:
#                 logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
#                 make_response(self, 500, f"llm request failed")
#                 return

#             ref_docs = []
#             async with async_session() as session:
#                 for doc_id in sorted_docids:
#                     doc = await session.get(RAGDocument, doc_id)
#                     ref_docs.append({
#                         "doc_id": doc.doc_id,
#                         "doc_title": doc.doc_name,
#                         "abstract": "",
#                         "version": doc.doc_version,
#                         "url": generate_presigned_url(config.MINIO_BUCKET_NAME, doc.minio_doc_path),
#                         "preview_url": generate_presigned_url(config.MINIO_BUCKET_NAME, doc.minio_doc_path, preview=True),
#                     })


#             question_message = DialogMessage(
#                 dialog_id=dialog.dialog_id,
#                 content=question,
#                 role="user"            
#             )
#             answer_message = DialogMessage(
#                 dialog_id=dialog.dialog_id,
#                 content=answer,
#                 role="assistant",
#                 refs=json.dumps(ref_docs)      
#             )
#             async with async_session() as session:
#                 session.add(question_message)
#                 session.add(answer_message)
#                 await session.commit()

#                 # 记录user和assistant的tokens
#                 user_tokens = DialogMessageToken(
#                     message_id=question_message.message_id,
#                     dialog_id=dialog.dialog_id,
#                     role="user",
#                     user_id=userid,
#                     input_tokens=prompt_tokens,  # 包含了system prompt和user input的所有tokens
#                     output_tokens=0
#                 )
#                 assistant_tokens = DialogMessageToken(
#                     message_id=answer_message.message_id,
#                     dialog_id=dialog.dialog_id,
#                     role="assistant",
#                     user_id=userid,
#                     input_tokens=0,
#                     output_tokens=completion_tokens  # 模型输出的tokens
#                 )
                
#                 session.add(user_tokens)
#                 session.add(assistant_tokens)
#                 await session.commit()

#             make_response(self, 200, "success", {
#                 "answer": answer, 
#                 "references": ref_docs,
#                 "dialog_id": dialog.dialog_id,
#             })

#             logging.info(trace_log)
#         except json.JSONDecodeError:
#             make_response(self, 400, "无效的 JSON 数据")


class ChatCompletionHandlerStream(BaseHandler):
    # 设置流式响应头
    def set_default_headers(self):
        self.set_header("Content-Type", "text/event-stream")
        self.set_header("Cache-Control", "no-cache")
        self.set_header("Connection", "keep-alive")

    async def post(self):
        stop_flag = False
        
        try:
            data = json.loads(self.request.body)
            if not data or 'question' not in data:
                make_response(self, 400, "invalid request")
                return
            question = data['question']
            enable_rag = data.get('enable_rag', None)
            dialog_id = data.get("dialog_id", None)
            file_objects = data.get("file_objects", None)
            kb_id = data.get("kb_id", None)

            userid = self.get_user_id()
            if userid is None:
                # 未登录, 判断是否为api的调用，否则返回未登录
                if self.get_zhiyu_api_token() != config.CHAT_COMPLETION_API_TOKEN:
                    return
                userid = config.ZHIYU_USER_ID

            if kb_id is not None:
                # 查询知识库是否存在且未被删除
                async with async_session() as session:
                    kb = await session.get(KnowledgeBase, int(kb_id))
                    if not kb or kb.is_deleted == 1:
                        make_response(self, 404, "知识库不存在")
                        return
                    
                    # 验证用户权限 暂时不关注user_id
                    if str(kb.user_id) != str(userid):
                        make_response(self, 403, "无权修改该知识库")
                        return

            history_messages = []
            if not dialog_id:
                dialog = Dialog(title=question[:128], user_id=userid)
                async with async_session() as session:
                    session.add(dialog)
                    await session.commit()
            else:
                async with async_session() as session:
                    dialog = await session.get(Dialog, dialog_id)
                    if not dialog or dialog.user_id != userid:
                        make_response(self, 404, "dialog not found")
                        return
                    result = await session.execute(
                            DialogMessage.__table__.select().filter(DialogMessage.dialog_id == dialog_id))
                    for message in result.all():
                        msg = {
                            "role": message.role,
                            "content": message.content
                        }
                        history_messages.append(msg)

            trace_log = {}
            sorted_docids = []
            contents = []
            content_read_percent = 100
            if file_objects:
                contents, sorted_docids = await retrive_by_uploaded_file(trace_log, question, file_objects)
                system_prompt = config.RAG_PROMPT_TEMPLATE
                content_str = "\n".join(contents)
                if len(content_str) > 20000:
                    content_read_percent = int(20000 / len(content_str) * 100)
                input_question = f"上下文信息: {content_str[0:20000]} \n\n 用户问题: {question}" 
            elif enable_rag or kb_id is not None:
                contents, sorted_docids, scores = await retrive_by_kb(trace_log, question, kb_id)

                # # 过滤score明显偏低的content（示例阈值：保留score大于等于平均分的项）
                # if scores:
                #     avg_score = sum(scores) / len(scores)
                #     filtered = [(content, score) for content, score in zip(contents, scores) if score >= avg_score]
                #     filtered_contents = [content for content, _ in filtered]
                # else:
                #     filtered_contents = contents
                
                # 拼接并控制总长度在10000字以内
                content_str = "\n".join(contents)
                if len(content_str) > 100000:
                    content_str = content_str[:100000]  # 截断前10000字
                
                system_prompt = config.RAG_PROMPT_TEMPLATE
                input_question = f"上下文信息: {content_str} \n\n 用户问题: {question}" 
            else:
                input_question = question
                system_prompt = config.BASE_PROMPT_TEMPLATE
            
            trace_log["input_question"] = input_question
            
            answer = ""
            if content_read_percent < 100:
                content = f"*由于参考资料超出字数限制，我只阅读了前{content_read_percent}%的内容* \n\n ---\n\n ---\n\n ---\n\n"
                self.write(f"data: {json.dumps({'content': content})}\n\n")
                answer += content
                await self.flush()
            output_tokens = 0  # 初始化输出tokens计数
            start_ts = int(time.time()*1000)
            # 流式请求大模型
            async with aiohttp.ClientSession() as http_session:
                post_headers = {
                    "Authorization": f"Bearer {config.API_KEY}",
                    "Content-Type": "application/json"
                }
                if len(history_messages) > 0:
                    history_messages[0] = {"role": "system", "content": system_prompt}
                    history_messages.append({"role": "user", "content": input_question})
                else:
                    history_messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": input_question}
                    ]
                post_data = {
                    "model": config.LLM_MODEL,
                    "messages": history_messages,
                    "temperature": 0.2,
                    "stream": True  # 启用流式模式
                }

                async with http_session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=600) as response:
                    # 逐块读取流式响应
                    async for chunk in response.content:
                        if self.request.connection.stream.closed():
                            stop_flag = True
                            break  # 连接中断时终止
                        
                        # import pdb;pdb.set_trace()
                        # 解析大模型返回的chunk
                        if chunk.startswith(b'data: '):
                            chunk = chunk.decode('utf-8')
                            if chunk.startswith('data: [DONE]'):
                                break
                            if not chunk.startswith('data: '):
                                continue
                            json_data = json.loads(chunk[6:].strip())
                            content = json_data['choices'][0]['delta'].get('content', '')
                            if content:
                                # 流式输出核心代码
                                self.write(f"data: {json.dumps({'content': content})}\n\n")
                                answer += content
                                output_tokens += 1  # 每个token计数
                                await self.flush()

            # 最终保存完整回答到数据库（原逻辑保持不变）
            end_ts = int(time.time()*1000)
            trace_log["generate"] = end_ts - start_ts
            
            # 估算tokens（简单估算，可根据实际情况调整）
            prompt_tokens = len(str(history_messages)) // 4  # 包含system prompt和历史消息的tokens
            completion_tokens = output_tokens
            
            ref_docs = []
            async with async_session() as session:
                for i in range(len(sorted_docids)):
                    doc_id = sorted_docids[i]
                    doc = await session.get(RAGDocument, doc_id)
                    ref_docs.append({
                        "doc_id": doc.doc_id,
                        "doc_title": doc.doc_name,
                        "abstract": contents[i][0:512],
                        "version": doc.doc_version,
                        "url": generate_presigned_url(config.MINIO_BUCKET_NAME, doc.minio_doc_path),
                        "preview_url": generate_presigned_url(config.MINIO_BUCKET_NAME, 
                                                              doc.minio_preview_doc_path if doc.minio_preview_doc_path else doc.minio_doc_path, 
                                                              preview=True),
                    })

            question_message = DialogMessage(
                dialog_id=dialog.dialog_id,
                content=question,
                role="user"            
            )
            answer_message = DialogMessage(
                dialog_id=dialog.dialog_id,
                content=answer,
                role="assistant",
                refs=json.dumps(ref_docs)      
            )
            async with async_session() as db_session:
                db_session.add(question_message)
                db_session.add(answer_message)
                await db_session.commit()
                
                # 记录user和assistant的tokens
                user_tokens = DialogMessageToken(
                    message_id=question_message.message_id,
                    dialog_id=dialog.dialog_id,
                    role="user",
                    user_id=userid,
                    input_tokens=prompt_tokens,  # 包含了system prompt和历史消息的所有tokens
                    output_tokens=0
                )
                assistant_tokens = DialogMessageToken(
                    message_id=answer_message.message_id,
                    dialog_id=dialog.dialog_id,
                    role="assistant",
                    user_id=userid,
                    input_tokens=0,
                    output_tokens=completion_tokens  # 模型输出的tokens
                )
                
                db_session.add(user_tokens)
                db_session.add(assistant_tokens)
                await db_session.commit()

            # make_response(self, 200, "success", {"answer": answer, "references": ref_docs})
            response_data = {"code": 200, "message": "success", "data": {
                "answer": answer, 
                "references": ref_docs,
                "dialog_id": dialog.dialog_id,
                "message_id": answer_message.message_id,
                }}
            self.write(f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n")
            if not stop_flag:
                await self.finish()

            # print(trace_log)
        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            response_data = {"code": 500, "message": "internal error", "data": {
                "answer": "服务出现故障，请联系管理员处理", 
                "references": []
                }}
            self.write(f"data: {json.dumps({'content': '服务出现故障，请联系管理员'})}\n\n")
            self.write(f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n")
            if not stop_flag:
                await self.finish()
            

import logging
import asyncio
import json
from typing import Optional, Dict, Any
from sqlalchemy import select

from .base_handler import BaseHandler
from models import Agent, Dialog, DialogMessage, async_session
from models.agent_file_contents import AgentFileContents
from utils.response import make_response
from utils.minio import download_file
from utils.extractor import extract_text
from agent_tools import agent_fault_analysis_stream, agent_text_proofreader_stream, agent_exec_code


class AgentRouteHandler(BaseHandler):
    """智能体路由处理器 - 非流式"""
    
    async def post(self):
        """处理智能体路由请求"""
        user_id = self.get_user_id()
        if not user_id:
            return
            
        try:
            # 解析请求参数
            data = json.loads(self.request.body)
            agent_id = data.get('agent_id')
            user_message = data.get('user_message', '')
            dialog_id = data.get('dialog_id')  # 可选的对话ID
            
            if not agent_id:
                make_response(self, 400, '缺少agent_id参数')
                return
            
            # 查询智能体信息
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return
            
            # 根据智能体描述进行路由分发
            result = await self._route_by_agent_desc(agent, user_message, files, user_id, dialog_id)
            
            make_response(self, 200, '处理成功', result)
            
        except json.JSONDecodeError:
            make_response(self, 400, '请求体格式错误')
        except Exception as e:
            logging.error(f"Agent route request failed: {str(e)}")
            make_response(self, 500, f'请求处理失败: {str(e)}')
    
    async def _route_by_agent_desc(self, agent: Agent, message: str, files: list, user_id: int, dialog_id: Optional[int]) -> Dict[str, Any]:
        """根据智能体描述进行路由分发"""
        agent_desc = agent.agent_desc.lower() if agent.agent_desc else ''
        
        # 路由分发逻辑
        if 'knowledge_qa' in agent_desc:
            return await agent_knowledge_qa(agent, message, files, user_id, dialog_id)
        elif 'text_correct' in agent_desc:
            return await agent_text_proofreader(agent, message, files, user_id, dialog_id)
        else:
            
            # 默认处理逻辑 - 执行智能体代码
            return await agent_exec_code(agent, message, files, user_id, dialog_id)


class AgentRouteStreamHandler(BaseHandler):
    """智能体路由处理器 - 流式"""
    
    async def post(self):
        """处理智能体路由请求 - 流式响应"""
        user_id = self.get_user_id()
        if not user_id:
            return
            
        try:
            # 解析请求参数
            data = json.loads(self.request.body)
            agent_id = data.get('agent_id')
            user_query = data.get('user_query', '')
            
            if not agent_id:
                await self._write_stream_error('缺少agent_id参数')
                return
                
            # 查询智能体信息
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    await self._write_stream_error('Agent不存在')
                    return
            
            # 设置流式响应头
            self.set_header('Content-Type', 'text/event-stream')
            self.set_header('Cache-Control', 'no-cache')
            self.set_header('Connection', 'keep-alive')
            self.set_header('Access-Control-Allow-Origin', '*')
            
            # 根据智能体描述进行路由分发
            await self._route_by_agent_desc_stream(agent, user_query, user_id)
            
        except json.JSONDecodeError:
            await self._write_stream_error('请求体格式错误')
        except Exception as e:
            logging.error(f"Agent route stream request failed: {str(e)}")
            await self._write_stream_error(f'请求处理失败: {str(e)}')
    
    async def _write_stream_data(self, data: Dict[str, Any]):
        """写入流式数据 - 修改为与 chat_handler.py 一致的格式"""
        # 如果 data 包含 content 字段，直接使用
        if 'content' in data:
            content = data['content']
        # 如果 data 包含 message 字段，将其作为 content
        elif 'message' in data:
            content = data['message']
        # 否则将整个 data 转换为字符串作为 content
        else:
            content = json.dumps(data, ensure_ascii=False)
        
        # 使用与 chat_handler.py 一致的格式
        json_data = json.dumps({'content': content}, ensure_ascii=False)
        self.write(f"data: {json_data}\n\n")
        await self.flush()
    
    async def _write_stream_error(self, error_message: str):
        """写入流式错误"""
        await self._write_stream_data({
            'content': error_message
        })
    
    async def _route_by_agent_desc_stream(
        self, agent: Agent,
        user_query: str,
        user_id: int):
        """流式路由分发"""
        agent_desc = agent.agent_desc.lower() if agent.agent_desc else ''
        
        # 路由分发逻辑
        if 'fault_analysis' in agent_desc:
            await self._handle_fault_analysis_stream(agent, user_query, user_id)
        elif 'text_proofreader' in agent_desc:
            await self._handle_text_proofreader_stream(agent, user_query, user_id)
        else:
            await self._handle_default_agent_stream(agent, user_query, user_id)
    
    async def _handle_fault_analysis_stream(
        self,
        agent: Agent,
        user_query: str,
        file_content: str, 
        user_id: int
    ):
        """处理故障分析流式请求"""
        # 创建一个适配器函数，将 agent_fault_analysis_stream 的输出格式转换为统一格式
        async def stream_writer_adapter(data):
            await self._write_stream_data(data)
        
        await agent_fault_analysis_stream(
            user_query=user_query,
            file_content=file_content,
            user_id=user_id,
            stream_writer=stream_writer_adapter
        )

    async def _handle_text_proofreader_stream(
        self, 
        agent: Agent, 
        user_query: str, 
        user_id: int):
        """处理文本校对流式请求"""
        async def stream_writer_adapter(data):
            await self._write_stream_data(data)
        
        # 从数据库中根据user_id和agent_id获取file_content
        file_content = ""
        try:
            async with async_session() as session:
                # 查询该用户和智能体对应的文件内容
                stmt = select(AgentFileContents.file_contents).where(
                    AgentFileContents.user_id == user_id,
                    AgentFileContents.agent_id == agent.agent_id,
                    AgentFileContents.parser_status == 2  # 只获取解析完成的文件
                )
                
                result = await session.execute(stmt)
                file_record = result.scalar_one_or_none()
                
                if file_record:
                    file_content = file_record
                else:
                    # 如果没有找到文件内容，记录日志
                    logging.warning(f"No file content found for user_id: {user_id}, agent_id: {agent.agent_id}")
                    file_content = ""
                    
        except Exception as e:
            logging.error(f"Error fetching file content: {str(e)}")
            file_content = ""
    
        # 调用流式文本校对函数
        await agent_text_proofreader_stream(
            user_query=user_query,
            file_content=file_content,
            user_id=user_id,
            stream_writer=stream_writer_adapter
        )

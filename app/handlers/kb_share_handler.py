import tornado.web
import json
import datetime
from utils.response import make_response
from handlers.base_handler import BaseHandler
from models.knowledge_base import KnowledgeBase
from models.kb_share_relation import KBShareRelation
from models import async_session
from sqlalchemy import select, and_
from models.user import User  # 添加导入


class KnowledgeBaseShareListHandler(BaseHandler):
    """获取知识库分享列表"""
    
    async def get(self):
        try:
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            kb_id = self.get_argument('kb_id', None)
            if not kb_id:
                make_response(self, 400, "参数错误：缺少kb_id")
                return
            
            async with async_session() as session:
                # 查询知识库是否存在且未被删除
                kb = await session.get(KnowledgeBase, int(kb_id))
                if not kb or kb.is_deleted == 1:
                    make_response(self, 404, "知识库不存在")
                    return
                
                # 验证用户权限
                if str(kb.user_id) != str(user_id):
                    make_response(self, 403, "无权查看该知识库的分享信息")
                    return
                
                # 查询分享列表
                query = select(KBShareRelation).where(
                    and_(
                        KBShareRelation.kb_id == kb_id,
                        KBShareRelation.from_user_id == user_id
                    )
                )
                result = await session.execute(query)
                shares = result.scalars().all()
                
                # 构建返回结果
                share_list = []
                for share in shares:
                    # 确定分享类型
                    share_type = "department" if share.to_department else "personal"
                    
                    share_info = {
                        "kb_id": share.kb_id,
                        "share_type": share_type,  # 使用字符串表示单一分享类型
                        "to_department": share.to_department,
                        "to_user_id": share.to_user_id if share.to_user_id != 0 else None,
                        "attr_02": share.attr_02,
                        "share_time": share.create_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    }
                    share_list.append(share_info)
                
                make_response(self, 200, "获取分享列表成功", {"shares": share_list})
        except ValueError:
            make_response(self, 400, "无效的知识库ID")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseShareDeleteHandler(BaseHandler):
    """取消知识库分享"""
    
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_id' not in data:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            kb_id = data.get('kb_id')
            to_user_id = data.get('to_user_id', None)
            to_department = data.get('to_department', None)
            
            # 至少需要提供一个目标（用户或部门）
            if to_user_id is None and to_department is None:
                make_response(self, 400, "参数错误：需要提供to_user_id或to_department")
                return
            
            async with async_session() as session:
                # 查询知识库是否存在且未被删除
                kb = await session.get(KnowledgeBase, int(kb_id))
                if not kb or kb.is_deleted == 1:
                    make_response(self, 404, "知识库不存在")
                    return
                
                # 验证用户权限
                if str(kb.user_id) != str(user_id):
                    make_response(self, 403, "无权取消该知识库的分享")
                    return
                
                # 构建查询条件
                conditions = [
                    KBShareRelation.kb_id == kb_id,
                    KBShareRelation.from_user_id == user_id
                ]
                
                # 根据提供的参数添加条件
                if to_user_id is not None:
                    conditions.append(KBShareRelation.to_user_id == to_user_id)
                if to_department is not None:
                    conditions.append(KBShareRelation.to_department == to_department)
                
                # 查询分享记录
                query = select(KBShareRelation).where(and_(*conditions))
                result = await session.execute(query)
                share = result.scalar_one_or_none()
                
                if not share:
                    make_response(self, 404, "分享记录不存在")
                    return
                
                # 删除分享记录
                await session.delete(share)
                await session.commit()
                
                # 构建响应数据
                response_data = {"kb_id": kb_id}
                if to_user_id is not None:
                    response_data["to_user_id"] = to_user_id
                if to_department is not None:
                    response_data["to_department"] = to_department
                
                make_response(self, 200, "取消分享成功", response_data)
        except ValueError:
            make_response(self, 400, "无效的ID")
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class SharedKnowledgeBaseListHandler(BaseHandler):
    """获取分享给我的知识库列表（包括个人分享和部门分享）"""
    
    async def get(self):
        try:
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            # 获取分页参数
            page_no = int(self.get_argument('pageNo', '1'))
            page_size = int(self.get_argument('pageSize', '10'))
            
            if page_no < 1 or page_size < 1:
                make_response(self, 400, "分页参数错误")
                return
            
            async with async_session() as session:
                from sqlalchemy import select, func, join, or_
                
                # 获取用户所属部门
                user_query = select(User).where(User.user_id == user_id)
                user_result = await session.execute(user_query)
                user = user_result.scalar()
                if not user:
                    make_response(self, 404, "用户不存在")
                    return
                
                user_department = user.department
                
                # 查询个人分享和部门分享的知识库
                shared_kb_query = select(
                    KnowledgeBase.kb_id,
                    KnowledgeBase.kb_name,
                    KnowledgeBase.kb_description,
                    KBShareRelation.to_department,
                    KBShareRelation.to_user_id,
                    KnowledgeBase.create_time,
                    KnowledgeBase.update_time  # 添加 update_time 字段
                ).join(
                    KBShareRelation, KnowledgeBase.kb_id == KBShareRelation.kb_id
                ).where(
                    and_(
                        or_(
                            KBShareRelation.to_user_id == user_id,  # 个人分享 (p2p)
                            and_(  # 部门分享 (p2d)
                                KBShareRelation.to_department == user_department,
                                user_department != None  # 确保用户有部门
                            )
                        ),
                        KnowledgeBase.is_deleted == 0
                    )
                )
                
                # 计算总数
                shared_count_query = select(func.count()).select_from(
                    join(KBShareRelation, KnowledgeBase, 
                         KBShareRelation.kb_id == KnowledgeBase.kb_id)
                ).where(
                    and_(
                        or_(
                            KBShareRelation.to_user_id == user_id,  # 个人分享
                            and_(  # 部门分享
                                KBShareRelation.to_department == user_department,
                                user_department != None  # 确保用户有部门
                            )
                        ),
                        KnowledgeBase.is_deleted == 0
                    )
                )
                total = await session.scalar(shared_count_query) or 0
                
                # 计算分页偏移量
                offset = (page_no - 1) * page_size
                
                # 应用排序和分页
                final_query = shared_kb_query.order_by(
                    KnowledgeBase.create_time.desc(),
                    KnowledgeBase.kb_id.desc()
                ).offset(offset).limit(page_size)
                
                result = await session.execute(final_query)
                kb_items = result.all()
                
                # 构建返回结果
                kb_list = []
                for item in kb_items:
                    # 确定分享类型
                    if item.to_department:
                        share_type = "p2d"  # 部门分享
                    elif item.to_user_id:
                        share_type = "p2p"  # 个人分享
                    else:
                        # 理论上这里不应该出现，因为已经移除了系统知识库 public
                        share_type = "unknown"
                    
                    kb_info = {
                        "kb_id": item.kb_id,
                        "kb_name": item.kb_name,
                        "kb_desc": item.kb_description,
                        "share_type": share_type,  # 保留分享类型以便前端区分
                        "update_time": item.update_time.strftime("%Y-%m-%dT%H:%M:%SZ") if item.update_time else None  # 添加 update_time 字段
                    }
                    kb_list.append(kb_info)
                
                result = {
                    "total": total,
                    "kb_list": kb_list,
                    "page_no": page_no,
                    "page_size": page_size
                }
                make_response(self, 200, "获取分享知识库列表成功", result)
        except ValueError:
            make_response(self, 400, "无效的分页参数")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseShareToHandler(BaseHandler):
    """批量分享知识库给多个用户"""
    
    async def post(self):
        """分享知识库给多个指定用户"""
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_id' not in data or 'user_ids' not in data:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 从session获取用户ID
            from_user_id = self.get_user_id()
            if from_user_id is None:
                return
            
            # 在相关处理程序中添加以下辅助函数
            def safe_int_convert(value):
                try:
                    return int(value)
                except (TypeError, ValueError):
                    return None
            
            # 然后在代码中使用这个函数替换直接的 int() 转换
            kb_id = safe_int_convert(data.get('kb_id'))
            if kb_id is None:
                make_response(self, 400, "无效的知识库ID")
                return
            user_ids = data.get('user_ids', [])
            
            # 验证user_ids是否为列表
            if not isinstance(user_ids, list) or len(user_ids) == 0:
                make_response(self, 400, "参数错误：user_ids必须为非空列表")
                return
            
            async with async_session() as session:
                # 查询知识库是否存在且未被删除
                kb = await session.get(KnowledgeBase, int(kb_id))
                if not kb or kb.is_deleted == 1:
                    make_response(self, 400, "知识库不存在")
                    return
                
                # 验证用户权限
                if str(kb.user_id) != str(from_user_id):
                    make_response(self, 403, "无权分享该知识库")
                    return
                
                # 检查不能分享给自己
                if from_user_id in user_ids:
                    make_response(self, 400, "不能分享给自己")
                    return
                
                # 检查目标用户是否存在
                for to_user_id in user_ids:
                    user = await session.get(User, int(to_user_id))
                    if not user:
                        make_response(self, 404, f"用户 {to_user_id} 不存在")
                        return
                
                # 批量创建分享关系
                success_count = 0
                for to_user_id in user_ids:
                    # 检查是否已经分享给该用户
                    query = select(KBShareRelation).where(
                        and_(
                            KBShareRelation.kb_id == kb_id,
                            KBShareRelation.from_user_id == from_user_id,
                            KBShareRelation.to_user_id == to_user_id
                        )
                    )
                    result = await session.execute(query)
                    existing_share = result.scalar_one_or_none()
                    
                    if not existing_share:
                        # 创建分享关系
                        share = KBShareRelation(
                            kb_id=kb_id,
                            from_user_id=from_user_id,
                            to_user_id=to_user_id,
                            create_time=datetime.datetime.utcnow(),
                            update_time=datetime.datetime.utcnow()
                        )
                        
                        session.add(share)
                        success_count += 1
                
                # 提交事务
                if success_count > 0:
                    await session.commit()
                
                make_response(self, 200, "成功", {"kb_id": kb_id})
        except ValueError:
            make_response(self, 400, "无效的ID")
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseShareToDepartmentHandler(BaseHandler):
    """分享知识库给部门"""
    
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_id' not in data or 'to_department' not in data:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 从session获取用户ID
            from_user_id = self.get_user_id()
            if from_user_id is None:
                return
            
            kb_id = int(data.get('kb_id'))
            to_department = data.get('to_department')
            
            async with async_session() as session:
                # 查询知识库是否存在且未被删除
                kb = await session.get(KnowledgeBase, kb_id)
                if not kb or kb.is_deleted == 1:
                    make_response(self, 400, "知识库不存在")
                    return
                
                # 验证用户权限
                if str(kb.user_id) != str(from_user_id):
                    make_response(self, 403, "无权分享该知识库")
                    return
                
                # 检查是否已经分享给该部门
                query = select(KBShareRelation).where(
                    and_(
                        KBShareRelation.kb_id == kb_id,
                        KBShareRelation.from_user_id == from_user_id,
                        KBShareRelation.to_department == to_department
                    )
                )
                result = await session.execute(query)
                existing_share = result.scalar_one_or_none()
                
                if existing_share:
                    make_response(self, 400, "已经分享给该部门")
                    return
                
                # 创建部门分享关系
                share = KBShareRelation(
                    kb_id=kb_id,
                    from_user_id=from_user_id,
                    to_user_id=0,  # 部门分享时，to_user_id设为0
                    to_department=to_department,
                    create_time=datetime.datetime.utcnow(),
                    update_time=datetime.datetime.utcnow()
                )
                
                session.add(share)
                await session.commit()
                
                make_response(self, 200, "分享成功", {
                    "kb_id": kb_id,
                    "to_department": to_department
                })
                
        except ValueError:
            make_response(self, 400, "无效的分页参数")
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")


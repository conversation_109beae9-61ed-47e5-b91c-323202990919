import tornado.web
import json
from models import Phrase, async_session, User
from utils.response import make_response
import logging
from handlers.base_handler import BaseHandler

class PhraseHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            if not data or 'content' not in data:
                make_response(self, 400, "参数错误")
                return
            content = data['content']
            if len(content) > 64:
                make_response(self, 400, "常用语过长，请不要超过64个字")
                return
            user_id = self.get_user_id()
            if user_id is None: return
            async with async_session() as session:
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 403, "用户不存在")
                    return
                result = await session.execute(
                    Phrase.__table__.select().filter(Phrase.content == content, Phrase.user_id == user_id))
                existing_phrase = result.all()
                if existing_phrase:
                    make_response(self, 400, "该内容已存在")
                    return
            new_phrase = Phrase(
                content=content,
                user_id=user_id
            )
            async with async_session() as session:
                session.add(new_phrase)
                await session.commit()
            phrase = {
                "phrase_id": new_phrase.phrase_id,
                "content": new_phrase.content,
                "create_time": new_phrase.create_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                "user_id": new_phrase.user_id
            }
            make_response(self, 200, "success", phrase)
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")

    async def delete(self):
        user_id = self.get_user_id()
        if user_id is None: return

        try:
            phrase_id = self.get_argument('phrase_id', None)
            if not phrase_id:
                make_response(self, 400, "invalid request")
                return
            async with async_session() as session:
                phrase = await session.get(Phrase, phrase_id)
                if phrase:
                    if phrase.user_id != user_id:
                        make_response(self, 403, "用户无权限")
                        return

                    await session.delete(phrase)
                    await session.commit()
                    self.write({"message": "Phrase deleted successfully"})
                else:
                    self.set_status(404)
                    self.write({"message": "Phrase not found"})
        except Exception as e:
            self.set_status(500)
            self.write({"message": f"Error deleting phrase: {str(e)}"})

class PhrasesListHandler(BaseHandler):
    async def get(self):
        try:
            user_id = self.get_user_id()
            if user_id is None: return

            async with async_session() as session:
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 400, "用户不存在")
                    return
                result = await session.execute(Phrase.__table__.select().filter(Phrase.user_id == user_id))
                phrases_data = result.all()
        
                response_phrases = []
                for phrase in phrases_data:
                    response_phrases.append({
                        "phrase_id": phrase.phrase_id,
                        "content": phrase.content,
                        "create_time": phrase.create_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                        "user_id": phrase.user_id
                    })
            make_response(self, 200, "success", {"phrases": response_phrases})
        except Exception as e:
            logging.error(logging.traceback.format_exc())
            make_response(self, 500, f"服务出现故障，请联系管理员")
            return
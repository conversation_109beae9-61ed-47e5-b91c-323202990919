import logging
import json
from models import Dialog, DialogMessage, async_session, DialogMessageToken
from sqlalchemy import and_, update
from utils.response import make_response
from models import User
from handlers.base_handler import BaseHandler

async def validate_user(user_id):
    async with async_session() as session:
        user = await session.get(User, user_id)
        return user is not None

class DialogHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            if not data or 'title' not in data:
                make_response(self, 400, "invalid request")
                return

            user_id = self.get_user_id()
            if user_id is None:return

            if not await validate_user(user_id):
                make_response(self, 403, "用户无权限")
                return
            new_dialog = Dialog(title=data['title'], user_id=user_id)
            async with async_session() as session:
                session.add(new_dialog)
                await session.commit()
            dialog = {
                "title": new_dialog.title,
                "dialog_id": new_dialog.dialog_id,
                "user_id": new_dialog.user_id
            }
            make_response(self, 200, "success", dialog)
            return
        except json.JSONDecodeError as e:
            make_response(self, 400, f"Error decode json: {str(e)}")
            return
        except Exception as e:
            make_response(self, 500, f"Error deleting phrase: {str(e)}")
            return

    async def delete(self):
        try:
            dialog_id = self.get_argument('dialog_id', None)
            if not dialog_id:
                make_response(self, 400, "invalid request")
                return

            user_id = self.get_user_id()
            if user_id is None:return

            async with async_session() as session:
                dialog = await session.get(Dialog, dialog_id)
                if dialog:
                    if dialog.user_id != user_id:
                        make_response(self, 403, "用户无权限")
                        return

                    # 检查并更新 dialog_messages_tokens 的删除状态
                    tokens_result = await session.execute(
                        DialogMessageToken.__table__.select().where(DialogMessageToken.dialog_id == dialog_id)
                    )
                    if tokens_result.first():  # 如果存在相关记录才进行更新
                        await session.execute(
                            update(DialogMessageToken)
                            .where(DialogMessageToken.dialog_id == dialog_id)
                            .values(is_deleted=1)
                        )
                    
                    # 删除对话消息
                    await session.execute(DialogMessage.__table__.delete().where(DialogMessage.dialog_id == dialog_id))
                    await session.delete(dialog)
                    await session.commit()
                    make_response(self, 200, "success")
                    return
                else:
                    make_response(self, 404, "dialog not found")
                    return
        except Exception as e:
            make_response(self, 500, f"Error deleting phrase: {str(e)}")
            return

    async def put(self):
        dialog_id = self.get_argument('dialog_id', None)
        if not dialog_id:
            make_response(self, 400, "invalid request")
            return
        try:
            data = json.loads(self.request.body)
            if not data or 'title' not in data:
                make_response(self, 400, "invalid request")
                return
            async with async_session() as session:
                dialog = await session.get(Dialog, dialog_id)
                if not dialog:
                    make_response(self, 404, "dialog not found")
                    return
                user_id = self.get_user_id()
                if user_id is None:return
                if dialog.user_id != user_id:
                    make_response(self, 403, "用户无权限")
                    return
                dialog.title = data['title']
                await session.commit()
                make_response(self, 200, "success", {
                    "title": dialog.title,
                    "dialog_id": dialog.dialog_id
                })
                return
        except json.JSONDecodeError:
            make_response(self, 400, "invalid request")
            return
        except Exception as e:
            make_response(self, 500, f"Error deleting phrase: {str(e)}")
            return

    async def get(self):
        dialog_id = self.get_argument('dialog_id', None)
        if not dialog_id:
            make_response(self, 400, "invalid request")
            return
        try:
            async with async_session() as session:
                dialog = await session.get(Dialog, dialog_id)
                if not dialog:
                    make_response(self, 404, "dialog not found")
                    return
                user_id = self.get_user_id()
                if user_id is None:return
                if dialog.user_id != user_id:
                    make_response(self, 403, "用户无权限")
                    return
                result = await session.execute(
                        DialogMessage.__table__.select().filter(DialogMessage.dialog_id == dialog_id))
            response_messages = []
            for message in result.all():
                msg = {
                    "id": message.message_id,
                    "role": message.role,
                    "content": message.content,
                    "time": message.time.strftime('%Y-%m-%dT%H:%M:%SZ')
                }
                if message.role == "assistant":
                    msg['extra_info'] = json.loads(message.extra_info) if message.extra_info else {}
                if message.refs:
                    msg['references'] = json.loads(message.refs)
                response_messages.append(msg)
            res_data = {
                "dialog_id": dialog_id,
                "title": dialog.title,
                "messages": response_messages,
            }
            make_response(self, 200, "success", res_data)
            return
        except json.JSONDecodeError:
            make_response(self, 400, "invalid request")
            return
        except Exception as e:
            logging.error(logging.traceback.format_exc())
            make_response(self, 500, f"Error get chat: {str(e)}")
            return

class DialogsListHandler(BaseHandler):
    async def get(self):
        try:
            user_id = self.get_user_id()
            if user_id is None: return
            if not await validate_user(user_id):
                make_response(self, 403, "用户无权限")
                return
            page = int(self.get_argument('page', 1))
            size = int(self.get_argument('size', 20))
            key_word = self.get_argument("key_word", "")

            async with async_session() as session:
                if key_word:
                    result = await session.execute(Dialog.__table__.select().filter(
                        and_(
                            Dialog.user_id == user_id, 
                            Dialog.title.like(f"%{key_word}%")
                        )
                    ))
                else:
                    result = await session.execute(Dialog.__table__.select().filter(Dialog.user_id == user_id))
                dialogs_data = result.all()

            total = len(dialogs_data)
            response_data = []
            dialogs_data.reverse()
            for dialog in dialogs_data:
                response_data.append({
                    "title": dialog.title,
                    "dialog_id": dialog.dialog_id, 
                    "create_time": dialog.create_time.strftime('%Y-%m-%dT%H:%M:%SZ')
                })
            make_response(self, 200, "success", {"dialogs": response_data, "total": total})
            return
        except Exception as e:
            logging.error(logging.traceback.format_exc())
            make_response(self, 500, f"服务出现故障，请联系管理员")
            return
    
class DialogMessageHandler(BaseHandler):
    async def put(self):
        try:
            user_id = self.get_user_id()
            if user_id is None: return
            if not await validate_user(user_id):
                make_response(self, 403, "用户无权限")
                return
            
            data = json.loads(self.request.body)
            if not data or 'message_id' not in data:
                make_response(self, 400, "invalid request")
                return
            
            message_id = data.get("message_id")
            dislike = data.get("dislike", False)
            dislike_reason = data.get("dislike_reason", "")
            like = data.get("like", False)
            like_reason = data.get("like_reason", "")

            if like and dislike:
                make_response(self, 400, "invalid request")
                return
            
            extra_info = {}
            if dislike:
                extra_info = {
                    "dislike": True,
                    "dislike_reason": dislike_reason
                }
            elif like:
                extra_info = {
                    "like": True,
                    "like_reason": like_reason
                }
            
            async with async_session() as session:
                message = await session.get(DialogMessage, message_id)
                if not message:
                    make_response(self, 404, "message not found")
                    return
                dialog = await session.get(Dialog, message.dialog_id)
                if not dialog:
                    make_response(self, 404, "dialog not found")
                    return
                if dialog.user_id != user_id:
                    make_response(self, 403, "用户无权限")
                    return

                message.extra_info = json.dumps(extra_info)
                await session.commit()
                make_response(self, 200, "success", {})
                return
        except Exception as e:
            logging.error(logging.traceback.format_exc())
            make_response(self, 500, f"服务出现故障，请联系管理员")
            return
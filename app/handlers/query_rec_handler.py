import json
import aiohttp
from utils.response import make_response
import config
import time
import logging
from handlers.base_handler import BaseHandler

class QueryRecHandler(BaseHandler):
    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'query' not in data:
                make_response(self, 400, "参数错误")
                return            
            query = data['query']

            # 调用大模型生成大纲
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "system", "content": "你是一个擅长生成相关查询的助手。"},
                            {"role": "user", "content": f"请根据以下查询 '{query}' 推荐3个相关的查询，每个查询占一行。"}
                        ],
                        "temperature": 0.7
                    }
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=30) as response:
                        response.raise_for_status()
                        result = await response.json()
                        related_queries = result["choices"][0]["message"]["content"].strip().split('\n')

                end_ts = int(time.time()*1000)
                logging.info(f"Generate outline took {end_ts - start_ts}ms")     
                make_response(self, 200, "success", related_queries)                    
            except Exception as e:
                make_response(self, 500, f"LLM请求失败: {str(e)}")
                return
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")
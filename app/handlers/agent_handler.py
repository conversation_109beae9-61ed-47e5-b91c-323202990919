from sqlalchemy import update, select, func, delete, and_, or_
import json
import logging
import datetime
import os

from .base_handler import BaseHand<PERSON>
from models import Agent, async_session, User
from utils.response import make_response
from utils.minio import download_file
import config as conf
from deepdoc.file_parser import FileParser
from services.document_parser import parse_semaphore
from models.fault_record import FaultRecords
from models.agent_file_contents import AgentFileContents
from models.data_control import DataControl

file_parser = FileParser()
AGENT_DATA_TYPE = "智能体应用"

class AgentHandler(BaseHandler):

    async def post(self):
        # 创建Agent
        user_id = self.get_user_id()
        if not user_id:
            return
        async with async_session() as session:
            result = await session.execute(
                User.__table__.select().filter(User.user_id == user_id))
            user = result.first()
            if not user or user.permission_level != "admin":
                make_response(self, 403, '权限不足')
                return

        try:
            data = json.loads(self.request.body)
            agent_code = data.get('agent_code')
            agent_name = data.get('agent_name')
            agent_desc = data.get('agent_desc', '')
            agent_icon_path = data.get('agent_icon_path', '')
            dt_dept_code = data.get('dt_dept_code', None)

            if not dt_dept_code:
                make_response(self, 400, '缺少dt_dept_code参数')
                return

            if not agent_code:
                make_response(self, 400, '缺少agent_code参数')
                return
            if not agent_name:
                make_response(self, 400, '缺少agent_name参数')
                return

            async with async_session() as session:
                # 创建Agent
                new_agent = Agent(
                    agent_name=agent_name,
                    agent_code=agent_code,
                    agent_desc=agent_desc,
                    agent_icon_path=agent_icon_path,
                    user_id=user_id,
                )
                session.add(new_agent)
                await session.flush()  # 获取agent_id但不提交
                
                # 创建DataControl
                new_data_control = DataControl(
                    data_type=AGENT_DATA_TYPE,
                    data_id=new_agent.agent_id,
                    dt_type='所在部门',
                    dt_dept_code=dt_dept_code,
                    owner_id=user_id,
                )
                session.add(new_data_control)
                await session.commit()  # 一次性提交所有更改

            make_response(self, 200, 'Agent创建成功', {'agent_id': new_agent.agent_id})
        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'创建失败: {str(e)}')

    async def put(self):
        # 修改Agent
        user_id = self.get_user_id()
        if not user_id:
            return

        async with async_session() as session:
            result = await session.execute(
                User.__table__.select().filter(User.user_id == user_id))
            user = result.first()
            if not user or user.permission_level != "admin":
                make_response(self, 403, '权限不足')
                return

        try:
            data = json.loads(self.request.body)
            if not data:
                make_response(self, 400, 'invalid request')
                return

            agent_id = data.get('agent_id')
            if not agent_id:
                make_response(self, 400, '缺少agent_id参数')
                return

            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return

                if agent.user_id != user_id:
                    make_response(self, 403, '无权限修改他人创建的Agent')
                    return

                # 更新字段
                if 'agent_name' in data:
                    agent.agent_name = data['agent_name']
                if 'agent_code' in data:
                    agent.agent_code = data['agent_code']
                if 'agent_desc' in data:
                    agent.agent_desc = data['agent_desc']
                if 'extro_info' in data:
                    agent.extro_info = data['extro_info']
                if 'agent_icon_path' in data:
                    agent.agent_icon_path = data['agent_icon_path']
                
                agent.update_time = datetime.datetime.now()
                
                await session.commit()
                
                # 返回更新后的Agent信息
                agent_info = {
                    'agent_name': agent.agent_name,
                    'agent_id': agent.agent_id,
                    'agent_code': agent.agent_code,
                    'agent_desc': agent.agent_desc,
                    'agent_icon_path': agent.agent_icon_path,
                    'create_time': agent.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'update_time': agent.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'extro_info': agent.extro_info,
                }
                make_response(self, 200, 'Agent修改成功', agent_info)

        except json.JSONDecodeError:
            make_response(self, 400, 'invalid request')
        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'修改失败: {str(e)}')

    async def delete(self):
        # 删除Agent
        user_id = self.get_user_id()
        if user_id is None:
            return
        async with async_session() as session:
            result = await session.execute(
                User.__table__.select().filter(User.user_id == user_id))
            user = result.first()
            if not user or user.permission_level != "admin":
                make_response(self, 403, '权限不足')
                return

        agent_id = self.get_argument('agent_id', None)
        if not agent_id:
            make_response(self, 400, "缺少agent_id参数")
            return

        try:
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return

                if agent.user_id != user_id:
                    make_response(self, 403, '无权限删除他人创建的Agent')
                    return
    
                # 删除Agent
                await session.delete(agent)
                
                # 同时删除data_control表中的相关数据
                await session.execute(
                    DataControl.__table__.delete().where(
                        DataControl.data_type == AGENT_DATA_TYPE,
                        DataControl.data_id == agent_id
                    )
                )
                
                await session.commit()
            make_response(self, 200, 'Agent删除成功')
        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'删除失败: {str(e)}')

    async def get(self):
        # 查看单个或列表
        user_id = self.get_user_id()
        if not user_id:
            return
        async with async_session() as session:
            result = await session.execute(
                User.__table__.select().filter(User.user_id == user_id))
            user = result.first()
            if not user or user.permission_level != "admin":
                make_response(self, 403, '权限不足')
                return

        agent_id = self.get_argument('agent_id', None)
        if not agent_id:
            make_response(self, 400, "缺少agent_id参数")
            return

        try:
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return
                if agent.user_id != user_id:
                    make_response(self, 403, '无权限查看他人创建的Agent')
                    return
                agent_info = {
                    'agent_name': agent.agent_name,
                    'agent_id': agent.agent_id,
                    'agent_code': agent.agent_code,
                    'agent_desc': agent.agent_desc,
                    'agent_icon_path': agent.agent_icon_path,
                    'create_time': agent.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'update_time': agent.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'extro_info': agent.extro_info
                }
                make_response(self, 200, '查询成功', agent_info)

        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'查询失败: {str(e)}')


class AgentListHandler(BaseHandler):
    async def get(self):
        user_id = self.get_user_id()
        if not user_id:
            return

        try:
            # 从URL查询参数获取数据，而不是从请求体
            page_no = max(int(self.get_argument('pageNo', '1')), 1)
            page_size = max(int(self.get_argument('pageSize', '10')), 1)
            dt_dept_code = self.get_argument('dt_dept_code', None)
            
            if not dt_dept_code:
                make_response(self, 400, '缺少dt_dept_code参数')
                return
            
            # 计算偏移量
            offset = (page_no - 1) * page_size
            
            async with async_session() as session:           
                # 1. 查询所有agent及其权限控制信息
                query = select(Agent, DataControl).outerjoin(
                    DataControl,
                    DataControl.data_id == Agent.agent_id
                )
                
                # 2. 添加权限过滤条件
                permission_conditions = or_(
                    # 没有权限控制记录的agent（默认可见）
                    DataControl.dt_id.is_(None),
                    
                    DataControl.dt_type == '公开',  # 公开的agent
                    
                    and_(
                        DataControl.dt_type == '私有',  # 私有但是owner是当前用户的agent
                        DataControl.owner_id == user_id
                    ),
                    
                    and_(
                        DataControl.dt_type == '所在部门',  # 所在部门：dt_dept_code与用户部门完全一致
                        DataControl.dt_dept_code == dt_dept_code,
                    ),

                    and_(
                        DataControl.dt_type == '指定部门',  # 指定部门：用户部门在dt_dept_code_list中
                        or_(
                            DataControl.dt_dept_code_list.like(f'%,{dt_dept_code},%'),  # 包含用户部门
                        ),
                    )
                )
                
                query = query.where(
                    and_(
                        DataControl.data_type == AGENT_DATA_TYPE,
                        permission_conditions
                    )
                )
                
                # 先查询总数
                count_query = select(func.count()).select_from(
                    query.subquery()
                )
                count_result = await session.execute(count_query)
                total = count_result.scalar()
                
                # 查询分页数据
                result = await session.execute(
                    query.offset(offset).limit(page_size)
                )
                agent_data = result.all()
                
                results = []
                for agent, data_control in agent_data:
                    agent_info = {
                        'agent_name': agent.agent_name,
                        'agent_id': agent.agent_id,
                        'agent_code': agent.agent_code,
                        'agent_desc': agent.agent_desc,
                        'agent_icon_path': agent.agent_icon_path,
                        'create_time': agent.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'update_time': agent.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'extro_info': agent.extro_info,
                        'editable': agent.user_id == user_id
                    }
                    results.append(agent_info)
                
                # 返回包含total和list的数据结构
                response_data = {
                    'total': total,
                    'list': results,
                    'pageNo': page_no,
                    'pageSize': page_size
                }
                
                make_response(self, 200, '查询成功', response_data)
            
        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'查询失败: {str(e)}')


class AgentFileParser(BaseHandler):
    async def post(self):
        user_id =self.get_user_id()
        if not user_id:
            return

        try:
            # 解析请求参数
            data = json.loads(self.request.body)
            agent_id = data.get('agent_id')
            file_path = data.get('file_path')
            file_name = data.get('file_name')

            if not agent_id:
                make_response(self, 400, '缺少agent_id参数')
                return

            # 查询智能体信息
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return
            
            if agent_id == 2:
                await self.parse_fault_report(agent_id, user_id, file_path, file_name)
            else:
                await self.parse_common_file(agent_id, user_id, file_path, file_name)
            
            make_response(self, 200, '文件解析中')

        except Exception as e:
            logging.error(f"agent文件解析失败{e}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'agent文件解析失败: {str(e)}')

    async def parse_common_file(self, agent_id: int, user_id: int, file_path: str, file_name: str):
        """
        通用解析接口
        """
        # 1. 先删除数据库中已有的数据
        async with async_session() as session:
            await session.execute(
                delete(AgentFileContents).where(
                    (AgentFileContents.agent_id == agent_id) & 
                    (AgentFileContents.user_id == user_id)
                )
            )
            await session.commit()
        
        # 2. 插入一条新记录
        async with async_session() as session:
            file_content = AgentFileContents(
                agent_id=agent_id,
                user_id=user_id,
                file_contents='',
                parser_status=1,
            )
            session.add(file_content)
            await session.commit()

        # 3. 解析文件
        os.makedirs(conf.TEMP_DIR, exist_ok=True)
        local_path = os.path.join(conf.TEMP_DIR, f"{user_id}_{file_name}")
        await download_file(file_path, local_path)

        # 使用信号量控制并发处理
        async with parse_semaphore:
            extract_res = await file_parser.extract(local_path)
            file_content = extract_res.md_content
            os.remove(local_path)
            if os.path.exists(extract_res.preview_file_local_path):
                os.remove(extract_res.preview_file_local_path)

        # 4. 更新记录
        async with async_session() as session:
                await session.execute(
                    update(AgentFileContents).where(
                        (AgentFileContents.agent_id == agent_id) & 
                        (AgentFileContents.user_id == user_id)
                    ).values(
                        file_contents=file_content,
                        parser_status=2,
                    )
                )
                await session.commit()

    async def parse_fault_report(self, agent_id: int, user_id: int, file_path: str, file_name: str):
        """解析故障报告Excel"""
        try:
            os.makedirs(conf.TEMP_DIR, exist_ok=True)
            local_path = os.path.join(conf.TEMP_DIR, f"{user_id}_{file_name}")
            await download_file(file_path, local_path)

            import pandas as pd
            df = pd.read_excel(local_path)
            result = df.to_dict(orient='records')

            # 将故障写入数据库
            async with async_session() as session:
                for item in result:
                    fault = FaultRecords(
                        agent_id=agent_id,
                        user_id=user_id,
                        fault_type=self._safe_get(item, '故障类型'),
                        fault_reason=self._safe_get(item, '故障原因'),
                        fault_content=self._safe_get(item, '故障描述'),
                        fault_severity=self._safe_get(item, '故障等级'),
                        fault_time=self._safe_get(item, '故障时间') or None,
                    )
                    session.add(fault)

                await session.commit()

            return result
        except Exception as e:
            logging.error(f"故障报告解析失败: {str(e)}")
            raise
    
    def _safe_get(self, item: dict, key: str) -> str:
        val = item.get(key)
        return val.strip() if val and isinstance(val, str) else ''

import tornado
import tornado.escape
import tornado.httpclient  # 导入AsyncHTTPClient
import logging
import json
import time
import re
from .base_handler import BaseHandler  # 假设BaseHandler 在同级目录的 base_handler.py
from utils.zhiyu import decrypt, encrypt  # 假设加解密函数路径
import config  # 导入配置文件
import markdown
from bs4 import BeautifulSoup



# from utils.response import make_response # 未使用，移除了，因有自定义的response

def markdown_to_plain_text(md_string):
    if not md_string:
        return ""
    try:
        # 1. 将 Markdown 转换为 HTML
        html = markdown.markdown(md_string)
        # 2. 从 HTML 中提取纯文本
        soup = BeautifulSoup(html, "html.parser")
        plain_text = soup.get_text(separator="\n", strip=True)  # separator="\n" 尝试保留换行
        return plain_text
    except Exception as e:
        logging.error(f"Markdown转纯文本失败: {e}")
        return md_string  # 转换失败则返回原始Markdown

class ZhiyuCallbackHandler(BaseHandler):
    _token_cache = {}
    _TOKEN_EXPIRES_IN_SECONDS = 7200      # <<< 修改：直接使用官方确认的7200秒
    _TOKEN_REFRESH_AHEAD_SECONDS = 300

    def response(self, code, data_to_encrypt_or_plain_data):
        """
        统一响应方法。
        如果 data_to_encrypt_or_plain_data 是字符串且我们要对其加密（如 "success"），
        则会对其进行加密。如果已经是加密好的数据（如 check_url 返回），则直接使用。
        对于错误情况，data_to_encrypt_or_plain_data 可以是普通字符串错误消息。
        """
        self.set_status(code)
        self.set_header("Content-Type", "application/json; charset=UTF-8")

        response_data = {}
        if isinstance(data_to_encrypt_or_plain_data, str) and code == 200:  # 成功时，对简单字符串进行加密
            # 对于成功回调，知语平台通常期望一个加密的响应
            # 注意：这里的 encrypt 函数需要能够处理 nonce。
            # 如果是 check_url，nonce 来自请求。
            # 如果是普通事件响应，可能需要生成新的 nonce，或者知语平台不严格要求响应中的 nonce 与请求一致。
            # 我们这里假设可以使用请求中的 nonce。
            current_nonce = ""
            try:
                # 尝试从请求体中获取 nonce，用于加密响应
                # 这是一个简化处理，实际中您可能需要更可靠地获取或传递 nonce
                if self.request.body:
                    body_data = json.loads(self.request.body.decode('utf-8', errors='ignore'))
                    current_nonce = body_data.get('nonce', '')
            except Exception:
                logging.warning("无法从请求体解析 nonce 用于加密响应，将使用空 nonce")

            # 如果 data_to_encrypt_or_plain_data 已经是加密函数期望的格式（如字典），
            # 那么 encrypt 函数应该能处理。这里我们假设 encrypt 的第三个参数是 timestamp (可以为None),
            # 第四个是 nonce, 第五个是要加密的文本。
            # 对于 check_url, 我们加密 "success"。对于其他成功事件，也加密 "success"。
            encrypted_payload = encrypt(config.ZHIYU_APP_KEY, "", None, current_nonce, data_to_encrypt_or_plain_data)
            response_data = encrypted_payload  # encrypt 返回的应该已经是类似 {"encrypt": "..."} 的结构
        elif isinstance(data_to_encrypt_or_plain_data, dict):  # 如果已经是准备好的json体
            response_data = data_to_encrypt_or_plain_data
        else:  # 错误消息等，直接封装
            response_data = {"errmsg": data_to_encrypt_or_plain_data, "errcode": code if code != 200 else -1}

        response_body_str = json.dumps(response_data, ensure_ascii=False)
        self.write(response_body_str)

        logging.info(f"响应状态码: {code}")  # 改为info级别，warning用于更重要的事情
        logging.info(f"响应体内容 (JSON str): {response_body_str}")

    def unpad_pkcs7(self, s_bytes_or_str):
        """
        从字节串或字符串中移除 PKCS#7 填充。
        如果输入是字符串，假设它是单字节字符表示的填充。
        更健壮的做法是始终在字节层面操作。
        """
        if not s_bytes_or_str:
            return s_bytes_or_str

        if isinstance(s_bytes_or_str, str):
            # 处理字符串输入
            try:
                last_char_val = ord(s_bytes_or_str[-1])
                if 0 < last_char_val <= len(s_bytes_or_str):
                    padding_chars = s_bytes_or_str[-last_char_val:]
                    is_padding_correct = all(ord(char) == last_char_val for char in padding_chars)
                    if is_padding_correct:
                        return s_bytes_or_str[:-last_char_val]
            except IndexError:  # 空字符串或其他问题
                return s_bytes_or_str
            return s_bytes_or_str  # 填充无效或不存在
        elif isinstance(s_bytes_or_str, bytes):
            # 处理字节串输入 (推荐)
            if not s_bytes_or_str:
                return s_bytes_or_str
            padding_len = s_bytes_or_str[-1]  # 获取最后一个字节作为填充长度
            if 0 < padding_len <= len(s_bytes_or_str):
                # 检查所有填充字节是否都等于 padding_len
                if s_bytes_or_str[-padding_len:] == bytes([padding_len]) * padding_len:
                    return s_bytes_or_str[:-padding_len]
            return s_bytes_or_str  # 填充无效或不存在
        else:
            # 不支持的类型
            logging.error(f"unpad_pkcs7 不支持的类型: {type(s_bytes_or_str)}")
            return s_bytes_or_str



    async def _get_zhiyu_token(self, corpid: str, appid: str) -> str | None:
        """异步获取知语 Access Token，带缓存逻辑，使用固定的7200秒有效期。"""
        cache_key = (corpid, appid)
        current_time = time.time()

        # 检查缓存
        if cache_key in self._token_cache:
            cached_entry = self._token_cache[cache_key]
            if cached_entry["expires_at"] > current_time + self._TOKEN_REFRESH_AHEAD_SECONDS:
                logging.info(f"使用缓存的Token for ({corpid}, {appid})")
                return cached_entry["token"]
            else:
                logging.info(
                    f"缓存的Token for ({corpid}, {appid}) 即将过期或已过期 (expires_at: {cached_entry['expires_at']}, current_time: {current_time}), 需要刷新。")
        else:
            logging.info(f"没有找到缓存的Token for ({corpid}, {appid})，将请求新的Token。")

        # 缓存未命中或已过期，请求新的Token
        http_client = tornado.httpclient.AsyncHTTPClient()
        url = f"{config.ZHIYU_API_BASE_URL}/v2/gettoken?corpid={corpid}&appid={appid}"
        logging.info(f"请求新Token URL: {url}")
        try:
            response = await http_client.fetch(url, method="GET")
            if response.code == 200:
                data = json.loads(response.body.decode('utf-8'))
                logging.info(f"获取新Token响应: {data}")
                if str(data.get("errcode")) == "0":
                    access_token = data.get("access_token")
                    if access_token:
                        # 使用固定的有效期 7200 秒
                        expires_at = current_time + self._TOKEN_EXPIRES_IN_SECONDS
                        self._token_cache[cache_key] = {
                            "token": access_token,
                            "expires_at": expires_at
                        }
                        logging.info(
                            f"新Token for ({corpid}, {appid}) 已获取并缓存，有效期至: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expires_at))} (固定有效期: {self._TOKEN_EXPIRES_IN_SECONDS}s)")
                        return access_token
                    else:
                        logging.error(f"获取新Token成功但响应中access_token为空: {data}")
                        return None
                else:
                    logging.error(
                        f"获取新Token API调用失败: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")
                    return None
            else:
                logging.error(
                    f"获取新Token HTTP请求失败: 状态码 {response.code}, 响应: {response.body.decode('utf-8', errors='ignore')}")
                return None
        except tornado.httpclient.HTTPClientError as e:
            logging.error(f"获取新Token HTTPClientError: {e.code if hasattr(e, 'code') else 'N/A'} - {e}")
            return None
        except json.JSONDecodeError as e:
            logging.error(
                f"获取新Token响应JSON解析失败: {e}. Body: {response.body.decode('utf-8', errors='ignore') if response else 'N/A'}")
            return None
        except Exception as e:
            logging.error(f"获取新Token时发生未知错误: {e}", exc_info=True)
            return None

    async def _send_zhiyu_message(self, access_token: str, to_single_uid: str, chat_id: str, content: str) -> bool:
        """异步发送知语机器人消息"""
        http_client = tornado.httpclient.AsyncHTTPClient()
        url = f"{config.ZHIYU_API_BASE_URL}/v2/message/bot_send_to_conversation?access_token={access_token}"
        payload = {
            "to_single_uid": to_single_uid,
            "type": "text",
            "message": {"content": content},
            "chat_id": chat_id  # 根据您提供的curl，chat_id也是需要的
        }
        body = json.dumps(payload, ensure_ascii=False)
        logging.info(f"发送消息 URL: {url}")
        logging.info(f"发送消息 Payload: {body}")
        try:
            response = await http_client.fetch(
                url,
                method="POST",
                headers={"Content-Type": "application/json; charset=UTF-8"},
                body=body.encode('utf-8')  # POST body需要编码
            )
            if response.code == 200:
                data = json.loads(response.body.decode('utf-8'))
                logging.info(f"发送消息响应: {data}")
                if data.get("errcode") == "0" or data.get("errcode") == 0:  # 兼容字符串和数字0
                    logging.info(f"消息成功发送至 {to_single_uid}")
                    return True
                else:
                    logging.error(f"发送消息失败: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")
                    return False
            else:
                logging.error(
                    f"发送消息 HTTP请求失败: 状态码 {response.code}, 响应: {response.body.decode('utf-8', errors='ignore')}")
                return False
        except tornado.httpclient.HTTPClientError as e:
            logging.error(f"发送消息 HTTPClientError: {e.response.code if e.response else 'N/A'} - {e}")
            return False
        except json.JSONDecodeError as e:
            logging.error(f"发送消息响应JSON解析失败: {e}")
            return False
        except Exception as e:
            logging.error(f"发送消息时发生未知错误: {e}", exc_info=True)
            return False
        finally:
            pass

    async def _get_chat_completion_answer(self, question: str) -> str | None:
        """
        调用外部聊天API获取答案。
        包含针对API返回401但响应体中可能仍有数据的临时处理逻辑。
        """
        if not config.CHAT_COMPLETION_API_URL or not config.CHAT_COMPLETION_API_TOKEN:
            logging.error("聊天API的URL或TOKEN未配置。")
            return None

        http_client = tornado.httpclient.AsyncHTTPClient()
        url = config.CHAT_COMPLETION_API_URL
        payload = {
            "question": question,
            "enable_rag": False
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.CHAT_COMPLETION_API_TOKEN}"
        }

        body_str = json.dumps(payload, ensure_ascii=False)
        encoded_body = body_str.encode('utf-8')

        logging.warning(f"请求聊天补全 API URL: {url}")
        logging.warning(f"配置文件中的 CHAT_COMPLETION_API_TOKEN: '{config.CHAT_COMPLETION_API_TOKEN}'")
        logging.warning(f"发送给API的 Authorization 头部: '{headers['Authorization']}'")
        logging.warning(f"聊天补全 API JSON Body (str): {body_str}")
        logging.warning(f"聊天补全 API Encoded Body (bytes repr): {repr(encoded_body)}")
        logging.warning(f"聊天补全 API Encoded Body Length: {len(encoded_body)}")

        request = tornado.httpclient.HTTPRequest(
            url,
            method="POST",
            headers=headers,
            body=encoded_body,
            connect_timeout=10,
            request_timeout=30
        )

        try:
            response = await http_client.fetch(request)
            # 正常的成功路径
            if response.code == 200:
                try:
                    data = json.loads(response.body.decode('utf-8'))
                    logging.info(f"聊天补全 API 响应 (200 OK): {json.dumps(data, ensure_ascii=False, indent=2)}")
                    # 检查API内部的code和message是否也是成功
                    if data.get("code") == 200 and data.get("message") == "success":
                        answer = data.get("data", {}).get("answer")
                        if answer is not None:
                            return str(answer)
                        else:
                            logging.warning("聊天补全 API 成功 (200 OK) 但响应中 'data.answer' 为空或不存在。")
                            return None  # 或者返回一个默认值，或者让调用方处理
                    else:  # API内部返回错误码
                        logging.error(
                            f"聊天补全 API 逻辑失败 (HTTP 200 但内部code/message指示错误): code={data.get('code')}, message={data.get('message')}")
                        return None
                except json.JSONDecodeError as je:
                    body_preview = response.body.decode('utf-8', errors='ignore')[:500]
                    logging.error(f"聊天补全 API 响应 (200 OK) JSON解析失败: {je}. Body preview: {body_preview}")
                    return None
            else:  # HTTP状态码不是200
                logging.error(
                    f"聊天补全 API HTTP请求失败: 状态码 {response.code}, 响应: {response.body.decode('utf-8', errors='ignore')}")
                return None

        except tornado.httpclient.HTTPClientError as e:
            logging.error(f"聊天补全 API HTTPClientError: {e.code if hasattr(e, 'code') else 'N/A'} - {e}")
            if e.response and e.response.code == 401:
                logging.warning("收到401错误，尝试从响应体中提取后续的成功数据 (临时措施)...")
                try:
                    body_content = e.response.body.decode('utf-8', errors='ignore')

                    lines = body_content.splitlines()  # 按行分割

                    # 从后往前遍历每一行
                    for line in reversed(lines):
                        line = line.strip()  # 去除行首尾的空白字符
                        if line.startswith("data:"):
                            json_str_candidate = line[len("data:"):].strip()  # 提取 "data: " 后面的部分
                            logging.warning(f"找到以 'data:' 开头的行，提取的JSON候选串: {json_str_candidate[:300]}...")

                            if not json_str_candidate:  # 如果 "data: " 后面是空的，跳过
                                logging.debug("  'data:' 后面内容为空，跳过。")
                                continue

                            try:
                                potential_json = json.loads(json_str_candidate)

                                if isinstance(potential_json, dict) and \
                                        potential_json.get("code") == 200 and \
                                        "data" in potential_json and \
                                        isinstance(potential_json.get("data"), dict) and \
                                        "answer" in potential_json.get("data"):

                                    answer_value = potential_json["data"]["answer"]
                                    if answer_value is not None:
                                        extracted_answer = markdown_to_plain_text(str(answer_value))
                                        logging.info(
                                            f"从401响应体的 'data:' 行成功提取到 answer: '{extracted_answer[:100]}...'")
                                        return extracted_answer  # 找到符合条件的第一个（从后往前）就返回
                                    else:
                                        logging.warning(
                                            "  从 'data:' 行找到成功JSON结构，但 'data.answer' 为 null 或未设置。")
                                elif isinstance(potential_json, dict) and "content" in potential_json:
                                    # 这是为了处理那些 data: {"content": "..."} 的中间流式行
                                    logging.debug(
                                        f"  解析到 'data:' 行的 'content' JSON: {potential_json.get('content')}")
                                else:
                                    logging.debug(
                                        f"  解析的 'data:' 行JSON结构不符合预期的成功响应: {json_str_candidate[:200]}")

                            except json.JSONDecodeError as je_inner:
                                # 这种情况是预料之中的，因为 body_content_str 的第一部分
                                # {"code": 401, ...}data: {"content": ...}
                                # 在按行分割后，第一行可能是 `{"code": 401, ...}data: {"content": ...}`
                                # 或者如果 "data:" 前面没有换行，那么某一行可能不是以 "data:" 开头，
                                # 或者 "data:" 后面的不是完整JSON
                                logging.warning(
                                    f"  尝试解析 'data:' 行的JSON内容失败: {je_inner}. 内容: {json_str_candidate[:200]}")
                            except Exception as ex_inner:
                                logging.warning(
                                    f"  处理 'data:' 行的JSON内容时发生未知错误: {ex_inner}. 内容: {json_str_candidate[:200]}")
                        # 如果行不是以 "data:" 开头，则忽略 (除非你有其他逻辑需要处理非 "data:" 开头的行)
                        # else:
                        #    logging.debug(f"  忽略非 'data:' 开头的行: {line[:100]}")

                    if extracted_answer is None:
                        logging.warning("遍历完所有行后，未能从 'data:' 开头的行中提取到有效的 answer。")

                except Exception as ex_outer:
                    logging.error(f"处理401响应体时发生预料之外的错误: {ex_outer}", exc_info=True)

                return extracted_answer  # 如果最终没找到，则返回 None

        except json.JSONDecodeError as e:  # 这个通常不会在这里触发，因为 HTTPClientError 会先捕获
            body_preview = e.response.body.decode('utf-8', errors='ignore')[:200] if hasattr(e,
                                                                                             'response') and e.response and e.response.body else "N/A"
            logging.error(f"聊天补全 API 响应JSON解析失败 (外层): {e}. Body preview: {body_preview}")
            return None
        except Exception as e:
            logging.error(f"调用聊天补全 API 时发生未知错误: {e}", exc_info=True)
            return None
        return None  # 默认返回 None，确保所有代码路径都有返回

    async def post(self):
        try:
            logging.info("接收到知语回调请求...")
            raw_body = self.request.body.decode('utf-8', errors='ignore')
            logging.debug(f"原始请求体 (raw_body): {raw_body}")  # 改为debug级别，因为可能很长

            data = json.loads(raw_body)
            msgSignature = data.get('msgSignature', '')
            encrypted_text = data.get('encrypt', '')
            timeStamp = data.get('timeStamp', '')
            nonce = data.get('nonce', '')

            logging.debug(f"解析参数: msgSignature='{msgSignature}', timeStamp='{timeStamp}', nonce='{nonce}'")
            # logging.debug(f"encrypted_text: {encrypted_text}") # 加密文本可能很长
            # logging.debug(f"app_key: {config.ZHIYU_APP_KEY}")

            if not all([msgSignature, encrypted_text, timeStamp, nonce]):
                logging.warning("请求缺少必要参数")
                self.response(400, {"errcode": 40001, "errmsg": "缺少参数"})  # 返回JSON错误体
                return

            # 解密消息
            # 假设 decrypt 返回的是字符串或者可以被 unpad_pkcs7 处理的字节串
            decrypted_data_raw = decrypt(config.ZHIYU_APP_KEY, "", msgSignature, timeStamp, nonce, encrypted_text)
            logging.debug(f"解密后的原始数据 (repr): {repr(decrypted_data_raw)}")

            # 去除PKCS7填充
            # 确保 unpad_pkcs7 能正确处理 decrypt 的输出类型 (str or bytes)
            unpadded_data = self.unpad_pkcs7(decrypted_data_raw)
            if isinstance(unpadded_data, bytes):  # 如果 unpad_pkcs7 返回 bytes，需要解码
                try:
                    unpadded_data_str = unpadded_data.decode('utf-8')
                except UnicodeDecodeError as ude:
                    logging.error(f"UTF-8解码去填充后的数据失败: {ude}. Data (repr): {repr(unpadded_data)}")
                    self.response(500, {"errcode": 50002, "errmsg": "解密后数据解码失败"})
                    return
            else:  # 假设已经是字符串
                unpadded_data_str = unpadded_data

            logging.debug(f"去填充后的数据 (repr): {repr(unpadded_data_str)}")

            try:
                json_mesg = json.loads(unpadded_data_str)
            except json.JSONDecodeError as je:
                logging.error(f"解析解密后的JSON失败: {je}. Data: '{unpadded_data_str}'")
                # 即使JSON解析失败，也尝试友好回复，因为可能是check_url这类简单文本
                if unpadded_data_str.strip() == "check_url":  # 有时平台会发纯文本
                    json_mesg = {"event_type": "check_url"}
                else:
                    self.response(400, {"errcode": 40002, "errmsg": "解密内容非JSON格式或格式错误"})
                    return

            logging.info(f"解密后的JSON内容: {json.dumps(json_mesg, ensure_ascii=False, indent=2)}")

            event_type = json_mesg.get('event_type', '')

            if event_type == 'check_url':
                logging.info("处理 check_url 事件")
                # check_url 的响应需要加密 "success"
                # encrypt 函数应该返回一个包含 "encrypt" 字段的字典
                encrypted_success_payload = encrypt(config.ZHIYU_APP_KEY, "", msgSignature, nonce, "success")
                self.response(200, encrypted_success_payload)  # 直接传递 encrypt 返回的字典
                return

            elif event_type == 'p2p_chat_receive_msg':
                logging.info("处理 p2p_chat_receive_msg 事件")
                corp_id = json_mesg.get('corp_id', '')
                appid = json_mesg.get('appid', '')
                event_details = json_mesg.get('event', {})

                sender_account = event_details.get('sender_account', '')
                sender_uid = event_details.get('sender_uid', '')
                message_data = event_details.get('message', {})

                chat_id = message_data.get('chat_id', '')  # 这个 chat_id 机器人返回用不上
                chat_type = message_data.get('chat_type', '')
                msg_type = message_data.get('type', '')
                msg_content = message_data.get('content', '')
                message_id = message_data.get('message_id', '')

                logging.info(
                    f"收到P2P消息: corp_id='{corp_id}', appid='{appid}', "
                    f"sender_account='{sender_account}', sender_uid='{sender_uid}', "
                    f"chat_id='{chat_id}', chat_type='{chat_type}', msg_type='{msg_type}', "
                    f"content='{msg_content}', message_id='{message_id}'"
                )

                if not sender_uid :
                    logging.warning("p2p_chat_receive_msg 事件缺少 sender_uid ，无法回复")
                    # 仍然需要给知语平台一个成功响应，表示事件已接收
                    self.response(200, "success")  # 加密 "success"
                    return

                # 1. 调用聊天API获取回复
                reply_text = ""
                chat_api_answer = await self._get_chat_completion_answer(msg_content)

                if chat_api_answer is not None:  # chat_api_answer 可能是空字符串，这也是有效回复
                    reply_text = chat_api_answer
                else:
                    logging.warning(f"未能从聊天API获取回复 for question: '{msg_content}'. 使用备用回复。")
                    # 您可以选择一个更友好的备用回复
                    reply_text = f"抱歉，我现在无法处理您的请求 '{msg_content[:20]}{'...' if len(msg_content) > 20 else ''}'。"



                # 1. 获取Token
                # 注意：这里的 corpid 和 appid 是从接收到的消息中获取的。
                # 如果机器人有自己固定的 corpid/appid 来获取 token，请使用 config中的。
                # 根据 curl 示例，corpid 和 appid 是动态的，所以从消息中获取是合适的。
                access_token = await self._get_zhiyu_token(corp_id, appid)

                if access_token:
                    # 2. 发送回复消息
                    #reply_bot_text = f"机器人自动回复：已收到您的消息 '{reply_text}'。"
                    reply_bot_text = reply_text

                    send_success = await self._send_zhiyu_message(
                        access_token,
                        sender_uid,  # 回复给发送者
                        chat_id,  # 使用收到的消息的 chat_id
                        reply_bot_text
                    )
                    if send_success:
                        logging.info(f"成功回复消息给 {sender_uid}")
                    else:
                        logging.warning(f"回复消息给 {sender_uid} 失败")
                else:
                    logging.warning("获取 access_token 失败，无法自动回复消息")

                # 无论机器人是否成功回复，都需要给知语平台一个成功接收事件的响应
                self.response(200, "success")  # 加密 "success"
                return

            else:
                logging.warning(f"未处理的事件类型: {event_type}")
                # 对于未明确处理的事件，也返回成功，表示已接收
                self.response(200, "success")  # 加密 "success"
                return

        except json.JSONDecodeError as e:
            logging.error(f"请求体JSON解析失败: {str(e)}", exc_info=True)
            self.response(400, {"errcode": 40003, "errmsg": f"请求体非JSON格式或格式错误: {str(e)}"})
        except Exception as e:
            logging.error(f"知语回调处理失败: {str(e)}", exc_info=True)  # exc_info=True 会记录堆栈跟踪
            # 避免将详细的异常信息直接暴露给客户端，但对于内部错误，这是一个通用消息
            self.response(500, {"errcode": 50001, "errmsg": "服务器内部错误"})
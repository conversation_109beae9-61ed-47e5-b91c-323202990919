from models import async_session, KnowledgeBase, Dialog, Article, ArticleSummary, DialogMessageToken
import json
import logging
from utils.response import make_response
from handlers.base_handler import BaseHandler
from sqlalchemy import select, func, and_

class StatsQAHandler(BaseHandler):
    """问答统计处理器"""
    
    async def get(self):
        """获取问答统计信息"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        start_tm = self.get_argument('start_tm', None)
        end_tm = self.get_argument('end_tm', None)
            
        try:
            async with async_session() as session:
                # 构建查询条件
                dialog_conditions = []
                article_conditions = []
                summary_conditions = []
                
                if start_tm:
                    dialog_conditions.append(Dialog.create_time >= start_tm)
                    article_conditions.append(Article.create_time >= start_tm)
                    summary_conditions.append(ArticleSummary.create_time >= start_tm)
                if end_tm:
                    dialog_conditions.append(Dialog.create_time <= end_tm)
                    article_conditions.append(Article.create_time <= end_tm)
                    summary_conditions.append(ArticleSummary.create_time <= end_tm)
                
                # 统计所有对话次数（从DialogMessageToken表统计唯一dialog_id数量）
                qa_count_query = select(func.count(func.distinct(DialogMessageToken.dialog_id))).select_from(DialogMessageToken)
                if dialog_conditions:
                    qa_count_query = qa_count_query.where(and_(*dialog_conditions))
                qa_count = await session.scalar(qa_count_query) or 0
                
                # 统计所有文档写作次数
                write_doc_query = select(func.count()).select_from(Article)
                if article_conditions:
                    write_doc_query = write_doc_query.where(and_(*article_conditions))
                write_doc_count = await session.scalar(write_doc_query) or 0
                
                # 统计所有阅读总结次数
                read_summary_query = select(func.count()).select_from(ArticleSummary)
                if summary_conditions:
                    read_summary_query = read_summary_query.where(and_(*summary_conditions))
                read_summary_count = await session.scalar(read_summary_query) or 0
                
                result = {
                    "qa_count": qa_count,
                    "write_doc_count": write_doc_count,
                    "read_summary_count": read_summary_count,
                    "total_count": qa_count + write_doc_count + read_summary_count,
                    "start_time": start_tm,
                    "end_time": end_tm
                }
                make_response(self, 200, "获取问答统计成功", result)
                
        except Exception as e:
            logging.error(f"获取问答统计失败: {e}")
            make_response(self, 500, "服务器内部错误")

class StatsTokensHandler(BaseHandler):
    """Tokens统计处理器"""
    
    async def get(self):
        """获取Tokens统计信息"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        start_tm = self.get_argument('start_tm', None)
        end_tm = self.get_argument('end_tm', None)
            
        try:
            async with async_session() as session:
                # 构建查询条件
                conditions = []
                if start_tm:
                    conditions.append(Article.create_time >= start_tm)
                    conditions.append(ArticleSummary.create_time >= start_tm)
                if end_tm:
                    conditions.append(Article.create_time <= end_tm)
                    conditions.append(ArticleSummary.create_time <= end_tm)
                
                # 统计文章生成的tokens
                article_tokens = select(
                    func.sum(Article.input_tokens).label('input'),
                    func.sum(Article.output_tokens).label('output')
                ).select_from(Article)
                if conditions:
                    article_tokens = article_tokens.where(and_(*conditions[:2]))  # Article相关条件
                article_result = await session.execute(article_tokens)
                article_tokens_data = article_result.fetchone()
                
                # 统计文章总结的tokens
                summary_tokens = select(
                    func.sum(ArticleSummary.input_tokens).label('input'),
                    func.sum(ArticleSummary.output_tokens).label('output')
                ).select_from(ArticleSummary)
                if conditions:
                    summary_tokens = summary_tokens.where(and_(*conditions[2:]))  # ArticleSummary相关条件
                summary_result = await session.execute(summary_tokens)
                summary_tokens_data = summary_result.fetchone()
                
                # 统计对话消息的tokens
                dialog_tokens = select(
                    func.sum(DialogMessageToken.input_tokens).label('input'),
                    func.sum(DialogMessageToken.output_tokens).label('output')
                ).select_from(DialogMessageToken)
                if start_tm:
                    dialog_tokens = dialog_tokens.where(DialogMessageToken.create_time >= start_tm)
                if end_tm:
                    dialog_tokens = dialog_tokens.where(DialogMessageToken.create_time <= end_tm)
                dialog_result = await session.execute(dialog_tokens)
                dialog_tokens_data = dialog_result.fetchone()
                
                # 计算总量，并转换为整数
                input_tokens = (
                    int(article_tokens_data.input or 0) + 
                    int(summary_tokens_data.input or 0) + 
                    int(dialog_tokens_data.input or 0)
                )
                output_tokens = (
                    int(article_tokens_data.output or 0) + 
                    int(summary_tokens_data.output or 0) + 
                    int(dialog_tokens_data.output or 0)
                )
                
                result = {
                    "input_tokens_count": input_tokens,
                    "output_tokens_count": output_tokens,
                    "all_tokens_count": input_tokens + output_tokens,
                    "start_time": start_tm,
                    "end_time": end_tm
                }
                make_response(self, 200, "获取Tokens统计成功", result)
                
        except Exception as e:
            logging.error(f"获取Tokens统计失败: {e}")
            make_response(self, 500, "服务器内部错误")

class StatsKBHandler(BaseHandler):
    """知识库统计处理器"""
    
    async def get(self):
        """获取知识库统计信息"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        start_tm = self.get_argument('start_tm', None)
        end_tm = self.get_argument('end_tm', None)
            
        try:
            async with async_session() as session:
                kb_conditions = [KnowledgeBase.is_deleted == 0]
                if start_tm:
                    kb_conditions.append(KnowledgeBase.create_time >= start_tm)
                if end_tm:
                    kb_conditions.append(KnowledgeBase.create_time <= end_tm)
                
                kb_count_query = select(func.count()).select_from(KnowledgeBase).where(
                    and_(*kb_conditions)
                )
                kb_count = await session.scalar(kb_count_query)
                
                # 统计有效的 doc_ids 数量
                kb_doc_count_query = select(
                    func.sum(
                        func.IF(
                            func.JSON_VALID(KnowledgeBase.doc_ids),
                            func.JSON_LENGTH(
                                func.JSON_UNQUOTE(  # 去除多余的引号
                                    func.JSON_EXTRACT(
                                        KnowledgeBase.doc_ids,
                                        '$'
                                    )
                                )
                            ),
                            0
                        )
                    )
                ).where(and_(*kb_conditions))
                kb_doc_count = await session.scalar(kb_doc_count_query) or 0
                
                result = {
                    "kb_count": kb_count,
                    "kb_doc_count": int(kb_doc_count),
                    "start_time": start_tm,
                    "end_time": end_tm
                }
                                
                make_response(self, 200, "获取知识库统计成功", result)
                
        except Exception as e:
            logging.error(f"获取知识库统计失败: {e}")
            make_response(self, 500, "服务器内部错误")

class StatsModelHandler(BaseHandler):
    """模型统计处理器"""
    
    async def get(self):
        """获取模型统计信息"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            from config import MODEL_NUM
            result = {
                "model_count": MODEL_NUM or 0
            }
            make_response(self, 200, "获取模型统计成功", result)
                
        except Exception as e:
            logging.error(f"获取模型统计失败: {e}")
            make_response(self, 500, "服务器内部错误")

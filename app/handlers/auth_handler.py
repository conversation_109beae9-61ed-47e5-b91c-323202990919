from models import User, async_session, pwd_context
from datetime import datetime, timedelta
from sqlalchemy import select  # 添加这行
import jwt
import config
import tornado
import json
import logging

from utils import sso
from utils.response import make_response

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, config.SECRET_KEY, algorithm=config.ALGORITHM)

class RegistHandler(tornado.web.RequestHandler):
    async def post(self):
        data = json.loads(self.request.body)
        if not data or 'username' not in data or 'password' not in data:
            make_response(self, 400, "invalid request")
            return
        username = data['username']
        password = data['password']
        
        # 获取新增字段
        department = data.get('department', '待分配')  # 更新默认值
        group = data.get('group', 'employee')
        telphone = data.get('telphone', '')
        id_num = data.get('id_num', '')
        nickname = data.get('nickname', '')
        
        try:
            async with async_session() as session:
                result = await session.execute(
                    User.__table__.select().filter(User.username == username))
                if result.all():
                    make_response(self, 400, "用户名已存在")
                    return
            
            async with async_session() as session:
                hashed_password = pwd_context.hash(password)
                # 创建用户时包含新增字段
                new_user = User(
                    username=username, 
                    password_hash=hashed_password,
                    department=department,
                    group=group,
                    telphone=telphone,
                    id_num=id_num,
                    nickname=nickname
                )
                session.add(new_user)
                await session.commit()
        except Exception as e:
            logging.error(f"注册用户时出错: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "注册失败, 服务出现故障，请联系管理员")
            return
        
        make_response(self, 200, "success")

class LoginHandler(tornado.web.RequestHandler):
    async def post(self):
        data = json.loads(self.request.body)
        if not data or 'username' not in data or 'password' not in data:
            make_response(self, 400, "invalid request")
            return
        username = data['username']
        password = data['password']

        try:
            async with async_session() as session:
                # Use select() to get User instance
                result = await session.execute(
                    select(User).filter(User.username == username))
                user = result.scalar()
                if not user or not pwd_context.verify(password, user.password_hash):
                    make_response(self, 200, "用户名或密码错误", {})
                    return
                
                # Update last login time
                user.last_login = datetime.utcnow()
                await session.commit()

            access_token = create_access_token({"sub": str(user.user_id)})
            make_response(self, 200, "success", {"access_token": access_token, "token_type": "bearer"})
        except Exception as e:
            logging.error(f"登录时出错: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "登录失败, 服务出现故障，请联系管理员")
            return

class SsoLoginHandler(tornado.web.RequestHandler):
    async def post(self):
        data = json.loads(self.request.body)
        if not data or 'token' not in data:
            make_response(self, 400, "invalid sso token")
            return
        token = data['token']
        succeed, ssoUser = sso.get_user_info(token)
        if not succeed:
            make_response(self, 400, "invalid sso token")
            return

        ssoUserInfo = ssoUser.get('basicInfo')
        userName = ssoUserInfo.get("username")
        ssoUserInfo.get("nickName")
        ssoUserInfo.get("deptName")
        ssoUserInfo.get("deptCode")
        ssoUserInfo.get("deptId")
        try:
            async with async_session() as session:
                # session调用数据库，根据userName查询用户信息

                result = await session.execute(
                    select(User).filter(User.username == userName)
                )
                user = result.scalar()
                notExist = user is None

                if notExist:
                    user = User()
                # user.user_id = ssoUserInfo.get("id")
                user.username = ssoUserInfo.get("username")
                user.nickname = ssoUserInfo.get("nickName")
                user.password_hash = pwd_context.hash("123456")
                user.department = ssoUserInfo.get("deptName")
                # Update last login time
                user.last_login = datetime.utcnow()
                user.extro_info = ssoUserInfo

                if notExist:
                    session.add(user)
                await session.commit()
                access_token = create_access_token({
                    "sub": str(user.user_id),
                    "token": token
                })
                make_response(self, 200, "success", {
                    "access_token": access_token,
                    "username": userName,
                    "deptcode": ssoUserInfo.get("deptCode"),
                    "token_type": "bearer"
                })
        except Exception as e:
            logging.error(f"登录时出错: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "登录失败, 服务出现故障，请联系管理员")
            return

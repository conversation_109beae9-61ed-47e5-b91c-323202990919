from models import User, async_session
import json
import logging
import datetime
from utils.response import make_response
from handlers.base_handler import BaseHandler

class UserInfoHandler(BaseHandler):
    """用户信息更新处理"""
    
    async def get(self):
        """获取用户信息"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            async with async_session() as session:
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 404, "用户不存在")
                    return
                    
                user_info = {
                    'user_id': user.user_id,
                    'username': user.username,
                    'permission_level': user.permission_level,
                    'department': user.department,
                    'group': user.group,
                    'telephone': user.telphone,
                    'id_num': user.id_num,
                    'nickname': user.nickname
                }
                make_response(self, 200, "获取用户信息成功", user_info)
        except Exception as e:
            logging.error(f"获取用户信息失败: {e}")
            make_response(self, 500, "服务器内部错误")

    async def post(self):
        """更新用户信息"""
        # 获取当前登录用户ID
        login_user_id = self.get_user_id()
        if login_user_id is None:
            return
            
        try:
            data = json.loads(self.request.body)
            if not data:
                make_response(self, 400, "无效的请求数据")
                return
            
            # 获取要修改的目标用户ID，如果未提供则默认为当前登录用户
            target_user_id = data.get('user_id', login_user_id)
                
            async with async_session() as session:
                # 获取当前登录用户信息
                login_user = await session.get(User, login_user_id)
                if not login_user:
                    make_response(self, 404, "登录用户不存在")
                    return
                
                # 获取目标用户信息
                target_user = await session.get(User, target_user_id)
                if not target_user:
                    make_response(self, 404, "目标用户不存在")
                    return
                
                # 检查是否包含管理员专属字段
                admin_fields = {'department', 'permission_level', 'group'}
                update_fields = set(data.keys()) - {'user_id'}  # 排除user_id字段
                
                # 如果不是管理员但试图修改其他用户信息
                if login_user.permission_level != "admin" and str(login_user_id) != str(target_user_id):
                    make_response(self, 403, "权限不足，无法修改其他用户信息")
                    return
                
                # 如果包含管理员专属字段但不是管理员
                if admin_fields & update_fields and login_user.permission_level != "admin":
                    make_response(self, 403, "权限不足，无法更新管理员专属字段")
                    return
                
                # 更新允许的普通字段
                allowed_fields = {'telphone', 'id_num', 'nickname'}
                for field in allowed_fields & update_fields:
                    setattr(target_user, field, data.get(field, ''))
                
                # 如果是管理员，更新管理员专属字段
                if login_user.permission_level == "admin":
                    if 'department' in data:
                        target_user.department = data['department']
                    if 'permission_level' in data:
                        target_user.permission_level = data['permission_level']
                    if 'group' in data:
                        target_user.group = data['group']
                
                await session.commit()
                
                # 返回更新后的用户信息
                result = {
                    "user_id": target_user.user_id,
                    "username": target_user.username,
                    "permission_level": target_user.permission_level,
                    "department": target_user.department,
                    "group": target_user.group,
                    "telphone": target_user.telphone,
                    "id_num": target_user.id_num,
                    "nickname": target_user.nickname,
                    "update_time": datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")
                }
                make_response(self, 200, "更新用户信息成功", result)
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            logging.error(f"更新用户信息失败: {e}")
            make_response(self, 500, "服务器内部错误")
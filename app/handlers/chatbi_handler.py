import tornado.web
import logging
import json
import datetime
from sqlalchemy import select, func, and_, or_

from utils import chatbi
from .base_handler import BaseHandler
from models import Chatbi, async_session, User  # 关键：使用 Chatbi 模型
from models.data_control import DataControl  # 添加 DataControl 导入
from utils.response import make_response
import config  # 导入配置文件
import aiohttp

CHATBI_DATA_TYPE = "ChatBI应用"


# --- ChatbiHandler ---
class ChatbiHandler(BaseHandler):

    async def _call_external_app_edit_service(self, edit_payload: dict, url) -> tuple[bool, str | None]:
        """
        调用外部接口编辑/更新 app 详细信息.
        返回 (success_status, error_message)
        """
        # external_edit_service_url = "http://172.17.110.105:5670/api/v1/app/edit"
        # auth_token = "123456" # 警告：Token 不应硬编码

        external_edit_service_url = url
        auth_token = config.CHATBI_API_KEY  # 通常编辑和创建用同一个token

        if not external_edit_service_url or not auth_token:
            logging.error("外部应用编辑服务URL或认证Token未配置")
            return False, "外部编辑服务配置错误"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}"
        }

        # 确保 app_code 在 payload 中
        if 'app_code' not in edit_payload:
            logging.error("调用外部编辑接口的payload中缺少app_code")
            return False, "编辑数据准备错误：缺少app_code"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(external_edit_service_url, json=edit_payload, headers=headers,
                                        timeout=10) as response:
                    response_text = await response.text()
                    logging.debug(f"外部编辑接口响应状态: {response.status}, 内容: {response_text}")
                    if response.status == 200:
                        response_data = json.loads(response_text)
                        if response_data.get("success") is True:
                            # 示例返回 data: true，表示操作成功
                            return True, None
                        else:
                            err_msg = response_data.get("err_msg", "外部编辑接口返回成功但业务操作失败")
                            logging.error(f"外部编辑接口调用成功但业务失败: {err_msg}, 响应: {response_data}")
                            return False, err_msg
                    else:
                        logging.error(f"调用外部编辑接口失败，状态码: {response.status}, 响应: {response_text}")
                        return False, f"调用外部编辑接口失败 (状态码: {response.status})"
        except aiohttp.ClientError as e:
            logging.error(f"调用外部编辑接口网络错误: {str(e)}")
            return False, f"调用外部编辑接口网络错误: {str(e)}"
        except json.JSONDecodeError as e:
            logging.error(f"解析外部编辑接口响应JSON失败: {str(e)}. 原始响应: {response_text}")
            return False, "解析外部编辑接口响应失败"
        except Exception as e:
            logging.error(f"调用外部编辑接口发生未知错误: {str(e)}, {logging.traceback.format_exc()}")
            return False, f"调用外部编辑接口时发生未知错误: {str(e)}"

    async def _call_external_app_create_service(self, payload: dict, url) -> tuple[str | None, str | None]:
        """
        调用外部接口创建 app 并获取 app_code.
        返回 (app_code, error_message)
        """

        external_service_url = url
        auth_token = config.CHATBI_API_KEY

        if not external_service_url or not auth_token:
            logging.error("外部应用创建服务URL或认证Token未配置")
            return None, "外部服务配置错误"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(external_service_url, json=payload, headers=headers,
                                        timeout=10) as response:  # 10秒超时
                    response_text = await response.text()  # 读取文本以供调试
                    logging.debug(f"外部接口响应状态: {response.status}, 内容: {response_text}")
                    if response.status == 200 or response.status == 201:  # 通常创建成功是 201，但示例返回的是200
                        response_data = json.loads(response_text)  # 重新解析，因为之前是text
                        if response_data.get("success") is True and response_data.get("data") and response_data[
                            "data"].get("app_code"):
                            return response_data["data"]["app_code"], None
                        else:
                            err_msg = response_data.get("err_msg", "外部接口返回成功但数据格式无效或操作失败")
                            logging.error(f"外部接口调用成功但业务失败: {err_msg}, 响应: {response_data}")
                            return None, err_msg
                    else:
                        logging.error(f"调用外部接口失败，状态码: {response.status}, 响应: {response_text}")
                        return None, f"调用外部接口失败 (状态码: {response.status})"
        except aiohttp.ClientError as e:  # 网络层面错误，如连接超时、DNS解析失败等
            logging.error(f"调用外部接口网络错误: {str(e)}")
            return None, f"调用外部接口网络错误: {str(e)}"
        except json.JSONDecodeError as e:
            logging.error(f"解析外部接口响应JSON失败: {str(e)}. 原始响应: {response_text}")
            return None, "解析外部接口响应失败"
        except Exception as e:
            logging.error(f"调用外部接口发生未知错误: {str(e)}, {logging.traceback.format_exc()}")
            return None, f"调用外部接口时发生未知错误: {str(e)}"

    def _get_chatbi_data_from_request(self, data: dict) -> dict:
        """从请求数据中提取Chatbi模型所需字段"""
        chatbi_payload = {}
        # Chatbi 模型中定义的字段名
        possible_fields = [
            'app_code', 'app_describe', 'app_name', 'app_icon_path', 'extro_info',
            'team_mode', 'language', 'team_context', 'param_need', 'recommend_questions'
        ]
        for field in possible_fields:
            if field in data:
                chatbi_payload[field] = data[field]
        return chatbi_payload

    def _serialize_chatbi(self, chatbi: Chatbi) -> dict:
        """将Chatbi对象序列化为字典，包含所有字段"""
        if not chatbi:
            return None
        return {
            'chatbi_id': chatbi.chatbi_id,  # Chatbi 模型的主键
            'app_code': chatbi.app_code,
            'app_describe': chatbi.app_describe,
            'app_name': chatbi.app_name,
            'user_id': chatbi.user_id,
            'app_icon_path': chatbi.app_icon_path,
            'extro_info': chatbi.extro_info,
            'team_mode': chatbi.team_mode,
            'language': chatbi.language,
            'team_context': chatbi.team_context,
            'param_need': chatbi.param_need,
            'recommend_questions': chatbi.recommend_questions,
            'create_time': chatbi.create_time.strftime('%Y-%m-%d %H:%M:%S') if chatbi.create_time else None,
            'update_time': chatbi.update_time.strftime('%Y-%m-%d %H:%M:%S') if chatbi.update_time else None,
        }

    async def post(self):
        # 创建 Chatbi 应用
        user_id = self.get_user_id()
        if not user_id:
            return

        async with async_session() as session:
            # 权限检查：只有 admin 用户可以创建
            result = await session.execute(
                select(User).filter(User.user_id == user_id)
            )
            user = result.scalars().first()
            if not user or user.permission_level != "admin":
                make_response(self, 403, '权限不足，仅管理员可创建Chatbi应用')
                return

        try:
            data = json.loads(self.request.body)

            # 添加 dt_dept_code 参数验证
            dt_dept_code = data.get('dt_dept_code', None)
            if not dt_dept_code:
                make_response(self, 400, '缺少dt_dept_code参数')
                return

            # 必需字段校验 (app_name 仍然是必须的，因为它用于调用外部接口和本地存储)
            if not data.get('app_name'):
                make_response(self, 400, '缺少app_name参数')
                return
            # 其他必需字段 (用于调用外部接口)
            # 根据curl示例，外部接口需要 language, app_name, app_describe, team_mode
            required_for_external = ['language', 'app_name', 'app_describe', 'team_mode']
            external_payload = {}
            for field in required_for_external:
                if field not in data:
                    make_response(self, 400, f'缺少用于生成app_code的参数: {field}')
                    return
                external_payload[field] = data[field]

            # 调用外部接口获取 app_code
            getcode_url = f"{config.CHATBI_CREATE_URL}"
            app_code_from_external, error_msg = await self._call_external_app_create_service(external_payload,
                                                                                             getcode_url)

            if error_msg or not app_code_from_external:
                make_response(self, 500, f'获取app_code失败: {error_msg or "未知错误"}')
                return

            # 检查获取到的 app_code 在本地是否已存在 (理论上外部服务应保证唯一性，但多一层防护)
            async with async_session() as session:
                existing_chatbi_check = await session.execute(
                    select(Chatbi).filter(Chatbi.app_code == app_code_from_external)
                )
                if existing_chatbi_check.scalars().first():
                    # 这种情况比较复杂，外部生成了app_code，但本地已存在。
                    # 可能需要更复杂的处理逻辑，或者信任外部接口的唯一性。
                    # 为简单起见，这里假设外部接口生成的app_code是全新的。
                    # 如果严格要求本地也唯一，则可以报错。
                    logging.warning(f"外部接口生成的app_code '{app_code_from_external}' 在本地已存在。继续创建...")
                    # make_response(self, 409, f"获取到的app_code '{app_code_from_external}' 在系统中已存在，请联系管理员")
                    # return

            # 从原始请求 data 中提取 Chatbi 模型需要的其他字段
            chatbi_payload_for_db = self._get_chatbi_data_from_request(data)
            chatbi_payload_for_db['app_code'] = app_code_from_external  # 使用获取到的app_code
            chatbi_payload_for_db['user_id'] = user_id

            # 确保 app_name 等字段与 external_payload 一致或按需覆盖
            # 当前 _get_chatbi_data_from_request 会从 data 中取，这通常是期望的
            # 如果外部接口返回的 app_name 等是权威的，则需要用外部接口的响应更新 chatbi_payload_for_db

            new_chatbi = Chatbi(**chatbi_payload_for_db)

            async with async_session() as session:
                # 创建 Chatbi
                session.add(new_chatbi)
                await session.flush()  # 获取chatbi_id但不提交

                # 创建 DataControl
                new_data_control = DataControl(
                    data_type=CHATBI_DATA_TYPE,
                    data_id=new_chatbi.chatbi_id,
                    dt_type='所在部门',
                    dt_dept_code=dt_dept_code,
                    owner_id=user_id,
                )
                session.add(new_data_control)
                await session.commit()  # 一次性提交所有更改
                await session.refresh(new_chatbi)

            make_response(self, 201, 'Chatbi应用创建成功本地step2-1',
                          self._serialize_chatbi(new_chatbi))  # 返回完整的Chatbi信息

            #  准备调用外部 "edit" 接口的 payload
            # 这个 payload 需要包含 app_code 和您 curl 示例中显示的所有其他字段
            # 这些字段应该来自 new_chatbi 对象 (即已存入数据库并可能经过处理的值)
            # 或者直接来自 original_request_data (如果这些数据不需要经过本地数据库处理)
            # 为确保一致性，最好从 new_chatbi 对象中构建，因为它代表了最终存储的状态

            external_edit_payload = {
                "app_code": new_chatbi.app_code,
                "app_describe": new_chatbi.app_describe,
                "team_mode": new_chatbi.team_mode,
                "app_name": new_chatbi.app_name,
                "language": new_chatbi.language,
                "team_context": new_chatbi.team_context,  # 这些是 JSON 字段
                "param_need": new_chatbi.param_need,  # 需要确保它们是正确的JSON结构
                "recommend_questions": new_chatbi.recommend_questions
            }
            # 如果原始请求中没有这些JSON字段，但模型中有默认值(如None)，
            # 确保 external_edit_payload 中的这些字段符合外部接口的期望 (e.g., 是 null 还是空列表/对象)
            # 例如，如果 new_chatbi.team_context 是 None，而外部接口期望一个对象，可能需要处理
            # for key in ["team_context", "param_need", "recommend_questions"]:
            #     if external_edit_payload[key] is None:
            #         if key == "team_context": external_edit_payload[key] = {} # 或 null
            #         else: external_edit_payload[key] = [] # 或 null

            # 7. 调用外部 "edit" 接口
            my_url = f"{config.CHATBI_EDIT_URL}"
            edit_success, edit_error_msg = await self._call_external_app_edit_service(external_edit_payload, my_url)

            if not edit_success:
                # 思考这里的处理逻辑：本地已创建成功，但同步到外部 "edit" 接口失败。
                # 是回滚本地创建，还是记录错误并继续？
                # 当前实现：记录警告，但仍然认为本地创建是成功的。
                logging.warning(
                    f"本地Chatbi应用 (app_code: {new_chatbi.app_code}) 创建成功，但同步到外部编辑服务失败: {edit_error_msg}")
                # 您可以选择在这里返回不同的成功消息或状态，或者采取补偿措施
                # make_response(self, 202, f'Chatbi应用创建成功但同步外部服务部分失败: {edit_error_msg}', self._serialize_chatbi(new_chatbi)) # HTTP 202 Accepted
                # return

            # 8. 返回最终响应
            make_response(self, 201, 'Chatbi应用创建成功本地step2-2', external_edit_payload)

        except json.JSONDecodeError:
            make_response(self, 400, '无效的JSON请求体')
        except Exception as e:
            logging.error(f"Chatbi应用创建失败: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'创建失败: {str(e)}')

    async def put(self):
        # 修改 Chatbi 应用 - 入口参数为 app_code
        user_id = self.get_user_id()
        if not user_id:
            return

        app_code_from_request = self.get_argument('app_code', None)
        if not app_code_from_request:
            make_response(self, 400, "缺少app_code参数")
            return

        async with async_session() as session:
            # 获取当前用户
            user_result = await session.execute(
                select(User).filter(User.user_id == user_id)
            )
            current_user = user_result.scalars().first()
            if not current_user:
                make_response(self, 404, '操作用户不存在')
                return

            # 根据 app_code 查询 Chatbi
            stmt = select(Chatbi).filter(Chatbi.app_code == app_code_from_request)
            result = await session.execute(stmt)
            chatbi_to_update = result.scalars().first()

            if not chatbi_to_update:
                make_response(self, 404, f"app_code为 '{app_code_from_request}' 的Chatbi应用不存在")
                return

            # 权限细化：管理员可以修改任何 Chatbi，普通用户只能修改自己创建的
            is_admin = (current_user.permission_level == "admin")
            if not is_admin and chatbi_to_update.user_id != user_id:
                make_response(self, 403, '无权限修改他人创建的Chatbi应用')
                return

            try:
                data = json.loads(self.request.body)
                if not data:
                    make_response(self, 400, '请求体不能为空')
                    return
            except json.JSONDecodeError:
                make_response(self, 400, '无效的JSON请求体')
                return

            updated_fields_count = 0
            update_payload = self._get_chatbi_data_from_request(data)

            # 不允许通过此接口修改 app_code 本身
            if 'app_code' in update_payload and update_payload['app_code'] != chatbi_to_update.app_code:
                make_response(self, 400, '不允许通过此接口修改app_code')
                return

            for field_name, value in update_payload.items():
                if field_name == 'app_code':  # 跳过 app_code 的更新
                    continue
                if hasattr(chatbi_to_update, field_name) and getattr(chatbi_to_update, field_name) != value:
                    setattr(chatbi_to_update, field_name, value)
                    updated_fields_count += 1

            if updated_fields_count == 0:
                # 如果没有字段值发生实际变化
                make_response(self, 200, 'Chatbi应用信息未发生变化', self._serialize_chatbi(chatbi_to_update))
                return

            # chatbi_to_update.update_time 会由 SQLAlchemy 的 onupdate 自动处理
            try:
                await session.commit()
                await session.refresh(chatbi_to_update)  # 确保获取最新的 update_time

                make_response(self, 200, 'Chatbi应用修改成功step2-1', self._serialize_chatbi(chatbi_to_update))

                chatbi_payload_for_db = self._get_chatbi_data_from_request(data)

                new_chatbi = Chatbi(**chatbi_payload_for_db)

                external_edit_payload = {
                    "app_code":  chatbi_to_update.app_code,
                    "app_describe": chatbi_to_update.app_describe,
                    "team_mode": chatbi_to_update.team_mode,
                    "app_name": chatbi_to_update.app_name,
                    "language": chatbi_to_update.language,
                    "team_context": chatbi_to_update.team_context,  # 这些是 JSON 字段
                    "param_need": chatbi_to_update.param_need,  # 需要确保它们是正确的JSON结构
                    "recommend_questions": chatbi_to_update.recommend_questions
                }
                # 如果原始请求中没有这些JSON字段，但模型中有默认值(如None)，
                # 确保 external_edit_payload 中的这些字段符合外部接口的期望 (e.g., 是 null 还是空列表/对象)
                # 例如，如果 new_chatbi.team_context 是 None，而外部接口期望一个对象，可能需要处理
                # for key in ["team_context", "param_need", "recommend_questions"]:
                #     if external_edit_payload[key] is None:
                #         if key == "team_context": external_edit_payload[key] = {} # 或 null
                #         else: external_edit_payload[key] = [] # 或 null

                # 7. 调用外部 "edit" 接口
                my_url = f"{config.CHATBI_EDIT_URL}"
                edit_success, edit_error_msg = await self._call_external_app_edit_service(external_edit_payload, my_url)

                if not edit_success:
                    # 思考这里的处理逻辑：本地已创建成功，但同步到外部 "edit" 接口失败。
                    # 是回滚本地创建，还是记录错误并继续？
                    # 当前实现：记录警告，但仍然认为本地创建是成功的。
                    logging.warning(
                        f"本地Chatbi应用 (app_code: {new_chatbi.app_code}) 创建成功，但同步到外部编辑服务失败: {edit_error_msg}")
                    # 您可以选择在这里返回不同的成功消息或状态，或者采取补偿措施
                    # make_response(self, 202, f'Chatbi应用创建成功但同步外部服务部分失败: {edit_error_msg}', self._serialize_chatbi(new_chatbi)) # HTTP 202 Accepted
                    # return

                # 8. 返回最终响应
                make_response(self, 201, 'Chatbi应用创建成功本地step2-2', external_edit_payload)



            except Exception as e:
                await session.rollback()
                logging.error(
                    f"Chatbi应用修改失败 (app_code: {app_code_from_request}): {str(e)}, {logging.traceback.format_exc()}")
                make_response(self, 500, f'修改失败: {str(e)}')

    async def delete(self):
        # 删除 Chatbi 应用 - 入口参数为 app_code
        user_id = self.get_user_id()
        if user_id is None:
            return

        app_code_from_request = self.get_argument('app_code', None)
        if not app_code_from_request:
            make_response(self, 400, "缺少app_code参数")
            return

        async with async_session() as session:
            user_result = await session.execute(
                select(User).filter(User.user_id == user_id)
            )
            current_user = user_result.scalars().first()
            if not current_user:
                make_response(self, 404, '操作用户不存在')
                return

            stmt = select(Chatbi).filter(Chatbi.app_code == app_code_from_request)
            result = await session.execute(stmt)
            chatbi_to_delete = result.scalars().first()

            if not chatbi_to_delete:
                make_response(self, 404, f"app_code为 '{app_code_from_request}' 的Chatbi应用不存在")
                return

            is_admin = (current_user.permission_level == "admin")
            if not is_admin and chatbi_to_delete.user_id != user_id:
                make_response(self, 403, '无权限删除他人创建的Chatbi应用')
                return

            try:
                await session.delete(chatbi_to_delete)
                await session.commit()
                make_response(self, 200, 'Chatbi应用删除成功')
            except Exception as e:
                await session.rollback()
                logging.error(
                    f"Chatbi应用删除失败 (app_code: {app_code_from_request}): {str(e)}, {logging.traceback.format_exc()}")
                make_response(self, 500, f'删除失败: {str(e)}')

    async def get(self):
        # 查看单个 Chatbi 应用 - 入口参数为 app_code
        user_id = self.get_user_id()
        if not user_id:
            return

        app_code_from_request = self.get_argument('app_code', None)
        if not app_code_from_request:
            make_response(self, 400, "缺少app_code参数")
            return

        async with async_session() as session:
            user_result = await session.execute(
                select(User).filter(User.user_id == user_id)
            )
            current_user = user_result.scalars().first()
            if not current_user:
                make_response(self, 404, '操作用户不存在')
                return

            stmt = select(Chatbi).filter(Chatbi.app_code == app_code_from_request)
            result = await session.execute(stmt)
            chatbi_instance = result.scalars().first()  # Renamed variable

            if not chatbi_instance:
                make_response(self, 404, f"app_code为 '{app_code_from_request}' 的Chatbi应用不存在")
                return

            is_admin = (current_user.permission_level == "admin")
            if not is_admin and chatbi_instance.user_id != user_id:
                make_response(self, 403, '无权限查看他人创建的Chatbi应用')
                return

            make_response(self, 200, '查询成功', self._serialize_chatbi(chatbi_instance))


# --- ChatbiListHandler ---
class ChatbiListHandler(BaseHandler):

    def _serialize_chatbi(self,
                          chatbi: Chatbi) -> dict:  # Copied for consistency, ensure proper code sharing in practice
        """将Chatbi对象序列化为字典，包含所有字段"""
        if not chatbi:
            return None
        return {
            'chatbi_id': chatbi.chatbi_id,
            'app_code': chatbi.app_code,
            'app_describe': chatbi.app_describe,
            'app_name': chatbi.app_name,
            'user_id': chatbi.user_id,
            'app_icon_path': chatbi.app_icon_path,
            'extro_info': chatbi.extro_info,
            'team_mode': chatbi.team_mode,
            'language': chatbi.language,
            'team_context': chatbi.team_context,
            'param_need': chatbi.param_need,
            'recommend_questions': chatbi.recommend_questions,
            'create_time': chatbi.create_time.strftime('%Y-%m-%d %H:%M:%S') if chatbi.create_time else None,
            'update_time': chatbi.update_time.strftime('%Y-%m-%d %H:%M:%S') if chatbi.update_time else None,
        }

    async def get(self):
        user_id = self.get_user_id()
        if not user_id:
            return

        try:
            # 从URL查询参数获取数据
            page_no = max(int(self.get_argument('pageNo', '1')), 1)
            page_size = max(int(self.get_argument('pageSize', '10')), 1)
            dt_dept_code = self.get_argument('dt_dept_code', None)

            if not dt_dept_code:
                make_response(self, 400, '缺少dt_dept_code参数')
                return

            if page_size > 100:
                page_size = 100
            offset = (page_no - 1) * page_size

            async with async_session() as session:
                # 1. 查询所有chatbi及其权限控制信息
                query = select(Chatbi, DataControl).outerjoin(
                    DataControl,
                    and_(
                        DataControl.data_id == Chatbi.chatbi_id,
                        DataControl.data_type == CHATBI_DATA_TYPE
                    )
                )

                # 2. 添加权限过滤条件
                permission_conditions = or_(
                    # 没有权限控制记录的chatbi（默认可见）
                    DataControl.dt_id.is_(None),

                    DataControl.dt_type == '公开',  # 公开的chatbi

                    and_(
                        DataControl.dt_type == '私有',  # 私有但是owner是当前用户的chatbi
                        DataControl.owner_id == user_id
                    ),

                    and_(
                        DataControl.dt_type == '所在部门',  # 所在部门：dt_dept_code与用户部门完全一致
                        DataControl.dt_dept_code == dt_dept_code,
                    ),

                    and_(
                        DataControl.dt_type == '指定部门',  # 指定部门：用户部门在dt_dept_code_list中
                        or_(
                            DataControl.dt_dept_code_list.like(f'%,{dt_dept_code},%'),  # 包含用户部门
                        ),
                    )
                )

                query = query.where(
                    and_(
                        DataControl.data_type == CHATBI_DATA_TYPE,
                        permission_conditions
                    )
                )

                # 先查询总数
                count_query = select(func.count()).select_from(
                    query.subquery()
                )
                count_result = await session.execute(count_query)
                total = count_result.scalar()

                # 查询分页数据
                result = await session.execute(
                    query.order_by(Chatbi.update_time.desc()).offset(offset).limit(page_size)
                )
                chatbi_data = result.all()

                results = []
                for chatbi, data_control in chatbi_data:
                    chatbi_info = self._serialize_chatbi(chatbi)
                    chatbi_info['editable'] = chatbi.user_id == user_id
                    results.append(chatbi_info)

                # 返回包含total和list的数据结构
                response_data = {
                    'total': total,
                    'list': results,
                    'pageNo': page_no,
                    'pageSize': page_size
                }

                make_response(self, 200, '查询成功', response_data)

        except ValueError:
            make_response(self, 400, '分页参数pageNo或pageSize无效')
        except Exception as e:
            logging.error(f"Chatbi应用列表查询失败: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'查询失败: {str(e)}')


class ChatbiGetConvHandler(BaseHandler):

    async def get(self):
        user_id = self.get_user_id()
        if not user_id:
            make_response(self, 400, "当前登录用户已过期，请退出系统重新登录")
            return

        try:
            succeed, result = chatbi.get_conv_id();
            if not succeed:
                make_response(self, 400, "获取ChatBI会话失败")
                return

            make_response(self, 200, "success", {
                "convID": result.get('conv_uid')
            })
        except Exception as e:
            logging.error(f"获取ChatBI会话失败: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "获取ChatBI会话失败, 服务出现故障，请联系管理员")
            return
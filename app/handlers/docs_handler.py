import tornado.web
import json
import uuid
import logging
import config
# from models import Document
from utils.response import make_response
from utils.minio import generate_presigned_url, generate_presigned_upload_url
from handlers.base_handler import BaseHandler


# class DocsUploadHandler(tornado.web.RequestHandler):
#     async def post(self):
#         try:
#             data = json.loads(self.request.body)
#             if not data or 'docments' not in data:
#                 make_response(self, 400, "参数错误")
#                 return
#             docment_ids = []
#             for doc in data['docments']:
#                 if 'path' not in doc or 'name' not in doc or 'permission_level' not in doc or 'version' not in doc:
#                     make_response(self, 400, "参数错误")
#                     return
#                 doc_id = f"i{len(docment_ids) + 1}"
#                 docment_ids.append(doc_id)
#                 new_doc = Document(
#                     doc_id=doc_id,
#                     path=doc['path'],
#                     name=doc['name'],
#                     permission_level=str(doc['permission_level']),
#                     version=doc['version']
#                 )
#                 session.add(new_doc)
#             session.commit()
#             make_response(self, 200, "success", {"docment_ids": docment_ids})
#         except json.JSONDecodeError:
#             make_response(self, 400, "无效的 JSON 数据")

# class DocsListHandler(tornado.web.RequestHandler):
#     async def get(self):
#         page = int(self.get_argument('page', 1))
#         size = int(self.get_argument('size', 20))
#         paginated_docs = Document.query.paginate(page=page, per_page=size, error_out=False)
#         total = paginated_docs.total
#         response_docs = []
#         for doc in paginated_docs.items:
#             response_docs.append({
#                 "doc_id": doc.doc_id,
#                 "name": doc.name,
#                 "version": doc.version,
#                 "download_url": "http://****",
#                 "upload_time": doc.upload_time.strftime('%Y-%m-%dT%H:%M:%SZ')
#             })
#         make_response(self, 200, "success", {"documents": response_docs, "total": total})

class PresignedUploadURLHandler(BaseHandler):
    async def get(self):
        userid = self.get_user_id()
        if not userid: return
        file_name = self.get_argument('file_name', None)
        if not file_name:
            make_response(self, 400, "invalid request")
            return

        object_path = f"tmp_docs/{userid}/{uuid.uuid4()}-{file_name}"
        try:
            upload_url = generate_presigned_upload_url(
                config.MINIO_BUCKET_NAME,
                object_path
            )
            make_response(self, 200, 'success', {'upload_url': upload_url, "object_path": object_path})
        except Exception as e:
            logging.error(f'生成预签名URL失败: {str(e)}, {logging.traceback.format_exc()}')
            make_response(self, 500, '服务器内部错误')


class FilePreviewHandler(BaseHandler):
    async def get(self):
        user_id = self.get_user_id()
        if not user_id: return
        
        object_path = self.get_argument('object_path', None)
        if not object_path:
            make_response(self, 400, "invalid request")
            return
        try:
            preview_url = generate_presigned_url(
                config.MINIO_BUCKET_NAME,
                object_path,
                preview=True
            )
            make_response(self, 200, 'success', {'preview_url': preview_url})
        except Exception as e:
            logging.error(f'生成预签名URL失败: {str(e)}, {logging.traceback.format_exc()}')
            make_response(self, 500, '服务器内部错误')

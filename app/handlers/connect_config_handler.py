import json
import logging
from utils.response import make_response
from handlers.base_handler import BaseHandler
from services.connect_config_service import ConnectConfigService
from models import async_session
from models.user import User

logger = logging.getLogger(__name__)

class ConnectConfigHandler(BaseHandler):
    """数据源配置统一处理器"""
    
    async def post(self):
        """创建数据源"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            # 获取用户信息
            async with async_session() as session:
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 404, "用户不存在")
                    return
            
            data = json.loads(self.request.body)
            if not data:
                make_response(self, 400, "无效的请求数据")
                return
                
            # 验证必要字段 - 适配新数据格式
            if 'type' not in data or 'params' not in data:
                make_response(self, 400, "缺少必要字段: type 或 params",data)
                return
            
            # 创建数据源
            datasource = await ConnectConfigService.create_datasource(
                user_id, data
            )
            
            make_response(self, 200, "数据源创建成功", {
                'id': datasource.id,
                'external_id': datasource.external_id
            })
            
        except Exception as e:
            logger.error(f"创建数据源失败: {str(e)}")
            make_response(self, 500, f"创建数据源失败: {str(e)}")
    
    async def put(self):
        """更新数据源"""
        try:
            data = json.loads(self.request.body)
            if not data or 'id' not in data:
                make_response(self, 400, "无效的请求数据或缺少数据源ID")
                return
            
            # 验证必要字段 - 适配新数据格式
            if 'type' not in data or 'params' not in data:
                make_response(self, 400, "缺少必要字段: type 或 params")
                return
            
            datasource_id = data.pop('id')
            
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            # 更新数据源
            datasource = await ConnectConfigService.update_datasource(
                datasource_id, user_id, data
            )
            
            make_response(self, 200, "数据源更新成功", {
                'id': datasource.id,
                'external_id': datasource.external_id
            })
            
        except Exception as e:
            logger.error(f"更新数据源失败: {str(e)}")
            make_response(self, 500, f"更新数据源失败: {str(e)}")
    
    async def delete(self):
        """删除数据源"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            data = json.loads(self.request.body)
            if not data or 'id' not in data:
                make_response(self, 400, "无效的请求数据或缺少数据源ID")
                return
            
            datasource_id = data['id']
            
            # 删除数据源
            await ConnectConfigService.delete_datasource(datasource_id, user_id)
            
            make_response(self, 200, "数据源删除成功")
            
        except Exception as e:
            logger.error(f"删除数据源失败: {str(e)}")
            make_response(self, 500, f"删除数据源失败: {str(e)}")
    
    async def get(self):
        """获取数据源列表或详情"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            # 获取查询参数
            datasource_id = self.get_argument('id', None)
            
            if datasource_id:
                # 获取数据源详情
                detail = await ConnectConfigService.get_datasource_detail(
                    int(datasource_id), user_id
                )
                
                if not detail:
                    make_response(self, 404, "数据源不存在")
                    return
                
                make_response(self, 200, "获取数据源详情成功", detail)
            else:
                # 获取数据源列表
                page = int(self.get_argument('page', 1))
                page_size = int(self.get_argument('page_size', 20))
                
                result = await ConnectConfigService.get_datasource_list(
                    user_id, page, page_size
                )
                
                make_response(self, 200, "获取数据源列表成功", result)
                
        except Exception as e:
            logger.error(f"获取数据源失败: {str(e)}")
            make_response(self, 500, f"获取数据源失败: {str(e)}")

class ConnectConfigTestHandler(BaseHandler):
    """数据源连接测试处理器"""
    
    async def post(self):
        """测试数据库连接"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            data = json.loads(self.request.body)
            if not data:
                make_response(self, 400, "无效的请求数据")
                return
            
            # 测试连接 - 修改为外部测试方法
            result = await ConnectConfigService.test_external_connection(data)
            
            if result['success']:
                make_response(self, 200, result['message'])
            else:
                make_response(self, 400, result['message'])
                
        except Exception as e:
            logger.error(f"测试数据库连接失败: {str(e)}")
            make_response(self, 500, f"测试连接失败: {str(e)}")

class ConnectConfigTypesHandler(BaseHandler):
    """获取支持的数据库类型处理器"""
    
    async def get(self):
        """获取支持的数据库类型"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            types = ConnectConfigService.get_supported_db_types()
            make_response(self, 200, "获取数据库类型成功", types)
            
        except Exception as e:
            logger.error(f"获取数据库类型失败: {str(e)}")
            make_response(self, 500, f"获取数据库类型失败: {str(e)}")
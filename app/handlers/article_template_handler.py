import tornado.web
import json
import aiohttp
import logging
import time
from utils.response import make_response
from models import ArticleTemplate, async_session
from handlers.base_handler import BaseHandler
from sqlalchemy import select, func, and_, or_
import config
import config_prompt
from utils.common import generate_checksum
from handlers.generate_handler import retrive_by_uploaded_file  # 添加导入语句

async def content_by_uploaded_file(file_object):
    """
    根据上传的文件对象获取文件内容

    参数:
        file_object: 文件对象

    返回:
        文件内容
    """
    # 将单个文件对象转换为列表，以便复用 retrive_by_uploaded_file 函数
    file_object_list = [file_object]
    return await retrive_by_uploaded_file(file_object_list)


class ManagerArticleTemplate(BaseHandler):
    """文章模板处理器"""
    
    async def post(self):
        """保存手工编辑的模板内容"""
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'template_content' not in data or 'template_type' not in data or 'template_name' not in data:
                make_response(self, 400, "参数错误：缺少模板内容、类型或名称")
                return
            
            template_content = data['template_content']
            template_type = data['template_type']
            template_id = data.get('template_id', None)  # 获取模板ID，如果存在则为更新操作
            template_name = data['template_name']  # 模板名称必填
            permission_level = data.get('permission_level', 'private')  # 获取权限级别
            
            # 检查同一用户下模板名称是否已存在
            async with async_session() as session:
                # 如果是更新操作，需要排除当前模板ID
                if template_id:
                    query = select(ArticleTemplate).where(
                        and_(
                            ArticleTemplate.user_id == user_id,
                            ArticleTemplate.template_name == template_name,
                            ArticleTemplate.template_id != template_id,
                            ArticleTemplate.is_deleted == 0
                        )
                    )
                else:
                    # 新建操作，检查是否有同名模板
                    query = select(ArticleTemplate).where(
                        and_(
                            ArticleTemplate.user_id == user_id,
                            ArticleTemplate.template_name == template_name,
                            ArticleTemplate.is_deleted == 0
                        )
                    )
                
                result = await session.execute(query)
                existing_template = result.scalar_one_or_none()
                
                if existing_template:
                    make_response(self, 400, "模板名称已存在，请使用其他名称")
                    return
                
            # 保存模板
            async with async_session() as session:
                if template_id:
                    # 更新已存在的模板
                    template = await session.get(ArticleTemplate, template_id)
                    if not template:
                        make_response(self, 404, "模板不存在")
                        return
                    
                    # 验证用户权限
                    if template.user_id != user_id:
                        make_response(self, 403, "无权修改该模板")
                        return
                    
                    # 更新模板内容
                    template.template_content = template_content
                    template.template_type = template_type
                    template.update_time = func.current_timestamp()
                    
                    # 更新新增字段
                    if template_name:
                        template.template_name = template_name
                    if permission_level:
                        template.permission_level = permission_level
                else:
                    # 创建新模板
                    template = ArticleTemplate(
                        template_content=template_content,
                        template_type=template_type,
                        template_name=template_name,
                        permission_level=permission_level,
                        is_manual=1,  # 新建时默认为手工编辑
                        user_id=user_id,
                        is_deleted=0
                    )
                    session.add(template)
                
                await session.commit()
                await session.refresh(template)
                
                result = {
                    "template_id": template.template_id,
                    "template_name": template.template_name,
                    "template_type": template.template_type,
                    "permission_level": template.permission_level,
                    "create_time": template.create_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "is_manual": template.is_manual
                }
                make_response(self, 200, "模板保存成功", result)
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            logging.error(f"保存模板失败: {str(e)}")
            make_response(self, 500, f"服务器错误: {str(e)}")
    
    async def get(self):
        """获取所有模板列表"""
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            # 获取筛选参数
            template_type = self.get_argument('template_type', None)
            
            async with async_session() as session:
                # 构建查询条件
                conditions = [ArticleTemplate.is_deleted == 0]
                
                # 添加权限过滤条件：用户只能看到自己的模板或系统级别的模板
                conditions.append(
                    or_(
                        ArticleTemplate.user_id == user_id,
                        ArticleTemplate.permission_level == 'system'
                    )
                )
                
                if template_type:
                    conditions.append(ArticleTemplate.template_type == template_type)
                
                # 查询所有数据
                query = select(ArticleTemplate).where(and_(*conditions)).order_by(
                    ArticleTemplate.create_time.desc()
                )
                
                result = await session.execute(query)
                templates = result.scalars().all()
                
                # 构建返回结果
                template_list = []
                for template in templates:
                    template_dict = {
                        "template_id": template.template_id,
                        "template_name": template.template_name,
                        "template_type": template.template_type,
                        "template_content": template.template_content,
                        "permission_level": template.permission_level,
                        "is_manual": template.is_manual,
                        "user_id": template.user_id,
                        "create_time": template.create_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "update_time": template.update_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    }
                    template_list.append(template_dict)
                
                result = {
                    "total": len(template_list),
                    "templates": template_list
                }
                make_response(self, 200, "获取模板列表成功", result)
                
        except Exception as e:
            logging.error(f"获取模板列表失败: {str(e)}")
            make_response(self, 500, f"服务器错误: {str(e)}")

class ExtractArticleTemplate(BaseHandler):
    """从文章中提取模板"""
    
    async def post(self):
        """根据给定的文章，抽取文章模板"""
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'file_object' not in data or 'template_type' not in data or 'template_name' not in data:
                make_response(self, 400, "参数错误：缺少文件对象、模板类型或模板名称")
                return
            file_object = data['file_object']
            template_type = data['template_type']
            template_name = data['template_name']
            permission_level = data.get('permission_level', 'private')  # 默认权限级别
            
            # 从minio下载并解析文件内容
            article_content = await content_by_uploaded_file(file_object)
            
            # 检查同一用户下模板名称是否已存在
            async with async_session() as session:
                # 构建查询条件：同一用户下相同名称且未删除的模板
                query = select(ArticleTemplate).where(
                    and_(
                        ArticleTemplate.user_id == user_id,
                        ArticleTemplate.template_name == template_name,
                        ArticleTemplate.is_deleted == 0
                    )
                )
                result = await session.execute(query)
                existing_template = result.scalar_one_or_none()
                if existing_template:
                    make_response(self, 400, "模板名称已存在，请使用其他名称")
                    return
            
            # 构建提示词
            prompt = f"""请分析以下文章，结合模板类型，提取出其结构模板，关注文章的关键词、段落结构、格式和文章结构信息，但移除具体内容，用通用占位符替代。
            
            模板类型：{template_type}

            文章内容：
            {article_content}
            
            请返回markdown格式描述的文章模板，以便用户后续编辑，并用于后续的文章生成。
            返回格式示例： #title\\n\\n## 1. 引言\\n introduction \\n\\n## 2. 正文\\n content ...
            请直接返回markdown格式的文章模板，不要添加其他额外信息，谢谢。
            """
            prompt_str = prompt.format(template_type=template_type, article_content=article_content)
            try:
                # 调用独立的函数处理大模型请求
                logging.info(f"Extracting template for {template_name}...")
                start_ts = int(time.time()*1000)
                template_result = await self.call_llm_for_template(prompt_str)
                end_ts = int(time.time()*1000)
                logging.info(f"Extract template took {end_ts - start_ts}ms")
                template_md = template_result["template_md"]
                input_tokens = template_result["input_tokens"]
                output_tokens = template_result["output_tokens"]
                logging.info(f"Extracted template length: {len(template_md)}, elapsed time: {end_ts - start_ts}ms")
                
                template_json = {"template_name": template_name,
                                "template_type": template_type,
                                "template_content": template_md,
                                "permission_level": permission_level,
                                "create_time": time.strftime("%Y-%m-%dT%H:%M:%SZ")}
                # 默认保存到数据库
                if data.get('save_template', True):
                    async with async_session() as session:
                        # 为自动生成的模板计算校验码
                        content_str, check_sum = '', ''
                        content_str = json.dumps(template_md, ensure_ascii=False) if isinstance(template_md, dict) else str(template_md)
                        check_sum = generate_checksum(content_str)
                        
                        template = ArticleTemplate(
                            template_content=content_str,  # 保存模板内容
                            template_type=template_type,  # 使用传入的template_type
                            template_name=template_name,  # 使用传入的template_name
                            permission_level=permission_level,
                            is_manual=0,  # 自动生成
                            user_id=user_id,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            check_sum=check_sum,  # 添加校验码
                            is_deleted=0
                        )
                        session.add(template)
                        await session.commit()
                        await session.refresh(template)
                        template_json.update({
                            "template_id": template.template_id,
                            "is_manual": template.is_manual,
                            "user_id": template.user_id,
                            "update_time": template.update_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                        })
                        logging.info(f"Template saved to database with ID: {template.template_id}")
                
                make_response(self, 200, "模板提取成功", template_json)
                
            except Exception as e:
                make_response(self, 500, f"模板提取失败: {str(e)}")
                return
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            logging.error(f"提取模板失败: {str(e)}")
            make_response(self, 500, f"服务器错误: {str(e)}")
    
    async def call_llm_for_template(self, prompt_str):
        """
        调用大模型提取文章模板
        
        参数:
            prompt_str: 提示词
            
        返回:
            包含模板JSON、输入tokens和输出tokens的字典
        """
        async with aiohttp.ClientSession() as session:
            post_headers = {
                "Authorization": f"Bearer {config.API_KEY}",
                "Content-Type": "application/json"
            }
            post_data = {
                "model": config.LLM_MODEL,
                "messages": [
                    {"role": "user", "content": prompt_str}
                ],
                "temperature": 0.3
            }
            async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
                response.raise_for_status()
                result = await response.json()
                template_text = result["choices"][0]["message"]["content"]
                input_tokens = result.get("usage", {}).get("prompt_tokens", 0)
                output_tokens = result.get("usage", {}).get("completion_tokens", 0)
                print(f"input_tokens: {input_tokens}, output_tokens: {output_tokens}, length: {len(template_text)}")
        # 解析大模型返回的结果
        # import re
        # json_match = re.search(r'\{[\s\S]*\}', template_text)
        # if json_match:
        #     template_json = json.loads(json_match.group(0))
        # else:
        #     template_json = json.loads(template_text)
        
        return {
            "template_md": template_text,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens
        }

class DeleteArticleTemplate(BaseHandler):
    """删除文章模板处理器"""
    
    async def post(self):
        """删除文章模板"""
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'template_id' not in data:
                make_response(self, 400, "参数错误：缺少模板ID")
                return
            
            template_id = data['template_id']
            
            async with async_session() as session:
                # 查询模板是否存在
                template = await session.get(ArticleTemplate, template_id)
                if not template:
                    make_response(self, 404, "模板不存在")
                    return
                
                # 验证用户权限（只能删除自己创建的模板）
                if template.user_id != user_id:
                    make_response(self, 403, "无权删除该模板")
                    return
                
                # 逻辑删除（将is_deleted设置为1）
                template.is_deleted = 1
                template.update_time = func.current_timestamp()
                await session.commit()
                
                make_response(self, 200, "模板删除成功", {"template_id": template_id})
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            logging.error(f"删除模板失败: {str(e)}")
            make_response(self, 500, f"服务器错误: {str(e)}")
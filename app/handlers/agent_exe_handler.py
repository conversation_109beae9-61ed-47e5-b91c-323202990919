import tornado, json, logging
import subprocess
from .base_handler import BaseHandler
from models import Agent, async_session
from utils.response import make_response

class AgentExeHandler(BaseHandler):
    async def get(self):
        # 执行Agent代码
        user_id = self.get_user_id()
        if not user_id:
            return
        
        agent_id = self.get_argument('agent_id', None)
        if not agent_id:
            make_response(self, 400, "缺少agent_id参数")
            return
        try:
            # 校验Agent存在性及权限
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return

            try:
                # 获取请求包体作为输入数据
                input_data = self.request.body.decode('utf-8')  # 假设使用tornado框架，具体根据实际框架调整
                # 执行代码（简化示例，实际需添加安全沙箱）
                process = subprocess.Popen(
                    ['python3', '-c', agent.agent_code],
                    stdin=subprocess.PIPE,  # 启用标准输入
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                )
                # 传递输入数据到子进程
                process.stdin.write(input_data)
                process.stdin.close()
                # 流式获取输出
                stdout_lines = []
                stderr_lines = []
                while True:
                    # 检查进程是否结束
                    returncode = process.poll()

                    # 读取标准输出行
                    lines = process.stdout.readlines()
                    if lines:
                        stdout_lines.extend(lines)
                    # 读取标准错误行
                    lines = process.stderr.readlines()
                    if lines:
                        stderr_lines.extend(lines)

                    if returncode is not None:
                        break
                # 等待进程结束并获取最终返回码
                process.wait(10)
                result = {
                    'stdout': '\n'.join(stdout_lines),
                    'stderr': '\n'.join(stderr_lines),
                }
                make_response(self, 200, '代码执行完成', result)
            except subprocess.TimeoutExpired:
                make_response(self, 500, '代码执行超时')
            except Exception as e:
                logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
                make_response(self, 500, f'执行异常: {str(e)}')

        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'请求处理失败: {str(e)}')

class StreamAgentExeHandler(BaseHandler):
    async def get(self):
        # 执行Agent代码
        user_id = self.get_user_id()
        if not user_id:
            return
        
        agent_id = self.get_argument('agent_id', None)
        if not agent_id:
            make_response(self, 400, "缺少agent_id参数")
            return
        try:
            # 校验Agent存在性及权限
            async with async_session() as session:
                agent = await session.get(Agent, agent_id)
                if not agent:
                    make_response(self, 404, 'Agent不存在')
                    return

            try:
                # 获取请求包体作为输入数据
                input_data = self.request.body.decode('utf-8')  # 假设使用tornado框架，具体根据实际框架调整
                # 执行代码（简化示例，实际需添加安全沙箱）
                process = subprocess.Popen(
                    ['python3', '-c', agent.agent_code],
                    stdin=subprocess.PIPE,  # 启用标准输入
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                )
                # 传递输入数据到子进程
                process.stdin.write(input_data)
                process.stdin.close()
                # 流式获取输出
                stdout_lines = []
                stderr_lines = []
                while True:
                    # 检查进程是否结束
                    returncode = process.poll()

                    # 读取标准输出行
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        self.write(f"data: {json.dumps({'content': line}, ensure_ascii=False)}\n\n")
                        await self.flush()
                        stdout_lines.append(line)

                    # 读取标准错误行
                    lines = process.stderr.readlines()
                    if lines:
                        stderr_lines.extend(lines)

                    if returncode is not None:
                        break
                # 等待进程结束并获取最终返回码
                process.wait(10)
                response_data = {"code": 200, "message": "success", "data": {
                    'stdout': '\n'.join(stdout_lines),
                    'stderr': '\n'.join(stderr_lines),
                    }}
                self.write(f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n")
                await self.finish()
            except subprocess.TimeoutExpired:
                make_response(self, 500, '代码执行超时')
            except Exception as e:
                logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
                make_response(self, 500, f'执行异常: {str(e)}')

        except Exception as e:
            logging.error(f"llm request failed: {str(e)}, {logging.traceback.format_exc()}")
            make_response(self, 500, f'请求处理失败: {str(e)}')
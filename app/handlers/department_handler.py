from models import Department, User, async_session
import json
import logging
import datetime

from utils import sso
from utils.response import make_response
from handlers.base_handler import BaseHandler
from sqlalchemy import select

class DepartmentHandler(BaseHandler):
    """部门管理处理器"""
    
    async def post(self):
        """创建部门"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            async with async_session() as session:
                # 检查用户权限
                user = await session.get(User, user_id)
                if not user or user.permission_level != "admin":
                    make_response(self, 403, "权限不足，仅管理员可以创建部门")
                    return

            data = json.loads(self.request.body)
            if not data:
                make_response(self, 400, "无效的请求数据")
                return
                
            # 检查必要字段
            if 'department_name' not in data:
                make_response(self, 400, "缺少部门名称")
                return
                
            async with async_session() as session:
                # 创建新部门
                department = Department(
                    department_name=data.get('department_name'),
                    department_phone=data.get('department_phone', ''),
                    department_email=data.get('department_email', ''),
                    creator_id=user_id,
                    extro_info=data.get('extro_info')
                )
                
                session.add(department)
                try:
                    await session.commit()
                    await session.refresh(department)
                except Exception as e:
                    if 'Duplicate entry' in str(e) and 'department_name' in str(e):
                        make_response(self, 400, f"部门名称 '{data.get('department_name')}' 已存在")
                        return
                    raise e
                
                # 返回创建结果
                result = {
                    "department_id": department.department_id,
                    "department_name": department.department_name,
                    "create_time": department.create_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
                make_response(self, 200, "部门创建成功", result)
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            logging.error(f"创建部门失败: {e}")
            make_response(self, 500, "服务器内部错误")

class DepartmentUpdateHandler(BaseHandler):
    """部门信息更新处理器"""
    
    async def post(self):
        """更新部门信息"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            async with async_session() as session:
                # 检查用户权限
                user = await session.get(User, user_id)
                if not user or user.permission_level != "admin":
                    make_response(self, 403, "权限不足，仅管理员可以更新部门信息")
                    return

            data = json.loads(self.request.body)
            if not data or 'department_id' not in data:
                make_response(self, 400, "无效的请求数据")
                return
                
            department_id = data['department_id']
            
            async with async_session() as session:
                # 获取部门信息
                department = await session.get(Department, department_id)
                if not department:
                    make_response(self, 404, "部门不存在")
                    return
                
                # 更新字段
                if 'department_name' in data:
                    department.department_name = data['department_name']
                if 'department_phone' in data:
                    department.department_phone = data['department_phone']
                if 'department_email' in data:
                    department.department_email = data['department_email']
                if 'extro_info' in data:
                    department.extro_info = data['extro_info']
                
                try:
                    await session.commit()
                    await session.refresh(department)
                except Exception as e:
                    if 'Duplicate entry' in str(e) and 'department_name' in str(e):
                        make_response(self, 400, f"部门名称 '{data.get('department_name')}' 已存在")
                        return
                    raise e
                
                # 返回更新后的信息
                result = {
                    "department_id": department.department_id,
                    "department_name": department.department_name,
                    "department_phone": department.department_phone,
                    "department_email": department.department_email,
                    "update_time": department.update_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
                make_response(self, 200, "部门信息更新成功", result)
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            logging.error(f"更新部门信息失败: {e}")
            make_response(self, 500, "服务器内部错误")

class DepartmentDeleteHandler(BaseHandler):
    """部门删除处理器"""
    
    async def post(self):
        """删除部门"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            async with async_session() as session:
                # 检查用户权限
                user = await session.get(User, user_id)
                if not user or user.permission_level != "admin":
                    make_response(self, 403, "权限不足，仅管理员可以删除部门")
                    return

            data = json.loads(self.request.body)
            if not data or 'department_id' not in data:
                make_response(self, 400, "无效的请求数据")
                return
                
            department_id = data['department_id']
            
            async with async_session() as session:
                department = await session.get(Department, department_id)
                if not department:
                    make_response(self, 404, "部门不存在")
                    return
                    
                await session.delete(department)
                await session.commit()
                
                make_response(self, 200, "部门删除成功")
                
        except Exception as e:
            logging.error(f"删除部门失败: {e}")
            make_response(self, 500, "服务器内部错误")

class DepartmentListHandler(BaseHandler):
    """部门列表处理器"""
    
    async def get(self):
        """获取部门列表"""
        user_id = self.get_user_id()
        if user_id is None:
            return
            
        try:
            async with async_session() as session:
                # 检查用户是否存在
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 404, "用户不存在")
                    return

                query = select(Department)
                result = await session.execute(query)
                departments = result.scalars().all()
                
                department_list = []
                for dept in departments:
                    department_list.append({
                        "department_id": dept.department_id,
                        "department_name": dept.department_name,
                        "department_phone": dept.department_phone,
                        "department_email": dept.department_email,
                        "employee_count": dept.employee_count,
                        "create_time": dept.create_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    })
                
                make_response(self, 200, "获取部门列表成功", department_list)
                
        except Exception as e:
            logging.error(f"获取部门列表失败: {e}")
            make_response(self, 500, "服务器内部错误")


class DepartmentAllListHandler(BaseHandler):
    """全部部门列表处理器"""

    async def get(self):
        token = self.get_sso_token()
        """获取部门列表"""
        try:
            succeed, resuslt = sso.get_all_dept_tree(token)
            make_response(self, 200, "获取部门列表成功", resuslt)

        except Exception as e:
            logging.error(f"获取部门列表失败: {e}")
            make_response(self, 500, "服务器内部错误")
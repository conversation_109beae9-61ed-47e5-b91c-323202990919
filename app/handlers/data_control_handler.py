# handlers/data_control_handler.py
import json

from tornado.web import Re<PERSON><PERSON><PERSON><PERSON>

from handlers.base_handler import <PERSON>Handler
from services.data_control_service import DataControlService
from utils.response import make_response


class DataControlCreateHandler(BaseHandler):
    def post(self):
        try:
            data = json.loads(self.request.body)
            data["owner_id"] = self.get_user_id()
            new_data_control = DataControlService.create_data_control(data)
            make_response(self, code=200, message="创建数据权限成功", data=new_data_control.to_dict())
        except Exception as e:
            make_response(self, code=500, message="创建数据权限失败：" + str(e))


class DataControlDetailHandler(BaseHandler):
    def get(self):
        # 获取参数
        data = self.request.query_arguments
        data_control = DataControlService.get_data_control_by_id(data["dt_id"])
        if not data_control:
            return make_response(self, code=404, message="查询数据权限失败")
        make_response(self, code=200, message="查询数据权限成功", data=data_control.to_dict())

class DataControlFindHandler(BaseHandler):
    def get(self):
        # 获取参数
        data = self.request.query_arguments
        data_control = DataControlService.get_data_control_by_data_type_id(data["data_type"], data["data_id"])
        if not data_control:
            return make_response(self, code=500, message="查询数据权限配置不存在")
        make_response(self, code=200, message="查询数据权限成功", data=data_control.to_dict())

class DataControlUpdateHandler(BaseHandler):
    def post(self):
        data = json.loads(self.request.body)
        data["owner_id"] = self.get_user_id()
        updated = DataControlService.update_data_control(data["dt_id"], data)
        if not updated:
            return make_response(self, code=404, message="查询数据权限失败")
        make_response(self, code=200, message="更改数据权限成功")



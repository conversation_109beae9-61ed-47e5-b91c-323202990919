import tornado.web
import json
import aiohttp
import logging
import time
import re
from utils.response import make_response
import config
import config_prompt
from handlers.base_handler import BaseHandler
import os
from utils.minio import download_file
from utils.extractor import extract_text
from models import ArticleSummary, async_session
from typing import Dict, Any

async def retrive_by_uploaded_file(file_object_list):
    # 下载
    start_ts = int(time.time()*1000)
    for file_object_path in file_object_list:
        local_file_path = os.path.join(config.TEMP_DIR, os.path.basename(file_object_path))  
        await download_file(file_object_path, local_file_path)
    end_ts = int(time.time()*1000)

    # 解析
    file_md_list = []
    start_ts = end_ts
    for file_object_path in file_object_list:
        local_file_path = os.path.join(config.TEMP_DIR, os.path.basename(file_object_path))  
        # md_content, content_list_content, tmp_pdf_path = await asyncio.to_thread(parse_doc, local_file_path)
        # file_md_list.append(md_content)
        # os.remove(tmp_pdf_path)
        # if tmp_pdf_path != local_file_path:
        #     os.remove(local_file_path)
        text = extract_text(local_file_path)
        file_md_list.append(text)
        os.remove(local_file_path)
    end_ts = int(time.time()*1000)

    sys_propmt = "\n".join(file_md_list)
    # 如果文档内容过长，则截取前6000个字符+后6000个字符
    if len(sys_propmt) > 12000:
        sys_propmt = sys_propmt[:6000] + "......" + sys_propmt[-6000:]
    return sys_propmt

class SummaryHandler(BaseHandler):

    async def save_article_summary(self, user_id: int, keywords: str = "", abstract: str = "", 
                                 keysentences: str = "", keydata: str = "", article_wcount: int = 0,
                                 input_tokens: int = 0, output_tokens: int = 0, file_name: str = "",
                                 file_type: str = "", file_path: str = "", io_bucket_name: str = "") -> Dict[str, Any]:
        """保存文章摘要信息到数据库"""
        async with async_session() as session:
            article_summary = ArticleSummary(
                user_id=user_id,
                keywords=keywords,
                abstract=abstract,
                keysentences=keysentences,
                keydata=keydata,
                article_wcount=article_wcount,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                file_name=file_name,
                file_type=file_type,
                file_path=file_path,
                io_bucket_name=io_bucket_name
            )
            session.add(article_summary)
            await session.commit()
            return {
                "id": article_summary.id,
                "abstract": article_summary.abstract
            }

    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            data = json.loads(self.request.body)
            if not data or 'file_objects' not in data:
                make_response(self, 400, "参数错误：缺少文档")
                return
            
            file_objects = data.get('file_objects')
            doc = await retrive_by_uploaded_file(file_objects)
            
            # 使用配置文件中的提示词模板
            prompt = config_prompt.SUMMARY_PROMPT.format(Documents=doc)
            
            # 调用大模型进行总结
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.3
                    }
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
                        response.raise_for_status()
                        result = await response.json()
                        summary_text = result["choices"][0]["message"]["content"]
                        input_tokens = result.get("usage", {}).get("prompt_tokens", 0)
                        output_tokens = result.get("usage", {}).get("completion_tokens", 0)
                end_ts = int(time.time()*1000)
                logging.info(f"Generate summary took {end_ts - start_ts}ms")
                
                # 尝试解析大模型返回的JSON
                try:
                    json_match = re.search(r'\{[\s\S]*\}', summary_text)
                    if json_match:
                        summary_json = json.loads(json_match.group(0))
                    else:
                        summary_json = json.loads(summary_text)
                    
                    # 保存到数据库
                    storage_info = await self.save_article_summary(
                        user_id=user_id,
                        keywords=json.dumps(summary_json.get("keywords", []), ensure_ascii=False, indent=2),
                        abstract=summary_json.get("summary", ""),
                        keysentences=json.dumps(summary_json.get("keysentences", []), ensure_ascii=False, indent=2),
                        keydata=json.dumps(summary_json.get("keydata", {}), ensure_ascii=False, indent=2),
                        article_wcount=len(summary_text),
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        file_name=os.path.basename(file_objects[0]) if file_objects else "",
                        file_type=file_objects[0].split(".")[-1] if file_objects else "",
                        file_path=file_objects[0] if file_objects else "",
                        io_bucket_name=config.MINIO_BUCKET_NAME
                    )
                    
                    summary_json["summary_id"] = storage_info["id"]
                    make_response(self, 200, "success", summary_json)
                    
                except json.JSONDecodeError as e:
                    # 如果无法解析为JSON，则尝试手动解析
                    logging.error(f"解析JSON失败: {str(e)}, 尝试手动解析")
                    
                    # 手动解析逻辑保持不变
                    summary = ""
                    keywords = []
                    keysentences = []
                    keydata = {}
                    
                    lines = summary_text.split('\n')
                    current_section = None
                    
                    for line in lines:
                        line = line.strip()
                        if "summary" in line.lower() or "总结" in line:
                            current_section = "summary"
                            continue
                        elif "keywords" in line.lower() or "关键词" in line:
                            current_section = "keywords"
                            continue
                        elif "keysentences" in line.lower() or "关键句" in line:
                            current_section = "keysentences"
                            continue
                        elif "keydata" in line.lower() or "关键数据" in line:
                            current_section = "keydata"
                            continue
                        
                        if not line or line.startswith("#") or ":" not in line:
                            continue
                            
                        if current_section == "summary":
                            summary += line + " "
                        elif current_section == "keywords":
                            # 尝试提取关键词列表
                            if "," in line:
                                keywords.extend([k.strip() for k in line.split(",")])
                            elif "、" in line:
                                keywords.extend([k.strip() for k in line.split("、")])
                            else:
                                keywords.append(line.strip())
                        elif current_section == "keysentences":
                            keysentences.append(line.strip())
                        elif current_section == "keydata":
                            # 尝试提取键值对
                            parts = line.split(":", 1)
                            if len(parts) == 2:
                                key, value = parts
                                keydata[key.strip()] = value.strip()
                    
                    manual_result = {
                        "summary": summary.strip(),
                        "keywords": keywords,
                        "keysentences": keysentences,
                        "keydata": keydata
                    }
                    
                    # 保存手动解析结果到数据库
                    storage_info = await self.save_article_summary(
                        user_id=user_id,
                        keywords=json.dumps(keywords, ensure_ascii=False, indent=2),
                        abstract=summary.strip(),
                        keysentences=json.dumps(keysentences, ensure_ascii=False, indent=2),
                        keydata=json.dumps(keydata, ensure_ascii=False, indent=2),
                        article_wcount=len(summary_text),
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        file_name=os.path.basename(file_objects[0]) if file_objects else "",
                        file_type=file_objects[0].split(".")[-1] if file_objects else "",
                        file_path=file_objects[0] if file_objects else "",
                        io_bucket_name=config.MINIO_BUCKET_NAME
                    )
                    
                    manual_result["summary_id"] = storage_info["id"]
                    make_response(self, 200, "success", manual_result)
                    
            except Exception as e:
                make_response(self, 500, f"LLM请求失败: {str(e)}")
                return
                
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")
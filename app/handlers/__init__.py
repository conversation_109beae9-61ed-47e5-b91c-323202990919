from .chat_handler import ChatCompletionHandlerStream
from .data_control_handler import DataControlCreate<PERSON><PERSON><PERSON>, DataControlUp<PERSON><PERSON><PERSON><PERSON>, DataControlDetailHandler, \
    DataControlFindHandler
# from .docs_handler import <PERSON>sU<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON>ist<PERSON>andler
from .docs_handler import PresignedU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FilePreviewHandler
from .dialog_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogsList<PERSON><PERSON><PERSON>, DialogMessageHandler
from .phrase_handler import PhraseHandler, PhrasesListHandler
from .generate_handler import (
    GenerateOutline, GenerateArticle, GenerateArticleStream,
    GenerateArticleStreamWithTemplate, ManagerArticle, DeleteArticle,  # 添加 ManagerArticle 和 DeleteArticle
    GenerateLongArticleStream  # 添加长文章生成处理器
)
from .auth_handler import RegistHandler, LoginHandler, SsoLoginHandler
from .summary_handler import SummaryHandler
from .query_rec_handler import QueryRecHandler
from .article_template_handler import ManagerArticleTemplate, ExtractArticleTemplate, DeleteArticleTemplate
from .kb_handler import (
    KnowledgeBaseCreate<PERSON><PERSON><PERSON>, 
    KnowledgeBaseInfoHandler, 
    KnowledgeBaseListHandler, 
    KnowledgeBaseEditHandler,
    KnowledgeBaseDeleteHandler
)
from .kb_init_handler import KnowledgeBaseCreateSystemKBHandler  # 添加初始化系统知识库的路由
from .kb_doc_handler import (
    KnowledgeBaseDocAddHandler, KnowledgeBaseDocParseHandler, KnowledgeBaseDocStatusHandler,
    KnowledgeBaseDocListHandler, KnowledgeBaseDocDeleteHandler, DocumentRetrievalHandler,
    DocumentPreviewHandler, UserDocListHandler, KnowledgeBaseChunkManageHandler,
    KnowledgeBaseChunkRetrievalHandler, KnowledgeBaseChunkPreviewHandler
)
from .user_handler import UserHandler, QuerySsoUserHandler, QuerySsoUserRoleHandler, QuerySsoUserMenuHandler
from .user_info_handler import UserInfoHandler  # 添加导入
# 导入新的知识库分享处理程序
from .kb_share_handler import (
    KnowledgeBaseShareListHandler,
    KnowledgeBaseShareDeleteHandler,
    SharedKnowledgeBaseListHandler,
    KnowledgeBaseShareToHandler,
    KnowledgeBaseShareToDepartmentHandler  # 新增导入
)
from .department_handler import DepartmentHandler, DepartmentUpdateHandler, DepartmentDeleteHandler, \
    DepartmentListHandler, DepartmentAllListHandler
from .agent_handler import AgentHandler, AgentListHandler, AgentFileParser
from .agent_exe_handler import AgentExeHandler, StreamAgentExeHandler
from .agent_route_handler import AgentRouteHandler, AgentRouteStreamHandler
from .stats_handler import StatsQAHandler, StatsTokensHandler, StatsKBHandler, StatsModelHandler
from handlers.text_check_handler import TextCheck
from .zhiyu_callback import ZhiyuCallbackHandler
from .chatbi_handler import ChatbiHandler, ChatbiListHandler, ChatbiGetConvHandler
from .connect_config_handler import ConnectConfigHandler
from handlers.connect_config_handler import ConnectConfigHandler, ConnectConfigTestHandler, ConnectConfigTypesHandler

# 在handlers列表中添加数据源相关路由
handlers = [
    # (r"/api/v1/chat/completion", ChatCompletionHandler),
    (r"/api/v1/chat/completion/stream", ChatCompletionHandlerStream),
    # (r"/api/v1/docs/upload", DocsUploadHandler),
    # (r"/api/v1/docs", DocsListHandler),
    (r"/api/v1/docs/upload_url", PresignedUploadURLHandler),
    (r"/api/v1/docs/file_preview", FilePreviewHandler),
    (r"/api/v1/dialog", DialogHandler),
    (r"/api/v1/message", DialogMessageHandler),
    (r"/api/v1/dialogs", DialogsListHandler),
    (r"/api/v1/phrase", PhraseHandler),
    (r"/api/v1/phrases", PhrasesListHandler),
    (r"/api/v1/auth/register", RegistHandler),
    (r"/api/v1/auth/login", LoginHandler),
    (r"/api/v1/auth/ssologin", SsoLoginHandler),
    (r"/api/v1/summary", SummaryHandler),
    (r"/api/v1/query_rec", QueryRecHandler),
    # 文本内容校验相关的路由
    (r"/api/v1/text_check", TextCheck),

    # 初始化系统知识库路由
    (r"/api/v1/kb_init_system", KnowledgeBaseCreateSystemKBHandler), 

    # 文章生成相关的路由, 不使用模板根据主题和关键字先生产大纲，再生成文章
    (r"/api/v1/generate/outline", GenerateOutline),
    (r"/api/v1/generate/article", GenerateArticle),
    (r"/api/v1/generate/article/stream", GenerateArticleStream),
    # 文章生成相关的路由, 使用模板根据主题和关键字生成文章
    (r"/api/v1/generate/article/stream_with_template", GenerateArticleStreamWithTemplate),
    # 长文章生成相关的路由 - 支持数万字文章生成
    (r"/api/v1/generate/article/long_stream", GenerateLongArticleStream),
    # 对生成文章进行管理 相关路由
    (r"/api/v1/generate/article/manage_article", ManagerArticle),
    (r"/api/v1/generate/article/del_article", DeleteArticle),

    # 文章模板相关的路由
    (r"/api/v1/generate/article_template", ManagerArticleTemplate),
    (r"/api/v1/generate/extract_template", ExtractArticleTemplate),
    (r"/api/v1/generate/del_template", DeleteArticleTemplate),
    
    # 知识库管理相关的路由
    (r"/api/v1/kb/kb_create", KnowledgeBaseCreateHandler),
    (r"/api/v1/kb/kb_info", KnowledgeBaseInfoHandler),
    (r"/api/v1/kb/kb_list", KnowledgeBaseListHandler),
    (r"/api/v1/kb/kb_edit", KnowledgeBaseEditHandler),
    (r"/api/v1/kb/kb_del", KnowledgeBaseDeleteHandler),  # 添加删除知识库路由
    
    # 知识库文档相关的新路由
    (r"/api/v1/kb/kb_add_doc", KnowledgeBaseDocAddHandler),
    (r"/api/v1/kb/async_doc_parse", KnowledgeBaseDocParseHandler),
    (r"/api/v1/kb/doc_parse_status", KnowledgeBaseDocStatusHandler),
    (r"/api/v1/kb/kb_doc_list", KnowledgeBaseDocListHandler),
    (r"/api/v1/kb/doc_del", KnowledgeBaseDocDeleteHandler),
    (r"/api/v1/kb/doc_retrieval", DocumentRetrievalHandler),
    (r"/api/v1/kb/doc_preview", DocumentPreviewHandler),
    (r"/api/v1/kb/user_doc_list", UserDocListHandler),
    (f"/api/v1/kb/chunk_manage", KnowledgeBaseChunkManageHandler),
    (f"/api/v1/kb/chunk_retrival", KnowledgeBaseChunkRetrievalHandler),
    (f"/api/v1/kb/chunk_preview", KnowledgeBaseChunkPreviewHandler),
    
    # 知识库分享相关的路由
    (r"/api/v1/kb/share_to", KnowledgeBaseShareToHandler),
    (r"/api/v1/kb/share_to_department", KnowledgeBaseShareToDepartmentHandler),  # 新增路由
    (r"/api/v1/kb/share_list", KnowledgeBaseShareListHandler),
    (r"/api/v1/kb/share_del", KnowledgeBaseShareDeleteHandler),
    (r"/api/v1/kb/shared_kb_list", SharedKnowledgeBaseListHandler),

    # 用户管理相关的路由
    (r"/api/v1/user", UserHandler),
    (r"/api/v1/user/update_info", UserInfoHandler),  # 添加新的路由
    (r"/api/v1/user/query_user", QuerySsoUserHandler),
    (r"/api/v1/user/query_user_role", QuerySsoUserRoleHandler),
    (r"/api/v1/user/query_user_menu", QuerySsoUserMenuHandler),


    # 数据权限
    (r"/api/v1/data-control/create", DataControlCreateHandler),
    (r"/api/v1/data-control/update", DataControlUpdateHandler),
    (r"/api/v1/data-control/get", DataControlDetailHandler),
    (r"/api/v1/data-control/find", DataControlFindHandler),

    # 部门管理相关路由
    (r"/api/v1/department/create", DepartmentHandler),
    (r"/api/v1/department/update", DepartmentUpdateHandler),
    (r"/api/v1/department/del", DepartmentDeleteHandler),
    (r"/api/v1/department/list", DepartmentListHandler),
    (r"/api/v1/department/alllist", DepartmentAllListHandler),

    # 统计信息相关路由
    (r"/api/v1/stats/qa", StatsQAHandler),
    (r"/api/v1/stats/tokens", StatsTokensHandler),
    (r"/api/v1/stats/kbs", StatsKBHandler),
    (r"/api/v1/stats/models", StatsModelHandler),

    # Agent管理相关路由
    (r"/api/v1/agent", AgentHandler),
    (r"/api/v1/agent/execute", AgentExeHandler),
    (r"/api/v1/agent/execute/stream", StreamAgentExeHandler),
    (r"/api/v1/agents", AgentListHandler),
    (r"/api/v1/agent/route", AgentRouteHandler),
    (r"/api/v1/agent/route/stream", AgentRouteStreamHandler),
    (r"/api/v1/agent/file_parser", AgentFileParser),

    # zhiyu 相关接口
    (r"/api/v1/chat/zhiyu/callback", ZhiyuCallbackHandler),

    #chatbi app管理接口
    (r"/api/v1/chatbi", ChatbiHandler),
    (r"/api/v1/chatbi/list", ChatbiListHandler),
    (r"/api/v1/chatbi/get_conv", ChatbiGetConvHandler),

    # 数据源管理相关路由
    (r"/api/v1/datasource/types", ConnectConfigTypesHandler),
    (r"/api/v1/datasource/test", ConnectConfigTestHandler),  # 修正：使用正确的Handler
    (r"/api/v1/datasource", ConnectConfigHandler),
]

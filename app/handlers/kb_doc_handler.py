import tornado.web
import json
import uuid
from handlers.base_handler import BaseHandler
import traceback
import os

from services.document_manager import DocumentManager
from services.document_parser import DocumentParser
from utils.response import make_response
from utils.minio import generate_presigned_url
from utils.storage import StorageManager
from config import MINIO_BUCKET_NAME
import config
from models import async_session
from models.knowledge_base import KnowledgeBase

storage_manager = StorageManager()


class KnowledgeBaseDocAddHandler(BaseHandler):
    async def post(self):
        try:
            # 获取参数
            user_id = self.get_user_id()
            if user_id is None: 
                make_response(self, 400, "参数错误：缺少user_id")
                return

            # 检查必要参数
            data = json.loads(self.request.body)
            for param in ['kb_id', 'file_path', 'file_name']:
                if not data.get(param):
                    make_response(self, 400, f"参数错误：缺少{param}参数")
                    return 
            
            kb_id = data.get('kb_id')
            bucket_name = data.get('bucket_name', MINIO_BUCKET_NAME)
            file_path = data.get('file_path')
            file_name = data.get('file_name')
            
            async with async_session() as session:
                # 查询知识库信息
                kb = await session.get(KnowledgeBase, int(kb_id))
                
                if not kb or kb.is_deleted == 1:
                    make_response(self, 404, "知识库不存在")
                    return
                
                # 验证用户权限 暂时不关注user_id
                if str(kb.user_id) != str(user_id):
                    make_response(self, 403, "无权访问该知识库")
                    return
            parse_strategy = data.get('parse_strategy', kb.kb_parse_strategy)  # 解析策略
            emb_model = "bge-m3"
            if kb.extro_info and "emb_model" in kb.extro_info:
                emb_model = kb.extro_info["emb_model"]

            # 1. 保存文档基本信息
            doc = await DocumentManager.add_document(
                user_id=user_id,
                kb_id=kb_id,
                bucket_name=bucket_name,
                file_path=file_path,
                file_name=file_name
            )

            if doc:
                await storage_manager.update_kb_doc_ids(kb_id, doc["doc_id"])
                # 2. 启动异步解析任务
                await DocumentParser.start_async_parse(
                    doc_id=doc["doc_id"],
                    kb_id=kb_id,
                    bucket_name=bucket_name,
                    file_path=file_path,
                    file_name=file_name,
                    parse_strategy=parse_strategy,
                    emb_model = emb_model
                )
            else:
                make_response(self, 500, f"file_name {file_name} 文档解析失败")
                return 
            
            result = {
                "doc_id": doc['doc_id']
            }
            make_response(self, 200, "success", result)
        except json.JSONDecodeError:
            make_response(self, 400, "file_name {file_name} 无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseDocParseHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            if not data or 'kb_id' not in data or 'file_path' not in data or 'file_name' not in data or 'parse_strategy' not in data:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 获取参数
            user_id = data.get('user_id')
            kb_id = data.get('kb_id')
            file_path = data.get('file_path')
            file_name = data.get('file_name')
            parse_strategy = data.get('parse_strategy')
            
            # 模拟返回数据
            result = {
                "status": "文档解析中"
            }
            make_response(self, 200, "文档解析中", result)
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseDocStatusHandler(BaseHandler):
    async def get(self):
        try:
            doc_id = self.get_argument('doc_id')
            if not doc_id:
                make_response(self, 400, "参数错误：缺少doc_id参数")
                return
            
            parse_result = await DocumentManager.get_parse_status(
                doc_id=doc_id,
                )
            
            if not parse_result:
                make_response(self, 500, f"doc_id {doc_id} 文档解析状态获取失败")
                return

            make_response(self, 200, "success", parse_result)
        except Exception as e:
            print(traceback.format_exc())
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseDocListHandler(BaseHandler):
    async def get(self):
        try:
            kb_id = self.get_argument('kb_id', None)

            if not kb_id:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 获取user_id
            user_id = self.get_user_id()
            if user_id is None: 
                make_response(self, 400, "参数错误：缺少user_id")
                return

            # 获取分页参数
            page_no = int(self.get_argument('pageNo', '1'))
            page_size = int(self.get_argument('pageSize', '10'))
            key_word = self.get_argument('keyWord', None)
            
            # 验证分页参数
            if page_no < 1 or page_size < 1:
                make_response(self, 400, "分页参数错误")
                return

            kb_result = await storage_manager.get_kb_doc_ids(kb_id)
            if kb_result['status'] == 200:
                doc_ids = kb_result['result']['doc_id_list']
            else:
                make_response(self, 500, "获取知识库文档列表失败")
                return
            
            if key_word:
                doc_retrieval_res = await DocumentManager.retrieval_documents(
                    user_id=user_id,
                    kb_id=kb_id,
                    doc_name=key_word,
                )
                doc_ids = [doc['doc_id'] for doc in doc_retrieval_res]

            # 计算总数和分页
            total = len(doc_ids)
            offset = (page_no - 1) * page_size
            paged_doc_ids = doc_ids[offset:offset + page_size]

            # 获取文档详细信息
            doc_list = []
            for doc_id in paged_doc_ids:
                doc_info = await DocumentManager.get_doc_info(user_id, doc_id)
                if doc_info:
                    # 添加预览URL
                    preview_url = generate_presigned_url(
                        bucket_name=doc_info.get("bucket_name"),
                        object_name=doc_info.get("preview_file_path"),
                        preview=True
                    )
                    download_url = generate_presigned_url(
                        bucket_name = config.MINIO_BUCKET_NAME, 
                        object_name = doc_info.get("file_path")
                    )
                    preview_info = dict()
                    preview_info['doc_id'] = doc_id
                    preview_info["preview_url"] = preview_url
                    preview_info["download_url"] = download_url
                    preview_info["file_name"] = doc_info["file_name"]
                    preview_info["parse_status"] = doc_info["parse_status"]
                    preview_info["parse_progress"] = doc_info["doc_status"]
                    doc_list.append(preview_info)
            
            # 构建返回结果
            result = {
                "total": total,
                "list": doc_list
            }
            make_response(self, 200, "success", result)
        except ValueError:
            make_response(self, 400, "分页参数格式错误")
        except Exception as e:
            error_msg = traceback.format_exc()
            print(error_msg)
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseDocDeleteHandler(BaseHandler):
    async def post(self):
        try:
            data = json.loads(self.request.body)
            # 检查必要参数
            for param in ['doc_id']:
                if not data.get(param):
                    make_response(self, 400, f"参数错误：缺少{param}参数")
                    return 
            
            user_id = self.get_user_id()
            if user_id is None: return
            
            # 获取参数
            doc_id = data.get('doc_id')
            
            # 获取 kb_id
            doc_info = await DocumentManager.get_doc_info(user_id, doc_id)
            if not doc_info:
                make_response(self, 401, f"未找到文档 {doc_id} 的信息")
                return
            kb_id = doc_info['kb_id']
            
            # 删除文档
            delete_result = await DocumentManager.delete_document(
                doc_id=doc_id
            )

            await storage_manager.delete_kb_doc_id(kb_id, doc_id)

            if not delete_result['status']:
                make_response(self, 400, delete_result['message'])
                return 

            result = {
                "doc_id": doc_id
            }

            make_response(self, 200, "success", result)
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")

class DocumentRetrievalHandler(BaseHandler):
    async def get(self):
        try:
            user_id = self.get_user_id()
            if user_id is None: return

            kb_id = self.get_argument('kb_id', None)
            doc_name = self.get_argument('doc_name', None)
            doc_keyword = self.get_arguments('doc_keyword')  # 获取多个关键词
            
            if not doc_name or not kb_id:
                make_response(self, 400, "参数错误：缺少参数")
                return
            
            # 调用文档管理器进行检索
            doc_info = await DocumentManager.retrieval_documents(
                user_id=int(user_id),
                kb_id=int(kb_id),
                doc_name=doc_name,
                doc_keyword=doc_keyword
            )
            
            if doc_info is None:
                make_response(self, 500, "文档检索失败")

            make_response(self, 200, "success", doc_info)
        except Exception as e:
            error_msg = traceback.format_exc()
            print(error_msg)
            make_response(self, 500, f"服务器错误: {str(e)}")

class DocumentPreviewHandler(BaseHandler):
    async def get(self):
        try:
            user_id = self.get_user_id()
            if user_id is None: return

            doc_id = self.get_argument('doc_id', None)
            
            if not doc_id:
                make_response(self, 400, "参数错误：缺少doc_id参数")
                return
            
            # 调用文档管理器获取预览信息
            preview_info = await DocumentManager.get_doc_info(
                user_id=int(user_id),
                doc_id=int(doc_id)
            )
            
            if not preview_info:
                make_response(self, 401, f"未找到文档 {doc_id} 的预览信息")
                return
            
            # 生成预览URL
            preview_url = generate_presigned_url(
                bucket_name=preview_info.get("bucket_name"),
                object_name=preview_info.get("file_path"),
                preview=True  # 设置为预览模式
            )
            
            result = {
                "preview_url": preview_url,
            }
            make_response(self, 200, "success", result)
        except ValueError as ve:
            make_response(self, 400, f"参数错误: {str(ve)}")
        except Exception as e:
            error_msg = traceback.format_exc()
            print(error_msg)
            make_response(self, 500, f"服务器错误: {str(e)}")

class UserDocListHandler(BaseHandler):
    async def get(self):
        try:
            user_id = self.get_user_id()
            if user_id is None: 
                make_response(self, 400, "参数错误：缺少user_id")
                return
            
            # 获取分页参数
            page_no = int(self.get_argument('pageNo', '1'))
            page_size = int(self.get_argument('pageSize', '10'))
            
            # 验证分页参数
            if page_no < 1 or page_size < 1:
                make_response(self, 400, "分页参数错误")
                return

            # 调用文档管理器获取文档列表
            result = await DocumentManager.get_user_docs(
                user_id=int(user_id),
                page_no=page_no,
                page_size=page_size
            )
            
            if result is None:
                make_response(self, 500, "获取文档列表失败")
                return
                
            make_response(self, 200, "success", result)
                
        except ValueError:
            make_response(self, 400, "分页参数格式错误")
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")
    
class KnowledgeBaseChunkManageHandler(BaseHandler):
    """切片管理功能"""
    async def post(self):
        try:
            data = json.loads(self.request.body)
            for param in ['doc_id', 'kb_id']:
                if not data.get(param):
                    make_response(self, 400, f"参数错误：缺少{param}参数")
                    return 
            
            # 验证参数
            # 获取参数
            doc_id = data.get('doc_id', None)
            kb_id = data.get('kb_id', None)
            chunk_id = data.get('chunk_id', None)
            operation = data.get('operation', 'update')  # 默认为 update
            insert_type = data.get('insert_type', None)  # 可选参数
            chunk_content = data.get('chunk_content', None)  # 可选参数
            collection_name = data.get('collection_name', config.DEFAULT_INDEX_NAME)  # 可选参数

            if not doc_id or not kb_id:
                make_response(self, 400, "参数错误：缺少必要参数")
                return 
            
            # 验证 operation 参数
            if operation not in ['insert', 'update', 'delete']:
                make_response(self, 400, "参数错误：operation 必须是 insert/update/delete 之一")
                return
            
            # 如果是插入操作，检查插入类型
            if operation == 'insert':
                if not insert_type:
                    make_response(self, 400, "参数错误：insert 操作需要指定 insert_type")
                    return
                if insert_type not in ['raw', 'fqa', 'summary']:
                    make_response(self, 400, "参数错误：insert_type 必须是 raw/faq/summary 之一")
                    return
            
            if operation in ['insert', 'update'] and not chunk_content:
                make_response(self, 400, "参数错误：insert/update 操作需要提供 chunk_content")
                return

            if operation in ['update', 'delete'] and not chunk_id:
                make_response(self, 400, "参数错误：delete 操作需要提供 chunk_id")
                return

            # 调用文档管理器处理切片
            result = await DocumentManager.manage_chunk(
                doc_id=doc_id,
                kb_id=kb_id,
                chunk_id=chunk_id,
                operation=operation,
                insert_type=insert_type,
                chunk_content=chunk_content,
                collection_name=collection_name
            )
            
            if not result['status']:
                make_response(self, 400, result['message'])
                return

            make_response(self, 200, "success", result)
        except json.JSONDecodeError:
            make_response(self, 400, "无效的 JSON 数据")
        except Exception as e:
            error_msg = traceback.format_exc()
            print(error_msg)
            make_response(self, 500, f"服务器错误: {str(e)}")
            
class KnowledgeBaseChunkRetrievalHandler(BaseHandler):
    """切片检索功能"""
    async def post(self):
        try:
            # 从session获取用户ID
            user_id = self.get_user_id()
            if user_id is None:
                return

            data = json.loads(self.request.body)
            for param in ['kb_id', 'query']:
                if param not in data:
                    make_response(self, 400, f"参数错误：缺少{param}参数")
                    return 
                
            # 获取必要参数
            kb_id = data.get('kb_id')
            doc_ids = data.get('doc_ids')  # 多个文档 ID
            query = data.get('query')
            top_k = int(data.get('top_k', 3))  # 默认返回3条
            collection_name = data.get('collection_name', config.DEFAULT_INDEX_NAME)  # 可选参数

            search_config = data.get('search_config', {})  #检索参数
            top_k=search_config.get("topk", 10)
            retrieval_type=search_config.get("search_type", "hybrid")
            score_threshold=search_config.get("score_threshold", 0.5)
            token_max=search_config.get("token_max", 10000),
            if retrieval_type not in ['vector', 'keyword', 'hybrid']:
                make_response(self, 400, "参数错误：retrieval_type 必须是 vector/keyword/hybrid 之一")
                return

            # 获取分页参数
            page_no = int(data.get('pageNo', '1'))
            page_size = int(data.get('pageSize', '10'))

            # 验证分页参数
            if page_no < 1 or page_size < 1:
                make_response(self, 400, "分页参数错误")
                return

            async with async_session() as session:
                kb = await session.get(KnowledgeBase, kb_id)
                if not kb or kb.is_deleted == 1:
                    make_response(self, 404, "知识库不存在")
                    return
                
                # 验证用户权限 暂时不关注user_id
                if str(kb.user_id) != str(user_id):
                    make_response(self, 403, "无权修改该知识库")
                    return

            emb_model = "bge-m3"
            rerank_model = "bge-reranker-v2-m3"
            if kb.extro_info:
                if "emb_model" in kb.extro_info:
                    emb_model = kb.extro_info["emb_model"]
                if "rerank_model" in kb.extro_info:
                    rerank_model = kb.extro_info["rerank_model"]

            # 如果 doc_ids 为空，则获取该知识库下所有文档 ID
            if not doc_ids:
                doc_ids = json.loads(kb.doc_ids) if kb.doc_ids else []

            # 调用文档管理器进行检索（这里增加分页参数）
            chunks = await DocumentManager.retrieval_chunks(
                kb_id=int(kb_id),
                doc_ids=doc_ids,
                query=query,
                collection_name=collection_name,
                top_k=top_k,
                retrieval_type=retrieval_type,
                score_threshold=score_threshold,
                emb_model=emb_model,
                rerank_model=rerank_model
            )

            if chunks is None:
                make_response(self, 500, "切片检索失败")
                return
            
            total = len(chunks)

            # 构造返回结果
            paged_chunks = [{
                'title': chunk.meta_data.get('chunk_title', ''),
                'chunk_content': chunk.chunk_content,
                'chunk_id': chunk.meta_data.get('chunk_id', ''),
                'doc_id': chunk.meta_data.get('doc_id', ''),
                'score': chunk.meta_data.get('score', ''),
            } for chunk in chunks]

            # 构建分页信息
            result = {
                'total': total,
                'paged_chunks': paged_chunks,
            }

            make_response(self, 200, "成功", result)
            
        except ValueError as ve:
            make_response(self, 400, f"参数错误: {str(ve)}")
        except Exception as e:
            error_msg = traceback.format_exc()
            print(error_msg)
            make_response(self, 500, f"服务器错误: {str(e)}")

class KnowledgeBaseChunkPreviewHandler(BaseHandler):
    """切片预览功能"""
    async def get(self):
        try:
            # 获取必要参数
            kb_id = self.get_argument('kb_id')
            doc_id = self.get_argument('doc_id')
            
            if not kb_id or not doc_id:
                make_response(self, 400, "参数错误：缺少必要参数")
                return
            
            # 调用文档管理器获取切片预览
            chunks = await DocumentManager.preview_chunks(
                kb_id=int(kb_id),
                doc_id=doc_id
            )
            
            if chunks is None:
                make_response(self, 500, "切片预览失败")
                return
            
            # 构造返回结果
            total = len(chunks)
            paged_chunks = [{
                'title': chunk.meta_data.get('chunk_title', ''),
                'chunk_content': chunk.chunk_content,
                'chunk_id': chunk.meta_data.get('chunk_id', ''),
                'doc_id': chunk.meta_data.get('doc_id', ''),
            } for chunk in chunks]

            # 构建分页信息
            result = {
                'total': total,
                'paged_chunks': paged_chunks,
            }

            make_response(self, 200, "success", result)
            
        except ValueError as ve:
            make_response(self, 400, f"参数错误: {str(ve)}")
        except Exception as e:
            error_msg = traceback.format_exc()
            print(error_msg)
            make_response(self, 500, f"服务器错误: {str(e)}")
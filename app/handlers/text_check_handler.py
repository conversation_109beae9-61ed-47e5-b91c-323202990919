import json
import time
import logging
import aiohttp
import re

import config
import config_prompt
from utils.response import make_response
from handlers.base_handler import BaseHandler

class TextCheck(BaseHandler):
    async def post(self):
        try:
            # 添加用户认证检查
            user_id = self.get_user_id()
            if user_id is None:
                return
            
            # 获取请求体中的文本内容
            request_data = json.loads(self.request.body)
            text = request_data.get("text", "")
            
            if not text:
                make_response(self, 400, "文本内容不能为空")
                return
            
            # 添加字符限制，最大支持3000字符
            is_truncated = False
            if len(text) > 3000:
                text = text[:3000]
                is_truncated = True
                
            # 构建提示词
            prompt = config_prompt.TEXT_CHECK_PROMPT.format(text=text)
            
            # 调用大模型进行文本校验
            try:
                start_ts = int(time.time()*1000)
                async with aiohttp.ClientSession() as session:
                    post_headers = {
                        "Authorization": f"Bearer {config.API_KEY}",
                        "Content-Type": "application/json"
                    }
                    post_data = {
                        "model": config.LLM_MODEL,
                        "messages": [
                            {"role": "user", "content": prompt}
                        ],
                        "temperature": 0.3
                    }
                    async with session.post(config.BASE_URL, headers=post_headers, json=post_data, timeout=60) as response:
                        response.raise_for_status()
                        result = await response.json()
                        check_text = result["choices"][0]["message"]["content"]
                        input_tokens = result.get("usage", {}).get("prompt_tokens", 0)
                        output_tokens = result.get("usage", {}).get("completion_tokens", 0)
                end_ts = int(time.time()*1000)
                logging.info(f"Text check took {end_ts - start_ts}ms")
                
                # 尝试解析大模型返回的JSON
                try:
                    # 首先尝试清理可能的Markdown代码块格式
                    # 移除可能的```json和```标记
                    cleaned_text = re.sub(r'^```json\s*|\s*```$', '', check_text.strip())
                    
                    # 然后尝试查找JSON数组格式的内容
                    json_match = re.search(r'\[\s*\[.*?\]\s*\]', cleaned_text, re.DOTALL)
                    if json_match:
                        check_result = json.loads(json_match.group())
                    else:
                        # 如果没有找到JSON数组，尝试直接解析清理后的内容
                        check_result = json.loads(cleaned_text)
                    
                    # 返回结果
                    response_data = {
                        "check_result": check_result,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "total_tokens": input_tokens + output_tokens
                    }
                    response_data["truncated"] = False
                    response_data["message"] = "文本不超过3000字符限制，校验完成"
                    # 如果文本被截断，添加提示信息
                    if is_truncated:
                        response_data["truncated"] = True
                        response_data["message"] = "文本超过3000字符限制，已自动截断"
                    
                    make_response(self, 200, "文本校验成功", response_data)
                except json.JSONDecodeError as e:
                    logging.error(f"Failed to parse check result: {e}")
                    logging.error(f"Raw check result: {check_text}")
                    make_response(self, 500, f"解析校验结果失败: {str(e)}")
            except Exception as e:
                logging.error(f"Failed to check text: {e}")
                make_response(self, 500, f"文本校验失败: {str(e)}")
        except json.JSONDecodeError:
            make_response(self, 400, "无效的请求体格式")
        except Exception as e:
            logging.error(f"Unexpected error: {e}")
            make_response(self, 500, f"服务器内部错误: {str(e)}")
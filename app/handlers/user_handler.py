import config
from models import User, async_session, pwd_context
import json
import logging

from utils import sso
from utils.response import make_response
from handlers.base_handler import BaseHandler
from sqlalchemy import select, func

class UserHandler(BaseHandler):
    async def get(self):
        try:
            # 检查操作者是否为管理员
            userid = self.get_user_id()
            if userid is None: return
            # async with async_session() as session:
            #     result = await session.execute(
            #         User.__table__.select().filter(User.user_id == userid))
            #     user = result.first()
            #     if not user or user.permission_level != "admin":
            #         make_response(self, 403, '权限不足')
            #         return

            page = int(self.get_argument('page', 1))
            size = int(self.get_argument('size', 20))
            async with async_session() as session:
                result = await session.execute(
                    User.__table__.select().offset((page-1)*size).limit(size))
                users = result.all()
                total = await session.scalar(select(func.count()).select_from(User))
                make_response(self, 200, 'success', {
                    'users': [{
                        'user_id': user.user_id,
                        'username': user.username,
                        'permission_level': user.permission_level,
                        'department': user.department,
                        'group': user.group,
                        'telphone': user.telphone,
                        'id_num': user.id_num,
                        'nickname': user.nickname,
                        'created_at': user.created_at.strftime('%Y-%m-%dT%H:%M:%SZ'),
                        'last_login': user.last_login.strftime('%Y-%m-%dT%H:%M:%SZ') if user.last_login else None
                    } for user in users],
                    'total': total
                })
        except Exception as e:
            logging.error(logging.traceback.format_exc())
            make_response(self, 500, f"服务出现故障，请联系管理员")
            return

    async def post(self):
        try:
            # 检查操作者是否为管理员
            userid = self.get_user_id()
            if userid is None: return
            async with async_session() as session:
                result = await session.execute(
                    User.__table__.select().filter(User.user_id == userid))
                user = result.first()
                if not user or user.permission_level != "admin":
                    make_response(self, 403, '权限不足')
                    return

            data = json.loads(self.request.body)
            if not data or 'username' not in data or 'password' not in data or 'permission_level' not in data:
                make_response(self, 400, 'invalid request')
                return
            username = data['username']
            password = data['password']
            permission_level = data.get('permission_level', 'employee')
            
            # 获取新增字段
            department = data.get('department', '待分配')  # 更新默认值
            group = data.get('group', 'employee')
            telphone = data.get('telphone', '')
            id_num = data.get('id_num', '')
            nickname = data.get('nickname', '')

            async with async_session() as session:
                result = await session.execute(
                    User.__table__.select().filter(User.username == username))
                if result.all():
                    make_response(self, 400, '用户名已存在')
                    return
                hashed_password = pwd_context.hash(password)
                # 创建用户时包含新增字段
                new_user = User(
                    username=username, 
                    password_hash=hashed_password, 
                    permission_level=permission_level,
                    department=department,
                    group=group,
                    telphone=telphone,
                    id_num=id_num,
                    nickname=nickname
                )
                session.add(new_user)
                await session.commit()
                make_response(self, 200, 'success', {'user_id': new_user.user_id})
        except Exception as e:
            logging.error(f'创建用户失败: {e}, {logging.traceback.format_exc()}')
            make_response(self, 500, '服务器内部错误')

    async def put(self):
        try:
            # 检查操作者是否为管理员
            userid = self.get_user_id()
            if userid is None: return
            async with async_session() as session:
                result = await session.execute(
                    User.__table__.select().filter(User.user_id == userid))
                user = result.first()
                if not user or user.permission_level != "admin":
                    make_response(self, 403, '权限不足')
                    return

            data = json.loads(self.request.body)
            if not data or 'user_id' not in data:
                make_response(self, 400, 'invalid request')
                return
            user_id = data['user_id']
        
            async with async_session() as session:
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 404, '用户不存在')
                    return
                if 'username' in data:
                    user.username = data['username']
                if 'password' in data:
                    user.password_hash = pwd_context.hash(data['password'])
                if 'permission_level' in data:
                    user.permission_level = data['permission_level']
                # 更新新增字段
                if 'department' in data:
                    user.department = data['department']
                if 'group' in data:
                    user.group = data['group']
                if 'telphone' in data:
                    user.telphone = data['telphone']
                if 'id_num' in data:
                    user.id_num = data['id_num']
                if 'nickname' in data:
                    user.nickname = data['nickname']
                await session.commit()
                make_response(self, 200, 'success')
        except Exception as e:
            logging.error(f'更新用户失败: {e}, {logging.traceback.format_exc()}')
            make_response(self, 500, '服务器内部错误')

    async def delete(self):
        try:
            # 检查操作者是否为管理员
            userid = self.get_user_id()
            if userid is None: return
            async with async_session() as session:
                user = await session.get(User, userid)
                if not user or user.permission_level != "admin":
                    make_response(self, 403, '权限不足')
                    return

            data = json.loads(self.request.body)
            if not data or 'user_id' not in data:
                make_response(self, 400, 'invalid request')
                return
            user_id = data['user_id']
        
            async with async_session() as session:
                user = await session.get(User, user_id)
                if not user:
                    make_response(self, 404, '用户不存在')
                    return
                await session.delete(user)
                await session.commit()
                make_response(self, 200, 'success')
        except Exception as e:
            logging.error(f'删除用户失败: {e}, {logging.traceback.format_exc()}')
            make_response(self, 500, '服务器内部错误')

class QuerySsoUserHandler(BaseHandler):
    async def post(self):
        data = json.loads(self.request.body)
        token = self.get_sso_token()
        if not data or not token:
            make_response(self, 400, "无效token")
            return

        try:
            succeed, ssoUsers = sso.query_user(token, data)
            if not succeed:
                make_response(self, 400, "无效token")
                return

            make_response(self, 200, "success", {
                "total": ssoUsers['total'],
                "list":ssoUsers['data']
            })
        except Exception as e:
            logging.error(f"登录时出错: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "登录失败, 服务出现故障，请联系管理员")
            return

class QuerySsoUserRoleHandler(BaseHandler):
    async def post(self):
        data = json.loads(self.request.body)
        token = self.get_sso_token()
        if not data or not token:
            make_response(self, 400, "无效token")
            return

        try:
            succeed, ssoUsers = sso.get_user_role(token, data['userName'])
            if not succeed:
                make_response(self, 400, "无效token")
                return

            make_response(self, 200, "success", {
                "total": ssoUsers['total'],
                "list":ssoUsers['data']
            })
        except Exception as e:
            logging.error(f"登录时出错: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "登录失败, 服务出现故障，请联系管理员")
            return

class QuerySsoUserMenuHandler(BaseHandler):
    async def post(self):
        # data = json.loads(self.request.body)
        token = self.get_sso_token()
        if not token:
            make_response(self, 400, "无效token")
            return

        try:
            succeed, ssoUsers = sso.get_user_menu(token)
            if not succeed:
                make_response(self, 400, "无效token")
                return

            make_response(self, 200, "success", {
                "total": ssoUsers['total'],
                "list":ssoUsers['data']
            })
        except Exception as e:
            logging.error(f"登录时出错: {e}, {logging.traceback.format_exc()}")
            make_response(self, 500, "登录失败, 服务出现故障，请联系管理员")
            return
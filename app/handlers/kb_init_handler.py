import tornado.web
import json
import datetime
from utils.response import make_response
from handlers.base_handler import BaseHandler
from models.knowledge_base import KnowledgeBase
from models.user import User
from models import async_session
from sqlalchemy import select
from utils.emb import EMB_MODELS
from utils.rerank import RERANK_MODELS
import os
from config import BASE_DIR, read_yaml_config

class KnowledgeBaseCreateSystemKBHandler(BaseHandler):
    async def get(self):
        try:
            # 检查是否有admin用户
            async with async_session() as session:
                # 查询是否存在admin用户
                query = select(User).where(User.username == 'admin')
                result = await session.execute(query)
                admin_user = result.scalar_one_or_none()
                
                if not admin_user:
                    make_response(self, 400, "尚未有超级用户，请先创建admin用户")
                    return
                
                # 使用read_yaml_config函数读取配置文件
                yaml_path = os.path.join(BASE_DIR, 'conf', 'init_sys_kb.yaml')
                kb_configs = read_yaml_config(yaml_path)
                
                if not kb_configs:
                    make_response(self, 500, "读取系统知识库配置失败")
                    return
                
                # 创建系统知识库
                created_kbs = []
                # 获取kb_system_tools列表
                kb_list = kb_configs.get('kb_system_tools', [])
                
                for kb_config in kb_list:
                    # 检查知识库是否已存在
                    kb_name = kb_config.get('kb_name')
                    kb_query = select(KnowledgeBase).where(
                        KnowledgeBase.kb_name == kb_name,
                        KnowledgeBase.kb_type == 'system',
                        KnowledgeBase.is_deleted == 0
                    )
                    kb_result = await session.execute(kb_query)
                    existing_kb = kb_result.scalar_one_or_none()
                    
                    if existing_kb:
                        # 知识库已存在，跳过
                        continue
                    
                    # 处理标签：如果是列表则转换为以|分隔的字符串
                    kb_tags_raw = kb_config.get('kb_tags', [])
                    if isinstance(kb_tags_raw, list):
                        kb_tags = '|'.join(kb_tags_raw)
                    else:
                        kb_tags = kb_tags_raw
                    
                    # 创建知识库对象
                    kb = KnowledgeBase(
                        kb_name=kb_name,
                        kb_tags=kb_tags,
                        kb_description=kb_config.get('kb_description', ''),
                        kb_parse_strategy=kb_config.get('kb_parse_strategy', 'normal'),
                        kb_type=kb_config.get('kb_type', 'system'),
                        user_id=admin_user.user_id,  # 使用admin用户ID
                        permission_level='admin',  # 系统知识库权限级别为admin
                        doc_ids=[],  # 初始为空列表
                        is_deleted=0,  # 明确设置为未删除状态
                        create_time=datetime.datetime.utcnow(),
                        update_time=datetime.datetime.utcnow(),
                        extro_info={
                            "rerank_model": "bge-reranker-v2-m3",
                            "emb_model": "bge-m3",
                            "search_config": {
                                "topk": 10,
                                "search_type": "vector",
                                "score_threshold": 0.5,
                                "token_max": 10000
                            },
                            "prompt_text": kb_config.get('prompt_text', '')  # 从配置中读取prompt字段
                        }
                    )
                    
                    # 保存到数据库
                    session.add(kb)
                    await session.flush()
                    created_kbs.append({
                        "kb_id": kb.kb_id,
                        "kb_name": kb.kb_name
                    })
                
                await session.commit()
                
                # 返回创建成功的知识库信息
                if created_kbs:
                    make_response(self, 200, "系统知识库创建成功", {"created_kbs": created_kbs})
                else:
                    make_response(self, 200, "系统知识库已存在，无需重复创建")
                    
        except Exception as e:
            make_response(self, 500, f"服务器错误: {str(e)}")
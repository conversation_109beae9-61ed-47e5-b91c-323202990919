import tornado.ioloop
import tornado.web
from handlers import handlers
import logging
import config

#确保目录../log/ 存在
import os
os.makedirs("../log/", exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("../log/info.log"),
        logging.StreamHandler(), 
    ]
)


app = tornado.web.Application(handlers, debug=False)

if __name__ == "__main__":
    logging.info("Server started on port "+str(config.SERVER_PORT))
    app.listen(config.SERVER_PORT)
    tornado.ioloop.IOLoop.current().start()
    
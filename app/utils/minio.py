import config
from minio import Minio
from minio.error import S3Error
from minio.commonconfig import CopySource
import logging
import os
import datetime
import asyncio

client = Minio(
    config.MINIO_HOST,
    access_key=config.MINIO_USER,
    secret_key=config.MINIO_PASSWORD,
    secure=False  # 如果是本地开发环境，可设为 False
)

# 判断同是否存在，不存在则自动创建
try:
    if not client.bucket_exists(config.MINIO_BUCKET_NAME):
        client.make_bucket(config.MINIO_BUCKET_NAME)
        logging.info(f"创建存储桶 '{config.MINIO_BUCKET_NAME}' 成功")
    else:
        logging.info(f"存储桶 '{config.MINIO_BUCKET_NAME}' 已存在")
except S3Error as e:
    pass

def upload_file(local_file_path, object_path):
    try:
        client.fput_object(config.MINIO_BUCKET_NAME, object_path, local_file_path)
    except S3Error as e:
        logging.error(f"上传文件时出错: {e}")
        raise e


response_content_type_dict = {
    ".pdf": "application/pdf",
    ".text": "text/plain;charset=utf-8",
    ".md": "text/markdown;charset=utf-8",
    ".html": "text/html;charset=utf-8",
    ".markdown": "text/markdown;charset=utf-8",
    ".txt": "text/plain;charset=utf-8",
}


def generate_presigned_url(bucket_name, object_name, expiration=datetime.timedelta(hours=24), preview=False):
    try:
        if not preview:
            url = client.presigned_get_object(
                bucket_name, 
                object_name, 
                expires=expiration,
                extra_query_params = {
                    "response-content-disposition": f"attachment; filename=\"{object_name}\""
                }
            )
        else:
            file_type = os.path.splitext(object_name)[1].lower()
            extra_query_params = {
                "response-content-disposition": f"inline; filename=\"{object_name}\""
            }
            if file_type in response_content_type_dict:
                extra_query_params["response-content-type"] = response_content_type_dict[file_type]
            url = client.presigned_get_object(
                bucket_name,
                object_name,
                expires=expiration,
                extra_query_params = extra_query_params
            )
        return url
    except S3Error as e:
        logging.error(f"生成预签名链接时出错: {e}")
        raise e


def generate_presigned_upload_url(bucket_name, object_name, expiration=datetime.timedelta(hours=1)):
    try:
        url = client.presigned_put_object(bucket_name, object_name, expires=expiration)
        return url
    except S3Error as e:
        logging.error(f"生成上传预签名链接时出错: {e}")
        raise e


async def download_file(object_path, local_file_path):
    try:
        await asyncio.to_thread(client.fget_object, config.MINIO_BUCKET_NAME, object_path, local_file_path)
    except S3Error as e:
        logging.error(f"下载文件时出错: {e}")
        raise e

async def resave_tmp_file(tmp_object_path, target_object_path):
    try:
        await asyncio.to_thread(client.copy_object, config.MINIO_BUCKET_NAME, target_object_path, 
                                CopySource(config.MINIO_BUCKET_NAME, tmp_object_path))
    except S3Error as e:
        logging.error(f"minio保存临时文件时出错: {e}")
        raise e
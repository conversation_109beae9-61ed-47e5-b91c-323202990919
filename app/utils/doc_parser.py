import os
import fitz
import tempfile
from pathlib import Path
import config
import multiprocessing
from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from magic_pdf.config.enums import SupportedPdfParseMethod
from magic_pdf.utils.office_to_pdf import convert_file_to_pdf
import torch

if multiprocessing.get_start_method(True) is None:
    multiprocessing.set_start_method('spawn')
torch.set_num_threads(10)  # 防止线程竞争开销过大

pool = None

pdf_suffixes = ['.pdf']
ms_office_suffixes = ['.ppt', '.pptx', '.doc', '.docx']
image_suffixes = ['.png', '.jpeg', '.jpg']

# 预热模型

def read_fn(path):
    if path.suffix in ms_office_suffixes:
        convert_file_to_pdf(str(path), config.TEMP_DIR)
        fn = os.path.join(config.TEMP_DIR, f"{path.stem}.pdf")
    elif path.suffix in image_suffixes:
        with open(str(path), 'rb') as f:
            bits = f.read()
        pdf_bytes = fitz.open(stream=bits).convert_to_pdf()
        fn = os.path.join(config.TEMP_DIR, f"{path.stem}.pdf")
        with open(fn, 'wb') as f:
            f.write(pdf_bytes)
    elif path.suffix in pdf_suffixes:
        fn = str(path)
    else:
        raise Exception(f"Unknown file suffix: {path.suffix}")
    
    disk_rw = FileBasedDataReader(os.path.dirname(fn))

    return disk_rw.read(os.path.basename(fn)), fn

def parse_doc_task(path):
    # prepare env
    local_image_dir, local_md_dir = "output/images", "output"
    image_dir = str(os.path.basename(local_image_dir))
    os.makedirs(local_image_dir, exist_ok=True)
    image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(
        local_md_dir
    )

    pdf_bytes, tmp_pdf_path = read_fn(Path(path))  # read the pdf content
    ds = PymuDocDataset(pdf_bytes)
    if ds.classify() == SupportedPdfParseMethod.OCR:
        infer_result = ds.apply(doc_analyze, ocr=True)
        pipe_result = infer_result.pipe_ocr_mode(image_writer)
    else:
        infer_result = ds.apply(doc_analyze, ocr=False)
        pipe_result = infer_result.pipe_txt_mode(image_writer)

    md_content = pipe_result.get_markdown(image_dir)
    content_list_content = pipe_result.get_content_list(image_dir)

    page_origin_text = []
    for page_info in infer_result.get_infer_res():
        if "page_info" not in page_info or "layout_dets" not in page_info:
            continue
        if "page_no" not in page_info["page_info"]:
            continue
        page_origin_text.append("\n".join([layout["text"] for layout in page_info["layout_dets"] if "text" in layout]))

    return md_content, content_list_content, tmp_pdf_path, page_origin_text

def parse_doc(path):
    global pool
    if pool is None:
        pool = multiprocessing.Pool(config.MINERU_POOL_SIZE)

    return pool.apply(parse_doc_task, (path,))

if __name__ == '__main__':
    print(parse_doc_task('./test.pdf'))
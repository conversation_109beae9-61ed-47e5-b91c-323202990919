from hnswlib import Index
from models import Chunk, Session
import json, logging

index = Index(space='ip', dim=1024)
index.init_index(max_elements=100000, ef_construction=400, M=16, allow_replace_deleted=True)
index.set_ef(500)


session = Session()
chunks = session.query(Chunk).all()
embeddings = [json.loads(chunk.dense_vector) for chunk in chunks]
labels = [chunk.chunk_id for chunk in chunks]
if len(embeddings) > 0:
    index.add_items(embeddings, labels)

logging.info("index labels number:{}".format(index.get_current_count()))
def extract_content_with_titles(json_data):
    """
    返回格式改为 [{
        "title": 正文段落所属的标题（多级标题用短横线拼接）,
        "content": 正文段落
    }]
    """
    current_titles = {}   # 存储当前各级标题文本
    current_level = 0     # 当前标题层级
    current_no_title_offset = 0  # 当前无标题段落的偏移量
    content_map = {}
    title_list = []
    
    # 标题、内容、关联关系提取
    for item in json_data:
        if item["type"] == "text" and "text_level" in item:
            # 处理标题
            current_level = item["text_level"]
            current_titles[current_level] = item["text"]

            ## 多级标题拼接
            title_parts = [current_titles[i] for i in range(1, current_level+1)]
            if not title_parts:
                title = f"无标题 - {current_no_title_offset}"
                current_no_title_offset += 1
            else:
                title = " - ".join(title_parts) if title_parts else "无标题"
            if title in title_list:    # 如果“标题链”已经存在，那大概率是误识别，比如页眉、页脚、短正文
                # print("skip title:{}".format(title))
                continue
            # print("add title:{}".format(title))
            title_list.append(title)
        
        elif item["type"] in ["table", "text"]:
        # 处理文本普通段落
            if len(title_list) > 0:
                title = title_list[-1]
            else:
                title = "空标题"
            if title not in content_map:
                content_map[title] = []
            if item["type"] == "table":
                if "table_body" in item:
                    content = item["table_body"]
                elif "table_caption" in item and len(item["table_caption"])>0:
                    content = "table_caption:" + ";".join(item["table_caption"])
                elif "table_footnote" in item and len(item["table_footnote"])>0:
                    content = "table_footnote " + ";".join(item["table_footnote"])
                else:
                    content = ""
            else:
                content = item["text"]
            content_map[title].append(content)

        else:
        # 其他类型段落先跳过
            pass
    
    # 整理结果
    result = []
    # current_empty_title = None
    # empty_flag = False
    for title in title_list:
        # 空内容标题合并
        if title not in content_map:
            # if not empty_flag:  # 上一个标题内容非空，当前标题内容为空，重置current_empty_title
            #     current_empty_title = None

            # empty_flag = True
            # if current_empty_title is None:
            #     current_empty_title = title
            # else:
            #     current_empty_title += " - " + title
            continue
        
        # 超短文本合并
        # empty_flag = False
        content_list = content_map[title]
        # new_title = current_empty_title + " - " + title if current_empty_title else title
        new_title = title
        i = 0
        while i < len(content_list):
            temp_content = content_list[i]
            while len(temp_content) < 2000 and i+1 < len(content_list):
                temp_content += "\n " + content_list[i+1]
                i += 1
            i += 1
            result.append({"title": new_title, "content": temp_content})
        
        if len(result[-1]["content"]) < 2000 and len(result) >1 and "tltle" and result[-1]["title"] == result[-2]["title"]:
            result[-2]["content"] += "\n " + result[-1]["content"]
            result.pop()
    result.append({"title": " - ".join(title_list), "content":""}) # title_list作为一个特殊切片

    return result

if __name__ == '__main__':
    import json
    with open('test.json') as f:
        data = json.load(f)
        
    res = extract_content_with_titles(data)
    print(json.dumps(res, indent=2, ensure_ascii=False))

import requests
import json
import logging

from config import SSO_API_BASE_URL, SSO_SYSTEM_CODE

API_USER_URL = SSO_API_BASE_URL + "api/internal/auth/getSelfDetail"
API_USER_QUERY_URL = SSO_API_BASE_URL + "api/public/auth/queryUsers"
API_USER_ROLE_URL = SSO_API_BASE_URL + "api/public/auth/queryUserRole"
API_MENU_URL = SSO_API_BASE_URL + "api/internal/auth/menu/queryMenuBySystem"
API_PERMISSIONS_URL = SSO_API_BASE_URL + "api/internal/auth/queryMyPermissions"
API_DEPT_URL = SSO_API_BASE_URL + "api/public/umr/dept/queryDeptForTreeByCode"

# 对接第三方平台，单点登录及权限操作接口

# 用户信息接口
# url: api/internal/auth/getSelfDetail
# invoke the http api from :api/internal/auth/getSelfDetail, add Name: Authorization to http header, it values is token  get from login
# write a function to get user info
def get_user_info(token):
    return request_api("POST", API_USER_URL,  token, {})

def query_user(token, params):
    return request_api_obj("POST", API_USER_QUERY_URL,  token, params)

# 用户菜单接口
def get_user_menu(token):
    return request_api_obj("GET", API_MENU_URL, token, {
        "systemCode": SSO_SYSTEM_CODE
    })

#  用户角色接口
def get_user_role(token, user_name):
    return request_api_obj("GET",  API_USER_ROLE_URL, token, {
        "username": user_name
    })

# 部门树接口
def get_dept_tree(token, code):
    return request_api("GET",  API_DEPT_URL, token, {
        "code": code
    })
#  全部部门树接口
def get_all_dept_tree(token):
    return request_api("GET",  API_DEPT_URL, token, {

    })

# 请求接口,根据token和url调用接口的通用方法
def request_api(method, url, token, params):
    try:
        headers = {
            "Authorization": "DXS " + token
        }
        response = requests.request(
            method=method,
            params=params if method == "GET" else None,
            json=params if method == "POST" else None,
            url=url,
            headers=headers,
            timeout=5  # 添加超时控制
        )

        # 检查 HTTP 状态码
        if response.status_code != 200:
            raise Exception(f"HTTP 请求失败: {response.status_code}, 响应内容: {response.text[:200]}")

        # 尝试解析 JSON
        try:
            result = response.json()
        except json.JSONDecodeError:
            raise Exception("响应内容不是合法的 JSON 格式")

        # 检查业务状态码
        if result.get("code", 0) != 200:
            raise Exception(f"获取用户信息失败:{result.get('msg', '')}")

        data = result.get("data", {})
        return True, data

    except requests.exceptions.RequestException as req_err:
        error_msg = f"网络请求异常: {str(req_err)}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    except Exception as ex:
        error_msg = str(ex)
        logging.error(f"An error occurred in request_api: {error_msg}", exc_info=True)
        return False, error_msg

def request_api_obj(method, url, token, params):
    try:
        headers = {
            "Authorization": "DXS " + token
        }
        response = requests.request(
            method=method,
            params=params if method == "GET" else None,
            json=params if method == "POST" else None,
            url=url,
            headers=headers,
            timeout=5  # 添加超时控制
        )

        # 检查 HTTP 状态码
        if response.status_code != 200:
            raise Exception(f"HTTP 请求失败: {response.status_code}, 响应内容: {response.text[:200]}")

        # 尝试解析 JSON
        try:
            result = response.json()
        except json.JSONDecodeError:
            raise Exception("响应内容不是合法的 JSON 格式")

        # 检查业务状态码
        if result.get("code", 0) != 200:
            raise Exception(f"获取用户信息失败:{result.get('msg', '')}")

        return True, result

    except requests.exceptions.RequestException as req_err:
        error_msg = f"网络请求异常: {str(req_err)}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    except Exception as ex:
        error_msg = str(ex)
        logging.error(f"An error occurred in request_api: {error_msg}", exc_info=True)
        return False, error_msg

if __name__ == "__main__":
    token = "7a786c95649e43879c96f4af1f9c2813"
    succeed, ssoUser = get_user_info(token)
    if not succeed:
        print("获取用户信息失败:", ssoUser)

    ssoUserInfo = ssoUser.get('basicInfo')
    print(ssoUserInfo.get("username"))
    print(ssoUserInfo.get("nickName"))
    print(ssoUserInfo.get("deptName"))
    print(ssoUserInfo.get("deptCode"))
    print(ssoUserInfo.get("deptId"))

    print(get_user_role(token, "admin"))
    print(get_user_menu(token))
    print(get_dept_tree(token,  "DP01"))
    print(get_all_dept_tree(token))
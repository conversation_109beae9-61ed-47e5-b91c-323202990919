from FlagEmbedding import <PERSON><PERSON><PERSON><PERSON>
from typing import List, <PERSON><PERSON>
from config import RER<PERSON>K_MODEL_PATH
from connector.retrieval_document import RetrievalDocument as Document

# RERANK_MODEL_PATH = {"bge-reranker-v2-m3": "/home/<USER>/huggingface.co/BAAI/bge-reranker-v2-m3"}

RERANK_MODELS = {
    "bge-reranker-v2-m3": FlagReranker(RERANK_MODEL_PATH['bge-reranker-v2-m3'], use_fp16=True, devices="cuda:0")
}

def rerank(rerank_model: str, query: str, docs: List[Document], score_threshold: float = 0.5) -> Tuple[List[str], List[float]]:
    if not docs:
        return [], []
    scores = RERANK_MODELS[rerank_model].compute_score([[query, doc.chunk_content] for doc in docs], normalize=True)
    print(scores)
    for i in range(len(docs)):
        docs[i].meta_data["score"] = scores[i]
    ranked = sorted(zip(docs, scores), key=lambda x: x[1], reverse=True)
    
    filtered = [(doc, score) for doc, score in ranked if score >= score_threshold]
    # if len(filtered) == 0:
    #     filtered = [(doc, score) for doc, score in ranked[0:5]]
    reranked_docs = [doc for doc, _ in filtered]
    reranked_scores = [score for _, score in filtered]
    
    return reranked_docs, reranked_scores

if __name__ == '__main__':
    # # 示例用法
    # sample_docs = ["文档1", "文档2", "文档3", "文档4", "文档5", "文档6"]
    # sample_scores = [0.8, 0.6, 0.9, 0.4, 0.7, 0.55]
    # reranker = Reranker()
    # result_docs, result_scores = reranker.rerank(sample_docs, sample_scores, top_k=3, score_threshold=0.5)
    # print("排序后的文档:", result_docs)
    # print("对应的得分:", result_scores)"}}}
    print(rerank('bge-reranker-v2-m3', '你好', ['你好', '世界']))
from elasticsearch import Elasticsearch
import config

# 连接到 Elasticsearch
es = Elasticsearch([config.ES_HOST], basic_auth=(config.ES_USER, config.ES_PASSWORD))

INDEX_NAME = "rag_demo"

# 创建索引
if not es.indices.exists(index=INDEX_NAME):
    es.indices.create(index=INDEX_NAME)

# 添加文档
def upsert(id, doc_title, doc_content):
    doc = {
        "doc_title": doc_title,
        "doc_content": doc_content
    }
    res = es.index(index=INDEX_NAME, body=doc, id=str(id))

# 搜索文档
def search(query_text, limit=3):
    query = {
        "query": {
            "match": {
                "doc_content": query_text
            }
        }
    }

    result = [[],[],[]]
    res = es.search(index=INDEX_NAME, body=query, size=limit)
    for hit in res['hits']['hits']:
        result[0].append(int(hit.get("_id")))
        result[1].append(hit.get("_score"))
        result[2].append(hit.get("_source"))
    
    return result

# 删除文档
def delete(id):
    es.delete(index=INDEX_NAME, id=str(id))
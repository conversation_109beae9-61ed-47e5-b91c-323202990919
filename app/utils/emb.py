from FlagEmbedding import BGEM3FlagModel
from config import EMB_MODEL_PATH

EMB_MODELS = {
    "bge-m3": BGEM3FlagModel(EMB_MODEL_PATH["bge-m3"], use_fp16=True, devices="cuda:0")
    # "bge-m3": BGEM3FlagModel("/home/<USER>/ai_models", use_fp16=True, devices="cuda:0")
}


def get_embedding(model, sentences):
    if model not in EMB_MODELS:
        raise ValueError(f"Model {model} not found")
    
    res =  EMB_MODELS[model].encode(sentences,  return_dense=True, return_sparse=True, return_colbert_vecs=False)
    dens_vecs = res["dense_vecs"].tolist()
    assert len(dens_vecs) == len(sentences)
    sparse_vecs = []
    for group in res['lexical_weights']:
        sparse_vec = []
        for k, v in group.items():
            sparse_vec.append((int(k),float(v)))
        sparse_vecs.append(sparse_vec)
    return dens_vecs, sparse_vecs

if __name__ == '__main__':
    # import time
    # start = time.time()
    # for i in range(1000):
        # get_embedding('bge-m3', ['你好', '世界'])
    # print(int(time.time() - start))
    dens_vecs, sparse_vecs = get_embedding('bge-m3', ['你好', '世界'])
    for k, v in sparse_vecs[0].items():
        print(k, v)

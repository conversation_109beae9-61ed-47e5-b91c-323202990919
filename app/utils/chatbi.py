import requests
import json
import logging

from config import CHATBI_API_KEY, CHATBI_BASE_URL

API_CHAT_DIALOGUE_NEW_URL = CHATBI_BASE_URL + "/api/v1/chat/dialogue/new"


# 对接第三方CHATBI平台操作接口

# 获取会话ID
def get_conv_id():
    return request_api("POST", API_CHAT_DIALOGUE_NEW_URL, {
        "chat_mode": "chat_with_db_execute",
        "model_name": "undefined"
    })


# 调用url接口的通用方法
def request_api(method, url, params):
    try:
        headers = {
            "Authorization": "Bearer " + CHATBI_API_KEY
        }
        response = requests.request(
            method=method,
            params=params if method == "GET" else None,
            json=params if method == "POST" else None,
            url=url,
            headers=headers,
            timeout=10  # 添加超时控制
        )

        # 检查 HTTP 状态码
        if response.status_code != 200:
            raise Exception(f"ChatBI 请求失败: {response.status_code}, 响应内容: {response.text[:200]}")

        # 尝试解析 JSON
        try:
            result = response.json()
        except json.JSONDecodeError:
            raise Exception("响应内容不是合法的 JSON 格式")

        # 检查业务状态码
        if result.get("success") != True:
            raise Exception(f"调用ChatBI接口【{url}】失败, 详细错误信息：{result.get('err_msg', '')}")

        data = result.get("data", {})
        return True, data

    except requests.exceptions.RequestException as req_err:
        error_msg = f"网络请求异常: {str(req_err)}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    except Exception as ex:
        error_msg = str(ex)
        logging.error(f"An error occurred in request_api: {error_msg}", exc_info=True)
        return False, error_msg

def request_api_obj(method, url, token, params):
    try:
        headers = {
            "Authorization": "DXS " + token
        }
        response = requests.request(
            method=method,
            params=params if method == "GET" else None,
            json=params if method == "POST" else None,
            url=url,
            headers=headers,
            timeout=5  # 添加超时控制
        )

        # 检查 HTTP 状态码
        if response.status_code != 200:
            raise Exception(f"HTTP 请求失败: {response.status_code}, 响应内容: {response.text[:200]}")

        # 尝试解析 JSON
        try:
            result = response.json()
        except json.JSONDecodeError:
            raise Exception("响应内容不是合法的 JSON 格式")

        # 检查业务状态码
        if result.get("code", 0) != 200:
            raise Exception(f"获取用户信息失败:{result.get('msg', '')}")

        return True, result

    except requests.exceptions.RequestException as req_err:
        error_msg = f"网络请求异常: {str(req_err)}"
        logging.error(error_msg, exc_info=True)
        return False, error_msg
    except Exception as ex:
        error_msg = str(ex)
        logging.error(f"An error occurred in request_api: {error_msg}", exc_info=True)
        return False, error_msg

if __name__ == "__main__":
    token = "7a786c95649e43879c96f4af1f9c2813"
    succeed, conv = get_conv_id()
    if not succeed:
        print("获取会话信息:", conv)

    conv_uid = conv.get('conv_uid')
    print("conv_uid = " + conv_uid)
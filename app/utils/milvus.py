import json
from models import Chunk, Session

from pymilvus import (
    connections,
    MilvusClient,
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
    AnnSearchRequest,
    WeightedRanker,
)

connections.connect(uri="./data/milvus.db")

col_name = "hybrid_rag_col"

fields = [
    FieldSchema(name="chunk_id", dtype=DataType.INT64, is_primary=True, auto_id=False, max_length=100),
    FieldSchema(name="sparse_vector", dtype=DataType.SPARSE_FLOAT_VECTOR),
    FieldSchema(name="dense_vector", dtype=DataType.FLOAT_VECTOR, dim=1024),
]
schema = CollectionSchema(fields)


if utility.has_collection(col_name):
    Collection(col_name).drop()
col = Collection(col_name, schema, consistency_level="Strong")

col.create_index("sparse_vector", {"index_type": "SPARSE_INVERTED_INDEX", "metric_type": "IP"})
col.create_index("dense_vector", {"index_type": "AUTOINDEX", "metric_type": "IP"})
col.load()

session = Session()
chunks = session.query(Chunk).all()
dense_embs = [json.loads(chunk.dense_vector) for chunk in chunks]
sparse_embs = [json.loads(chunk.sparse_vector) for chunk in chunks]
labels = [chunk.chunk_id for chunk in chunks]
if len(labels) > 0:
    col.upsert([labels,sparse_embs,dense_embs])


def upsert(datas):
    col.upsert([
        [data["chunk_id"] for data in datas],
        [data["sparse_vector"] for data in datas],
        [data["dense_vector"] for data in datas]
    ])

def delete(chunk_ids):
    col.delete("chunk_id in {}".format(json.dumps(chunk_ids)))

def dense_search(col, dense_embedding, limit=10):
    search_params = {"metric_type": "IP", "params": {}}
    res = col.search(
        [dense_embedding],
        anns_field="dense_vector",
        limit=limit,
        param=search_params,
    )[0]
    return [hit.get("chunk_id") for hit in res]


def sparse_search(col, sparse_embedding, limit=10):
    search_params = {
        "metric_type": "IP",
        "params": {},
    }
    res = col.search(
        [sparse_embedding],
        anns_field="sparse_vector",
        limit=limit,
        param=search_params,
    )[0]
    return [hit.get("chunk_id") for hit in res]


def hybrid_search(
    dense_embedding,
    sparse_embedding,
    sparse_weight=1.0,
    dense_weight=1.0,
    limit=10,
):
    dense_search_params = {"metric_type": "IP", "params": {}}
    dense_req = AnnSearchRequest(
        [dense_embedding], "dense_vector", dense_search_params, limit=limit
    )
    sparse_search_params = {"metric_type": "IP", "params": {}}
    sparse_req = AnnSearchRequest(
        [sparse_embedding], "sparse_vector", sparse_search_params, limit=limit
    )
    rerank = WeightedRanker(sparse_weight, dense_weight)
    res = col.hybrid_search(
        [dense_req, sparse_req], rerank=rerank, limit=limit
    )[0]
    return [hit.id for hit in res], [hit.distance for hit in res]
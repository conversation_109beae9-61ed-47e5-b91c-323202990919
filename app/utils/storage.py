import logging
from datetime import datetime
import os
from typing import Dict, Any
from models import Article, async_session
from sqlalchemy.future import select
from .minio import upload_file, generate_presigned_url
import config
from sqlalchemy import or_, func
from models import KnowledgeBase 
import json  # 添加这行

class StorageManager:
    async def save_article(self, doc_topic: str, content: str) -> Dict[str, Any]:
        """保存文章到MinIO并记录到MySQL"""
        try:
            # 生成文件名
            title = content.split('\n')[0].strip('# ').strip()
            # timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            # file_name = f"{title}_{timestamp}.md"
            file_name = f"{title}.md"
            temp_path = os.path.join(config.BASE_DIR, "temp", file_name)
            
            # 确保临时目录存在
            os.makedirs(os.path.dirname(temp_path), exist_ok=True)
            
            # 保存到临时文件
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 使用minio.py中的upload_file上传文件
            file_size = os.path.getsize(temp_path)
            minio_path = "generated_docs/"+file_name
            upload_file(temp_path, minio_path)
            
            # 删除临时文件
            os.remove(temp_path)
            
            # 使用SQLAlchemy保存记录
            async with async_session() as session:
                article = Article(
                    doc_topic=doc_topic,
                    file_name=file_name,
                    file_type='markdown',
                    io_bucket_name=config.MINIO_BUCKET_NAME,
                    file_path=minio_path,
                    file_size=file_size
                )
                session.add(article)
                await session.commit()
                await session.refresh(article)
                article_id = article.id
            
            # 使用minio.py中的generate_presigned_url生成访问URL
            file_url = generate_presigned_url(
                config.MINIO_BUCKET_NAME,
                minio_path
            )
            
            return {
                "article_id": article_id,
                "file_name": file_name,
                "file_path": minio_path,
                "file_size": file_size,
                "download_url": file_url
            }
            
        except Exception as e:
            logging.error(f"保存文章失败: {str(e)}")
            return None

    async def get_article_info(self, article_id: int) -> Dict[str, Any]:
        """获取文章信息"""
        async with async_session() as session:
            result = await session.execute(
                select(Article).where(Article.id == article_id)
            )
            article = result.scalar_one_or_none()
            
            if article:
                article_dict = {
                    "id": article.id,
                    "doc_topic": article.doc_topic,
                    "file_name": article.file_name,
                    "file_type": article.file_type,
                    "file_path": article.file_path,
                    "file_size": article.file_size,
                    "create_time": article.create_time,
                    "update_time": article.update_time,
                    "download_url": generate_presigned_url(
                        article.io_bucket_name,
                        article.file_path
                    )
                }
                return article_dict
            return None

    async def search_articles(self, keyword: str = None, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """
        按名称模糊搜索文章
        Args:
            keyword: 搜索关键词
            limit: 每页数量
            offset: 偏移量
        Returns:
            包含文章列表和总数的字典
        """
        try:
            async with async_session() as session:
                # 构建查询条件
                query = select(Article)
                if keyword:
                    query = query.where(
                        or_(
                            Article.file_name.ilike(f"%{keyword}%"),
                            Article.doc_topic.ilike(f"%{keyword}%")
                        )
                    )
                
                # 获取总数
                count_query = select(func.count()).select_from(query.subquery())
                total = await session.scalar(count_query)
                
                # 获取分页数据
                query = query.order_by(Article.create_time.desc())
                query = query.offset(offset).limit(limit)
                result = await session.execute(query)
                articles = result.scalars().all()
                
                # 转换为字典列表并添加下载链接
                articles_list = []
                for article in articles:
                    article_dict = {
                        "id": article.id,
                        "doc_topic": article.doc_topic,
                        "file_name": article.file_name,
                        "file_type": article.file_type,
                        "file_path": article.file_path,
                        "file_size": article.file_size,
                        "create_time": article.create_time,
                        "update_time": article.update_time,
                        "download_url": generate_presigned_url(
                            article.io_bucket_name,
                            article.file_path
                        )
                    }
                    articles_list.append(article_dict)
                
                return {
                    "total": total,
                    "articles": articles_list
                }
                
        except Exception as e:
            logging.error(f"搜索文章失败: {str(e)}")
            raise

    async def update_kb_doc_ids(self, kb_id: int, doc_id: int) -> bool:
        """
        更新知识库的文档ID列表
        Args:
            kb_id: 知识库ID
            doc_id: 要添加的文档ID
        Returns:
            bool: 更新是否成功
        """
        try:
            async with async_session() as session:
                # 查询知识库，确保未被删除
                kb = await session.get(KnowledgeBase, kb_id)
                if not kb or kb.is_deleted == 1:
                    logging.error(f"更新知识库文档失败: 知识库不存在或已被删除 kb_id={kb_id}")
                    return False
                
                # 更新 doc_ids 字段
                try:
                    current_doc_ids = json.loads(kb.doc_ids) if kb.doc_ids else []
                except (TypeError, json.JSONDecodeError):
                    current_doc_ids = []
                
                logging.info(f"更新前的doc_ids: {current_doc_ids}")
                
                if doc_id not in current_doc_ids:
                    current_doc_ids.append(doc_id)
                    # 将列表转换为JSON字符串存储
                    kb.doc_ids = json.dumps(current_doc_ids)
                    kb.update_time = datetime.utcnow()
                    
                    await session.commit()
                    logging.info(f"知识库文档更新成功: kb_id={kb_id}, doc_id={doc_id}, current_doc_ids={current_doc_ids}")
                    return True
                else:
                    logging.info(f"文档ID已存在: kb_id={kb_id}, doc_id={doc_id}, current_doc_ids={current_doc_ids}")
                
                return True
                
        except Exception as e:
            logging.error(f"更新知识库文档失败: kb_id={kb_id}, doc_id={doc_id}, error={str(e)}")
            return False

    async def delete_kb_doc_id(self, kb_id: int, doc_id: int) -> Dict[str, Any]:
        """
        从知识库的文档ID列表中删除指定文档
        Args:
            kb_id: 知识库ID
            doc_id: 要删除的文档ID
        Returns:
            Dict: 包含删除结果的字典
        """
        try:
            async with async_session() as session:
                # 查询知识库，确保未被删除
                kb = await session.get(KnowledgeBase, kb_id)
                if not kb or kb.is_deleted == 1:
                    logging.error(f"删除知识库文档失败: 知识库不存在或已被删除 kb_id={kb_id}")
                    return {"status": 404, "message": "知识库不存在", "result": None}
                
                # 获取并更新 doc_ids 字段
                try:
                    current_doc_ids = json.loads(kb.doc_ids) if kb.doc_ids else []
                except (TypeError, json.JSONDecodeError):
                    current_doc_ids = []
                
                logging.info(f"删除前的doc_ids: {current_doc_ids}")
                
                if doc_id in current_doc_ids:
                    current_doc_ids.remove(doc_id)
                    # 将更新后的列表转换为JSON字符串存储
                    kb.doc_ids = json.dumps(current_doc_ids)
                    kb.update_time = datetime.utcnow()
                    
                    await session.commit()
                    logging.info(f"知识库文档删除成功: kb_id={kb_id}, doc_id={doc_id}, current_doc_ids={current_doc_ids}")
                    return {"status": 200, "message": "成功", "result": {"doc_id": doc_id}}
                else:
                    logging.info(f"文档ID不存在: kb_id={kb_id}, doc_id={doc_id}, current_doc_ids={current_doc_ids}")
                    return {"status": 404, "message": "文档ID不存在", "result": None}
                
        except Exception as e:
            error_msg = f"删除知识库文档失败: kb_id={kb_id}, doc_id={doc_id}, error={str(e)}"
            logging.error(error_msg)
            return {"status": 500, "message": error_msg, "result": None}

    async def get_kb_doc_ids(self, kb_id: int) -> Dict[str, Any]:
        """
        获取知识库的文档ID列表
        Args:
            kb_id: 知识库ID
        Returns:
            Dict: 包含文档ID列表的字典
        """
        try:
            async with async_session() as session:
                # 查询知识库，确保未被删除
                kb = await session.get(KnowledgeBase, kb_id)
                if not kb or kb.is_deleted == 1:
                    logging.error(f"获取知识库文档列表失败: 知识库不存在或已被删除 kb_id={kb_id}")
                    return {"status": 404, "message": "知识库不存在", "result": None}
                
                # 获取 doc_ids 字段
                try:
                    doc_id_list = json.loads(kb.doc_ids) if kb.doc_ids else []
                except (TypeError, json.JSONDecodeError):
                    doc_id_list = []
                
                logging.info(f"获取知识库文档列表成功: kb_id={kb_id}, doc_id_list={doc_id_list}")
                return {
                    "status": 200, 
                    "message": "成功", 
                    "result": {"doc_id_list": doc_id_list}
                }
                
        except Exception as e:
            error_msg = f"获取知识库文档列表失败: kb_id={kb_id}, error={str(e)}"
            logging.error(error_msg)
            return {"status": 500, "message": error_msg, "result": None}

    async def save_article_outline(self, user_id, doc_topic, keywords="", outline="", input_tokens=0, output_tokens=0):
        """保存文章相关信息到数据库"""
        async with async_session() as session:
            article = Article(
                user_id=user_id,
                doc_topic=doc_topic,
                keywords=keywords,
                outline=outline,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )
            session.add(article)
            await session.commit()
            return {
                "id": article.id,
                "doc_topic": article.doc_topic
            }

    async def update_article(self, id: int, doc_topic: str, keywords: str, wcount: int, outline: str, 
                           content: str, input_tokens: int, output_tokens: int) -> Dict[str, Any]:
        """更新文章信息并保存到MinIO"""
        try:
            # 生成文件名和计算字数
            title = content.split('\n')[0].strip('# ').strip()
            if 'markdown' in title:
                title = f"{doc_topic}_{keywords}_{wcount}"
            file_name = f"{title}.md"
            article_wcount = len(content)  # 简单计算字数
            temp_path = os.path.join(config.BASE_DIR, "temp", file_name)
            
            # 确保临时目录存在
            os.makedirs(os.path.dirname(temp_path), exist_ok=True)
            
            # 保存到临时文件
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 使用minio.py中的upload_file上传文件
            minio_path = "generated_docs/"+file_name
            upload_file(temp_path, minio_path)
            
            # 删除临时文件
            os.remove(temp_path)
            
            # 使用SQLAlchemy更新记录
            async with async_session() as session:
                # 先获取原有记录
                article = await session.get(Article, id)
                if not article:
                    raise ValueError(f"Article with id {id} not found")
                
                # 更新字段
                article.doc_topic = doc_topic
                if keywords:  # 只在keywords不为空时更新
                    article.keywords = keywords
                article.wcount = wcount
                article.outline = outline
                article.article_wcount = article_wcount
                article.file_size = article_wcount
                article.input_tokens += input_tokens  # 累加tokens
                article.output_tokens += output_tokens  # 累加tokens
                article.file_name = file_name
                article.file_type = 'markdown'
                article.io_bucket_name = config.MINIO_BUCKET_NAME
                article.file_path = minio_path
                
                await session.commit()
                await session.refresh(article)
            
            # 生成访问URL
            file_url = generate_presigned_url(
                config.MINIO_BUCKET_NAME,
                minio_path
            )
            
            return {
                "id": article.id,
                "doc_topic": article.doc_topic,
                "keywords": article.keywords,
                "wcount": article.wcount,
                "article_wcount": article.article_wcount,
                "file_size": article.file_size,
                "input_tokens": article.input_tokens,
                "output_tokens": article.output_tokens,
                "file_name": file_name,
                "file_path": minio_path,
                "download_url": file_url
            }
            
        except Exception as e:
            logging.error(f"更新文章失败: {str(e)}")
            raise

import PyPDF2
import docx
import logging
import config
import os
from magic_pdf.utils.office_to_pdf import convert_file_to_pdf
import pandas as pd
from deepdoc.helpers import detect_file_encodings

def html_text_from_csv(csv_file_path, html_file_path):
    try:
        # 读取 CSV 文件
        df = pd.read_csv(csv_file_path)

        # 将 DataFrame 转换为 HTML 表格
        html_text = df.to_html()
        return html_text
    except Exception as e:
        print(f"读取 csv 文件时出错: {e}")

def html_text_from_excel(excel_file_path):
    try:
        excel_file = pd.ExcelFile(excel_file_path)
        sheet_names = excel_file.sheet_names
        html_content = []
        chunks = []
        for sheet_name in sheet_names:
            df = excel_file.parse(sheet_name)
            sheet_html = df.to_html(na_rep='nan')
            html_content.append(f'<h2>{sheet_name}</h2>')
            html_content.append(sheet_html)
            chunks.append({"title": sheet_name, "content": sheet_html})
        full_html = '\n'.join(html_content)
        return full_html, chunks
    except Exception as e:
        logging.warning(f"读取 excel 文件时出错: {e}")
        raise e

def extract_text_from_pdf(file_path):
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text()
            return text
    except Exception as e:
        logging.warning(f"读取 PDF 文件时出错: {e}")
        raise e


def extract_text_from_doc(file_path):
    try:
        convert_file_to_pdf(str(file_path), config.TEMP_DIR)
        suffix = file_path.split(".")[-1]
        fn = os.path.join(config.TEMP_DIR, f"{file_path.replace('.'+suffix, '')}.pdf")
        text = extract_text_from_pdf(fn)
        return text
    except Exception as e:
        logging.warning(f"读取 DOC 文件时出错: {e}")
        raise e
    finally:
        os.remove(fn)


def extract_text_from_docx(file_path):
    try:
        doc = docx.Document(file_path)
        text = ""
        for para in doc.paragraphs:
            text += para.text + "\n"
        return text
    except Exception as e:
        logging.warning(f"读取 DOCX 文件时出错: {e}")
        raise e

def extract_text_from_text(file_path):
    try:
        with open(file_path, "rb") as fd:
            detected_encodings = detect_file_encodings(file_path)
            for encoding in detected_encodings:
                try:
                    txt = fd.read().decode(encoding.encoding)
                    break
                except UnicodeDecodeError:
                    continue
            return txt
    except Exception as e:
        logging.warning(f"读取 text 文件时出错: {e}")
        raise e

def extract_text(file_path):
    if file_path.lower().endswith('.pdf'):
        return extract_text_from_pdf(file_path)
    elif file_path.lower().endswith('.doc'):
        return extract_text_from_doc(file_path)
    elif file_path.lower().endswith('.docx'):
        content = extract_text_from_docx(file_path)
        if content == "":
            return extract_text_from_doc(file_path)
        return extract_text_from_docx(file_path)
    elif file_path.lower().endswith('.txt') or file_path.lower().endswith('.html') or file_path.lower().endswith('.md'):
        return extract_text_from_text(file_path)
    elif file_path.lower().endswith('.xlsx') or file_path.lower().endswith('.xls'):
        full_html, _ = html_text_from_excel(file_path)
        return full_html
    else:
        logging.warning("不支持的文件类型")
        raise Exception("不支持的文件类型")


if __name__ == "__main__":
    files = ['新丰县人民政府办公室规章制度.pdf', '政府采买规章制度.doc', '政府采购制度.docx']

    for file in files:
        text = extract_text(file)
        if text:
            print(f"{file} 文件内容:")
            print(text)
    
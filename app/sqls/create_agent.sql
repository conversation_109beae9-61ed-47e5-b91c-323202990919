CREATE TABLE agent (
    agent_id INT AUTO_INCREMENT PRIMARY KEY,
    agent_name VARCHAR(255) NOT NULL COMMENT 'agent名称',
    agent_desc TEXT COMMENT 'agent描述',
    agent_code TEXT NOT NULL COMMENT 'agent代码',
    user_id INT NOT NULL COMMENT 'agent创建者id',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'agent创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'agent更新时间',
    agent_icon_path VARCHAR(500) COMMENT 'agent图标路径，对应minio中的文件链接',
    extro_info JSON COMMENT '预留扩展字段',
    INDEX idx_user_id (user_id)
);
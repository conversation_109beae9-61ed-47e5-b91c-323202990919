CREATE TABLE IF NOT EXISTS `connect_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `db_type` varchar(50) NOT NULL COMMENT '数据库类型：mysql,postgresql,sqlite,oracle',
  `db_name` varchar(100) NOT NULL COMMENT '数据库名称',
  `db_path` varchar(500) DEFAULT NULL COMMENT '数据库路径(SQLite使用)',
  `db_host` varchar(100) DEFAULT NULL COMMENT '数据库主机地址',
  `db_port` int DEFAULT NULL COMMENT '数据库端口',
  `db_user` varchar(100) DEFAULT NULL COMMENT '数据库用户名',
  `db_pwd` varchar(500) DEFAULT NULL COMMENT '数据库密码(加密存储)',
  `comment` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `sys_code` varchar(100) DEFAULT NULL COMMENT '系统编码',
  -- 移除 user_name 字段
  `user_id` int NOT NULL COMMENT '创建用户ID',
  `gmt_created` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `ext_config` text COMMENT '扩展配置(JSON格式)',
  `external_id` varchar(100) DEFAULT NULL COMMENT '外部系统数据源ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_db_type` (`db_type`),
  KEY `idx_sys_code` (`sys_code`),
  UNIQUE KEY `idx_external_id` (`external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源配置表';
import config
from models import Base
from models.data_control import DataControl  # 导入你的模型
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 数据库连接 URI（根据你的实际配置修改）
user = config.DB_CONFIG['user']
password = config.DB_CONFIG['password']
host = config.DB_CONFIG['host']
port = config.DB_CONFIG['port']
db = config.DB_CONFIG['db']

DATABASE_URI = f"mysql+pymysql://{user}:{password}@{host}:{port}/{db}"

# 创建引擎
engine = create_engine(DATABASE_URI)

# 创建所有未存在的表
Base.metadata.create_all(bind=engine)

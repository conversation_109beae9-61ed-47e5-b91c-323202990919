CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(255) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_login DATETIME COMMENT '最后登录时间',
    permission_level ENUM('admin', 'employee', 'guest') DEFAULT 'employee' COMMENT '用户权限级别',
    INDEX idx_username (username),
    INDEX idx_permission_level (permission_level),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 新增字段
ALTER TABLE users
ADD COLUMN department VARCHAR(100) DEFAULT '待分配' COMMENT '用户部门' AFTER permission_level,
ADD COLUMN `group` VARCHAR(50) DEFAULT 'employee' COMMENT '用户所属权限组' AFTER department,
ADD COLUMN telphone VARCHAR(20) DEFAULT '' COMMENT '手机号码' AFTER `group`,
ADD COLUMN id_num VARCHAR(50) DEFAULT '' COMMENT '身份证号码' AFTER telphone,
ADD COLUMN nickname VARCHAR(100) DEFAULT '' COMMENT '昵称' AFTER id_num,
ADD COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER last_login,
ADD COLUMN attr_01 VARCHAR(255) DEFAULT '' COMMENT '预留字段' AFTER update_time,
ADD COLUMN attr_02 VARCHAR(255) DEFAULT '' COMMENT '预留字段' AFTER attr_01,
ADD COLUMN extro_info JSON DEFAULT NULL COMMENT '预留扩展字段' AFTER attr_02,
ADD INDEX idx_department (department),
ADD INDEX idx_group (`group`);

-- 回滚
-- 删除新增的索引
ALTER TABLE users
DROP INDEX idx_department,
DROP INDEX idx_group;

-- 删除新增的字段
ALTER TABLE users
DROP COLUMN extro_info,
DROP COLUMN attr_02,
DROP COLUMN attr_01,
DROP COLUMN update_time,
DROP COLUMN nickname,
DROP COLUMN id_num,
DROP COLUMN telphone,
DROP COLUMN `group`,
DROP COLUMN department;
CREATE DATABASE IF NOT EXISTS rag;

USE rag;

CREATE TABLE `users` (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `attr_01` varchar(255) DEFAULT '' COMMENT '预留字段',
  `attr_02` varchar(255) DEFAULT '' COMMENT '预留字段',
  `extro_info` json DEFAULT NULL COMMENT '预留扩展字段',
  `permission_level` enum('admin','employee','guest') NOT NULL DEFAULT 'employee',
  `department` varchar(100) DEFAULT '待分配' COMMENT '用户部门',
  `group` varchar(50) DEFAULT 'employee' COMMENT '用户所属权限组',
  `telphone` varchar(20) DEFAULT '' COMMENT '手机号码',
  `id_num` varchar(50) DEFAULT '' COMMENT '身份证号码',
  `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `username_2` (`username`),
  KEY `idx_department` (`department`),
  KEY `idx_group` (`group`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci; 

CREATE TABLE departments (
    department_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    department_name VARCHAR(100) NOT NULL UNIQUE DEFAULT '待分配' COMMENT '部门名称',
    department_phone VARCHAR(20) DEFAULT '' COMMENT '部门联系电话',
    department_email VARCHAR(100) DEFAULT '' COMMENT '部门邮箱',
    employee_count INT DEFAULT 0 COMMENT '部门员工数量',
    creator_id INT DEFAULT NULL COMMENT '部门创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    extro_info JSON DEFAULT NULL COMMENT '预留扩展字段',
    
    INDEX idx_department_name (department_name),
    INDEX idx_creator (creator_id),
    
    FOREIGN KEY (creator_id) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 添加用户表与部门表的外键关系
-- ALTER TABLE users
-- ADD CONSTRAINT fk_user_department FOREIGN KEY (department) REFERENCES departments(department_name) ON DELETE SET NULL ON UPDATE CASCADE;

CREATE TABLE documents (
    doc_id INT AUTO_INCREMENT PRIMARY KEY,
    path VARCHAR(255) NOT NULL,
    name VARCHAR(255) UNIQUE NOT NULL,
    markdown MEDIUMTEXT NOT NULL,
    version VARCHAR(20) NOT NULL,
    upload_time DATETIME NOT NULL,
    minio_config TEXT NOT NULL,
    permission_level ENUM('admin', 'employee', 'guest') NOT NULL DEFAULT 'employee',
    UNIQUE (name)
);

CREATE TABLE chunks (
    chunk_id INT AUTO_INCREMENT PRIMARY KEY,
    doc_id INT NOT NULL ,
    chunk_content TEXT NOT NULL,
    chunk_title TEXT,
    update_time DATETIME NOT NULL,
    dense_vector TEXT,
    sparse_vector TEXT,
    FOREIGN KEY (doc_id) REFERENCES documents(doc_id),
    INDEX (doc_id)
);

CREATE TABLE dialogs (
    dialog_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

CREATE TABLE dialog_messages (
    message_id INT AUTO_INCREMENT PRIMARY KEY,
    dialog_id INT NOT NULL,
    role ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,
    time DATETIME NOT NULL,
    refs TEXT,
    extra_info JSON COMMENT '预留扩展字段',
    FOREIGN KEY (dialog_id) REFERENCES dialogs(dialog_id),
    INDEX (dialog_id)
);

-- 新增 dialog_messages_tokens 表
CREATE TABLE IF NOT EXISTS dialog_messages_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    message_id INT NOT NULL COMMENT '关联的消息ID',
    dialog_id INT NOT NULL COMMENT '关联的对话ID',
    role ENUM('user', 'assistant', 'system') NOT NULL COMMENT '角色',
    user_id INT NOT NULL COMMENT '用户ID',
    input_tokens INT DEFAULT 0 COMMENT '输入tokens数, 针对user和system角色',
    output_tokens INT DEFAULT 0 COMMENT '输出tokens数, 针对assistant角色',
    is_deleted INT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    INDEX idx_message_id (message_id),
    INDEX idx_dialog_id (dialog_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话消息Tokens记录表';

CREATE TABLE phrases (
    phrase_id INT AUTO_INCREMENT PRIMARY KEY,
    content TEXT NOT NULL,
    create_time DATETIME NOT NULL,
    user_id INT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

CREATE TABLE IF NOT EXISTS aigc_articles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '创建人用户ID',
    doc_topic VARCHAR(255) NOT NULL COMMENT '文档主题',
    keywords VARCHAR(255) DEFAULT '' COMMENT '关键词',
    wcount INT DEFAULT 0 COMMENT '计划文章字数',
    outline TEXT COMMENT '文章大纲',
    abstract TEXT COMMENT '文章摘要',
    article_wcount INT DEFAULT 0 COMMENT '实际字数',
    file_size  INT DEFAULT 0 COMMENT '文件大小，为了之前的兼容性，实际等于article_wcount',
    input_tokens INT DEFAULT 0 COMMENT '输入tokens数',
    output_tokens INT DEFAULT 0 COMMENT '输出tokens数',
    file_name VARCHAR(255) COMMENT '文件名',
    file_type VARCHAR(255) COMMENT '文件类型',
    file_path VARCHAR(255) COMMENT '文件路径',
    io_bucket_name VARCHAR(255) COMMENT 'MinIO的Bucket名称',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted INT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_create_time (create_time),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成的文章信息表';

CREATE TABLE IF NOT EXISTS kb_share_relations (
    kb_id INT NOT NULL COMMENT '知识库id',
    from_user_id INT NOT NULL COMMENT '知识库所属用户id',
    to_user_id INT NOT NULL COMMENT '被分享的用户id',
    to_department VARCHAR(255) COMMENT '备用',
    attr_02 VARCHAR(255) COMMENT '备用',
    extro_info JSON COMMENT '预留扩展字段',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (kb_id, from_user_id, to_user_id),
    INDEX idx_kb_id (kb_id),
    INDEX idx_from_user_id (from_user_id),
    INDEX idx_to_user_id (to_user_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库分享关系表';

CREATE TABLE IF NOT EXISTS knowledge_bases (
    kb_id INT PRIMARY KEY AUTO_INCREMENT,
    kb_name VARCHAR(255) NOT NULL COMMENT '知识库名称',
    kb_tags VARCHAR(255) COMMENT '知识库标签，标签用,号分割',
    kb_description VARCHAR(1000) COMMENT '知识库描述',
    kb_parse_strategy VARCHAR(50) NOT NULL DEFAULT 'normal' COMMENT '知识库解析策略：normal-普通解析，structure-结构化解析',
    kb_type VARCHAR(16) NOT NULL DEFAULT 'normal' COMMENT '知识库类型：normal-普通，system-系统内置',
    user_id INT NOT NULL COMMENT '创建人用户ID',
    shared_to_user_id INT COMMENT '分享给用户ID',
    doc_ids JSON COMMENT '所属于该知识库的所有文档id列表',
    permission_group VARCHAR(50) DEFAULT 'employee' COMMENT '权限组：all/admin/employee/guest',
    permission_level VARCHAR(50) DEFAULT 'employee' COMMENT '访问权限：all/admin/employee/guest',
    is_deleted INT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '知识库创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '知识库更新时间',
    extro_info JSON COMMENT '预留扩展字段',
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库信息表';

CREATE TABLE rag_documents (
    doc_id INT AUTO_INCREMENT PRIMARY KEY,
    doc_name VARCHAR(255) NOT NULL COMMENT '文档名称',
    doc_type VARCHAR(50) NOT NULL COMMENT '文档文件类型(PDF, Word, TXT 等)',
    doc_status VARCHAR(20) NOT NULL COMMENT '文档生命周期状态(草稿, 已发布, 已归档等)',
    kb_id INT COMMENT '知识库 ID',
    user_id INT NOT NULL COMMENT '创建人用户 ID',
    parse_status INT DEFAULT 0 COMMENT '文档的解析状态',
    latest_version INT DEFAULT 1 COMMENT '是否为最新版本 (1 是最新，0 是旧版本)',
    doc_version INT DEFAULT 1 COMMENT '文档版本号',
    chunk_cnt INT DEFAULT 0 COMMENT '文档分块数量',
    doc_checksum VARCHAR(64) COMMENT '文档hash码',1
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0没有删除, 1是删除)',
    minio_doc_path VARCHAR(255) NOT NULL COMMENT 'MinIO 对象存储路径',
    minio_preview_doc_path VARCHAR(255) NOT NULL COMMENT 'MinIO 预览对象存储路径',
    minio_bucket_name VARCHAR(100) NOT NULL COMMENT 'MinIO 桶名称',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '文档上传时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '文档更新时间',
    extro_info JSON COMMENT '预留扩展字段',
    FOREIGN KEY (kb_id) REFERENCES knowledge_bases(kb_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_kb_id (kb_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户上传文档表';

insert into departments(`department_id`, `department_name`, `employee_count`)
VALUES (1, "system",1);

insert into users(`user_id`, `username`, `password_hash`, `permission_level`, `department`, `group`) 
VALUES (1, "admin", "$2b$12$JzlLX4em2BOm5MZ147RUG.tRnqpMLG/0CLcavRzeVwDrsLTEf2fg6", "admin", "system", "admin");

insert into knowledge_bases(`kb_id`, `kb_name`, `kb_tags`, `kb_description`, `kb_parse_strategy`, `kb_type`, `user_id`) 
VALUES (1, "规章制度", "规章制度", "系统内置知识库，存储规章制度相关文档，用以作为智能助手的rag数据源", "rules", "system", 1);

-- insert into knowledge_bases(`kb_id`, `kb_name`, `kb_tags`, `kb_description`, `kb_parse_strategy`, `kb_type`, `user_id`) 
-- VALUES (2, "论文", "论文", "系统内置知识库，存储论文，用以作为智能助手的rag数据源", "rules", "system", 1);

CREATE TABLE IF NOT EXISTS article_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    keywords TEXT  COMMENT '关键词',
    abstract  TEXT COMMENT '摘要',
    keysentences TEXT COMMENT '关键句子',
    keydata TEXT COMMENT '关键数据',
    article_wcount INT DEFAULT 0 COMMENT '实际字数',
    input_tokens INT DEFAULT 0 COMMENT '输入tokens数',
    output_tokens INT DEFAULT 0 COMMENT '输出tokens数',
    file_name VARCHAR(255) COMMENT '文件名',
    file_type VARCHAR(255) COMMENT '文件类型',
    file_path VARCHAR(255) COMMENT '文件路径',
    io_bucket_name VARCHAR(255) COMMENT 'MinIO的Bucket名称',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted INT DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_create_time (create_time),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章摘要信息表';

-- 创建文章模板表
CREATE TABLE IF NOT EXISTS `article_template` (
  `template_id` INT NOT NULL AUTO_INCREMENT COMMENT '文章模板唯一ID',
  `template_name` VARCHAR(255) COMMENT '文章模板名称',
  `template_content` TEXT COMMENT '模板内容',
  `template_type` VARCHAR(255) COMMENT '模型类型，比如公文、简报等',
  `permission_level` VARCHAR(50) DEFAULT 'employee' COMMENT '访问权限：admin/employee/guest',
  `is_manual` INT DEFAULT 1 COMMENT '是否为手工编辑，1为手工编辑，0为自动生成',
  `is_deleted` INT DEFAULT 0 COMMENT '是否删除',
  `user_id` INT COMMENT '创建人用户ID',
  `input_tokens` INT DEFAULT 0 COMMENT '输入tokens总数',
  `output_tokens` INT DEFAULT 0 COMMENT '输出tokens总数',
  `check_sum` VARCHAR(255) COMMENT '自动生成模板的校验码',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '文档上传时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '文档更新时间',
  `extro_info` JSON COMMENT '预留扩展字段',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章模板生成';

-- 创建故障记录表
CREATE TABLE fault_records (
    fault_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '故障记录主键 ID',
    agent_id INT NOT NULL COMMENT '关联的智能体 ID',
    user_id INT NOT NULL COMMENT '上传的用户 ID',
    fault_type VARCHAR(255) NOT NULL COMMENT '故障类型',
    fault_reason TEXT NOT NULL COMMENT '故障原因',
    fault_content TEXT NOT NULL COMMENT '故障描述内容',
    fault_severity VARCHAR(255) NOT NULL COMMENT '故障严重等级',
    fault_time DATETIME DEFAULT NULL COMMENT '故障发生时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    CONSTRAINT fk_fault_agent FOREIGN KEY (agent_id) REFERENCES agent(agent_id),
    CONSTRAINT fk_fault_user FOREIGN KEY (user_id) REFERENCES users(user_id),
    
    INDEX idx_agent_id (agent_id),
    INDEX idx_user_id (user_id),
    INDEX idx_fault_time (fault_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户上传的故障记录表';

-- 创建 agent 表
CREATE TABLE agent (
    agent_id INT AUTO_INCREMENT PRIMARY KEY,
    agent_name VARCHAR(255) NOT NULL COMMENT 'agent名称',
    agent_desc TEXT COMMENT 'agent描述',
    agent_code TEXT NOT NULL COMMENT 'agent代码',
    user_id INT NOT NULL COMMENT 'agent创建者id',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'agent创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'agent更新时间',
    agent_icon_path TEXT COMMENT 'agent图标路径，对应minio中的文件链接',
    extro_info JSON COMMENT '预留扩展字段',
    INDEX idx_user_id (user_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='agent表';

-- 创建 agent_file_contents 表
CREATE TABLE agent_file_contents (
    agent_file_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'agent文件主键',
    agent_id INT NOT NULL COMMENT '关联的agent ID',
    user_id INT NOT NULL COMMENT '用户ID',
    file_contents TEXT NOT NULL COMMENT 'agent文件内容',
    parser_status INT NOT NULL DEFAULT 0 COMMENT 'agent文件解析状态，0-未解析，1-解析中，2-解析完成',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'agent创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'agent更新时间',
    extro_info JSON COMMENT '预留扩展字段',
    
    CONSTRAINT fk_agent_file_agent FOREIGN KEY (agent_id) REFERENCES agent(agent_id),
    CONSTRAINT fk_agent_file_user FOREIGN KEY (user_id) REFERENCES users(user_id),
    
    INDEX idx_agent_id (agent_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parser_status (parser_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='agent文件内容表';
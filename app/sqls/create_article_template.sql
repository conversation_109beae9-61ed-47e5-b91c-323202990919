-- 创建文章模板表
CREATE TABLE IF NOT EXISTS `article_template` (
  `template_id` INT NOT NULL AUTO_INCREMENT COMMENT '文章模板唯一ID',
  `template_name` VARCHAR(255) COMMENT '文章模板名称',
  `template_content` TEXT COMMENT '模板内容',
  `template_type` VARCHAR(255) COMMENT '模型类型: 讲话稿、工作报告、调研报告、工作总结、自由写作，其他',
  `permission_level` VARCHAR(50) DEFAULT 'private' COMMENT '访问权限：system/private/public',
  `is_manual` INT DEFAULT 1 COMMENT '是否为手工编辑，1为手工编辑，0为自动生成',
  `is_deleted` INT DEFAULT 0 COMMENT '是否删除',
  `user_id` INT COMMENT '创建人用户ID',
  `input_tokens` INT DEFAULT 0 COMMENT '输入tokens总数',
  `output_tokens` INT DEFAULT 0 COMMENT '输出tokens总数',
  `check_sum` VARCHAR(255) COMMENT '自动生成模板的校验码',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '文档上传时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '文档更新时间',
  `extro_info` JSON COMMENT '预留扩展字段',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章模板生成';
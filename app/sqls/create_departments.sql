CREATE TABLE departments (
    department_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    department_name VARCHAR(100) NOT NULL UNIQUE DEFAULT '待分配' COMMENT '部门名称',
    department_phone VARCHAR(20) DEFAULT '' COMMENT '部门联系电话',
    department_email VARCHAR(100) DEFAULT '' COMMENT '部门邮箱',
    employee_count INT DEFAULT 0 COMMENT '部门员工数量',
    creator_id INT DEFAULT NULL COMMENT '部门创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    extro_info JSON DEFAULT NULL COMMENT '预留扩展字段',
    
    INDEX idx_department_name (department_name),
    INDEX idx_creator (creator_id),
    
    FOREIGN KEY (creator_id) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 修改用户表的department字段，添加外键约束
ALTER TABLE users
MODIFY COLUMN department VARCHAR(100) DEFAULT '待分配' COMMENT '用户部门',
ADD CONSTRAINT fk_user_department FOREIGN KEY (department) REFERENCES departments(department_name) ON DELETE SET NULL ON UPDATE CASCADE;

-- 回滚脚本
-- 删除用户表外键约束
ALTER TABLE users
DROP FOREIGN KEY fk_user_department;

-- 删除部门表
DROP TABLE IF EXISTS departments;
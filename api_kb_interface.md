# 知识库管理 API 接口文档

## 基础说明
- 基础路径: `/api/v1/kb`
- 请求格式: JSON
- 响应格式: 
```json
{
    "code": 200,
    "message": "success",
    "data": {}
}
```


## API 列表

### 1. 创建知识库
- 接口: `POST /kb_create`
- 功能: 创建新的知识库
- 请求体:
```json
{
    "kb_name": "测试知识库",                // 必填，知识库名称
    "kb_parse_strategy": "normal",         // 可选，解析策略，默认为 "normal"
    "kb_auth_level": "private",           // 可选，权限级别，默认为 "private"
    "kb_tag": "测试,示例",                 // 可选，标签，多个标签用逗号分隔
    "kb_desc": "这是一个测试知识库",         // 可选，知识库描述
    "emb_model" "bge-m3",                 // 可选，知识库嵌入模型
    "rerank_model" "bge-reranker-v2-m3"   // 可选，知识库嵌入模型
}
```
- 响应体:
```json
{
    "code": 200,
    "message": "知识库创建成功",
    "data": {
        "kb_id": 1,
        "create_time": "2024-01-20T10:00:00Z"
    }
}
```

### 2. 获取知识库信息
- 接口: `GET /kb_info`
- 功能: 获取指定知识库的详细信息
- 请求参数:
  - kb_id: 知识库ID（必填）
- 响应体:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "kb_id": 1,
        "kb_name": "测试知识库",
        "kb_parse_strategy": "normal",
        "kb_auth_level": "private",
        "kb_tag": "测试,示例",
        "kb_description": "这是一个测试知识库",
        "create_time": "2024-01-20T10:00:00Z",
        "emb_model" "bge-m3",
        "rerank_model" "bge-reranker-v2-m3",
        "search_config": {
            "topk": 10,
            "search_type": "vector",
            "score_threshold": 0.5,
            "token_max": 10000
        }
}
```

### 3. 获取知识库列表
- 接口: `GET /kb_list`
- 功能: 获取用户的知识库列表
- 请求参数:
  - pageNo: 页码（可选，默认1）
  - pageSize: 每页数量（可选，默认10）
- 响应体:
```json
{
    "code": 200,
    "message": "success",
    "data": {
<<<<<<< HEAD
        "list": [
            {
                "kb_id": 1,
                "kb_name": "知识库1",
                "kb_parse_strategy": "normal",
                "kb_auth_level": "private",
                "kb_tags": ["测试"],
                "kb_description": "描述1",
                "create_time": "2024-01-20T10:00:00Z"
            },
            {
                "kb_id": 2,
                "kb_name": "知识库2",
                "kb_parse_strategy": "normal",
                "kb_auth_level": "private",
                "kb_tags": ["示例"],
                "kb_description": "描述2",
                "create_time": "2024-01-20T11:00:00Z"
            }
        ],
=======
        "kb_id_list": [1, 2],
        "kb_name_list": ["知识库1", "知识库2"],
        "kb_parse_strategy_list": ["normal", "normal"],
        "kb_auth_level_list": ["private", "private"],
        "kb_tag_list": [["测试"], ["示例"]],
        "kb_description_list": ["描述1", "描述2"],
        "create_time_list": [
            "2024-01-20T10:00:00Z",
            "2024-01-20T11:00:00Z"
        ],
        "pageNo": 1,
        "pageSize": 10,
>>>>>>> 4133856c47f4cf2ab5ee058e9bfa4fa319371990
        "total": 2
    }
}
```

### 4. 编辑知识库
- 接口: `POST /kb_edit`
- 功能: 修改知识库信息
- 请求体:
```json
{
    "kb_id": 1,                          // 必填，知识库ID
    "kb_name": "新知识库名称",            // 可选，知识库名称
    "kb_auth_level": "private",         // 可选，权限级别
    "kb_tag": "新标签1,新标签2",         // 可选，标签
    "kb_desc": "新的知识库描述",          // 可选，描述
    "search_config": {                // 可选，搜索配置
        "topk": 10,
        "search_type": "vector",
        "score_threshold": 0.5,
        "token_max": 10000
    }
}
```
- 响应体:
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "kb_id": 1,
        "update_time": "2024-01-20T15:00:00Z"
    }
}
```

### 5. 删除知识库
- 接口: `POST /kb_del`
- 功能: 删除指定的知识库
- 请求体:
```json
{
    "kb_id": 1    // 必填，知识库ID
}
```
- 响应体:
```json
{
    "code": 200,
    "message": "删除成功",
    "data": {
        "kb_id": 1
    }
}
```

## 错误码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 参数错误 |
| 403 | 无权访问 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 注意事项
1. 所有接口需要登录授权
2. 知识库操作需要验证用户权限
3. 时间格式统一使用 ISO 8601 格式
4. 标签字符串使用英文逗号分隔

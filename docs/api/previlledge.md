权限相关接口
1、， 
## 1. 获取部门树

### 接口描述
从基础平台获取部门树信息。

### 请求方式
- GET `/api/v1/department/alllist`

### 请求头
| 参数名 | 必选 | 类型 | 说明 |
|--------|-----|------|------|
|  |  |  | |

### 请求参数
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
|  |  |  | |

### 响应参数
| 参数名         | 类型      | 说明   |
|-------------|---------|------|
| code        | integer | 状态码  |
| message     | string  | 消息   |
| data        | json    | 部门信息 |

### 响应示例
本地调试：
#### 实际token: 
    从http://*************:30000/#/portal 的智能工作台点击进去，再通过查看cookie ssotoken即可获得。
#### 修改前端工程的/testsso.html文件: 
    将token换成105上的实际token
    <script>
      window.location = "/?token=35c2668c91194ba9a9854e070a437778"
    </script>
    
#### 刷新页面：http://localhost:5173/testsso.html
#### 访问接口：http://localhost:5173/chat_api/department/alllist

```json
{
    "code": 200,
    "message": "获取问答统计成功",
    "data":{
          'name': 'dept', 
          'nodes': [{
            'id': 201889451933697,
            'parentId': 0,
            'name': '数据治理',
            'sort': 0,
            'childs': [],
            'nodeType': 'normal',
            'data': {
                'createTime': '2025-03-31 13:54:35',
                'updateTime': '2025-03-31 13:54:35',
                'creatorId': 195475493035009,
                'updaterId': 195475493035009,
                'updaterName': None,
                'deleteFlag': 0,
                'id': 201889451933697,
                'name': '数据治理',
                'code': 'DPSJZL01',
                'deptType': 'outside',
                'parentId': 0,
                'sort': 0,
                'status': 1,
                'description': '数据治理测试'
            }
        },
        {
            'id': 195470087171585,
            'parentId': 0,
            'name': '评估中心（军内）',
            'sort': 1,
            'childs': [{
                'id': 195470940217345,
                'parentId': 195470087171585,
                'name': '三站',
                'sort': 1,
                'childs': [],
                'nodeType': 'normal',
                'data': {
                    'createTime': '2024-11-06 11:38:39',
                    'updateTime': '2025-01-07 14:46:49',
                    'creatorId': 195475493035009,
                    'updaterId': 195475493035009,
                    'updaterName': None,
                    'deleteFlag': 0,
                    'id': 195470940217345,
                    'name': '三站',
                    'code': 'DP0103',
                    'deptType': 'inside',
                    'parentId': 195470087171585,
                    'sort': 1,
                    'status': 1,
                    'description': ''
                }
            }, {
                'id': 195568552221185,
                'parentId': 195470087171585,
                'name': '二站',
                'sort': 4,
                'childs': [],
                'nodeType': 'normal',
                'data': {
                    'createTime': '2024-11-08 16:36:09',
                    'updateTime': '2025-01-07 14:46:32',
                    'creatorId': 195475493035009,
                    'updaterId': 195475493035009,
                    'updaterName': None,
                    'deleteFlag': 0,
                    'id': 195568552221185,
                    'name': '二站',
                    'code': 'DP0102',
                    'deptType': 'inside',
                    'parentId': 195470087171585,
                    'sort': 4,
                    'status': 1,
                    'description': ''
                }
            }, {
                'id': 195779096097793,
                'parentId': 195470087171585,
                'name': '一站',
                'sort': 5,
                'childs': [],
                'nodeType': 'normal',
                'data': {
                    'createTime': '2024-11-13 10:49:48',
                    'updateTime': '2025-01-07 14:46:09',
                    'creatorId': 195738883292673,
                    'updaterId': 195475493035009,
                    'updaterName': None,
                    'deleteFlag': 0,
                    'id': 195779096097793,
                    'name': '一站',
                    'code': 'DP0101',
                    'deptType': 'inside',
                    'parentId': 195470087171585,
                    'sort': 5,
                    'status': 1,
                    'description': ''
                }
            }],
            'nodeType': 'normal',
            'data': {
                'createTime': '2024-11-06 11:10:55',
                'updateTime': '2025-01-07 14:45:28',
                'creatorId': 195475493035009,
                'updaterId': 195475493035009,
                'updaterName': None,
                'deleteFlag': 0,
                'id': 195470087171585,
                'name': '评估中心（军内）',
                'code': 'DP01',
                'deptType': 'inside',
                'parentId': 0,
                'sort': 1,
                'status': 1,
                'description': ''
            }
        }, {
            'id': 195471007694849,
            'parentId': 0,
            'name': '601(地方)',
            'sort': 2,
            'childs': [],
            'nodeType': 'normal',
            'data': {
                'createTime': '2024-11-06 11:40:51',
                'updateTime': '2025-01-07 14:47:34',
                'creatorId': 195475493035009,
                'updaterId': 195475493035009,
                'updaterName': None,
                'deleteFlag': 0,
                'id': 195471007694849,
                'name': '601(地方)',
                'code': 'DP601',
                'deptType': 'outside',
                'parentId': 0,
                'sort': 2,
                'status': 1,
                'description': ''
            }
        }, {
            'id': 198219495091201,
            'parentId': 0,
            'name': '603(地方)',
            'sort': 3,
            'childs': [],
            'nodeType': 'normal',
            'data': {
                'createTime': '2025-01-07 14:49:50',
                'updateTime': '2025-01-07 14:49:50',
                'creatorId': 195475493035009,
                'updaterId': 195475493035009,
                'updaterName': None,
                'deleteFlag': 0,
                'id': 198219495091201,
                'name': '603(地方)',
                'code': 'DP603',
                'deptType': 'outside',
                'parentId': 0,
                'sort': 3,
                'status': 1,
                'description': ''
            }
        }, {
            'id': 199853686328833,
            'parentId': 0,
            'name': '边缘计算',
            'sort': 4,
            'childs': [],
            'nodeType': 'normal',
            'data': {
                'createTime': '2025-02-13 13:26:12',
                'updateTime': '2025-02-13 13:26:12',
                'creatorId': 195475493035009,
                'updaterId': 195475493035009,
                'updaterName': None,
                'deleteFlag': 0,
                'id': 199853686328833,
                'name': '边缘计算',
                'code': 'DPBYJS',
                'deptType': 'outside',
                'parentId': 0,
                'sort': 4,
                'status': 1,
                'description': ''
            }
        }, {
            'id': 200201701734913,
            'parentId': 0,
            'name': '机器学习',
            'sort': 5,
            'childs': [],
            'nodeType': 'normal',
            'data': {
                'createTime': '2025-02-21 10:14:50',
                'updateTime': '2025-02-21 10:15:01',
                'creatorId': 195475493035009,
                'updaterId': 195475493035009,
                'updaterName': None,
                'deleteFlag': 0,
                'id': 200201701734913,
                'name': '机器学习',
                'code': 'DPJQXX',
                'deptType': 'outside',
                'parentId': 0,
                'sort': 5,
                'status': 1,
                'description': ''
             }
          }
      ]
    }
}
```

## 2. 数据权限-新增

### 接口描述
创建数据权限信息。

### 请求方式
- POST /data-control/create

> Body 请求参数

```json
{
  "data_type": "知识库",
  "data_id": 1,
  "dt_type": "所在部门",
  "dt_dept_code": "001",
  "dt_dept_code_list": "string",
  "extro_info": {}
}
```

### 请求参数

|名称|位置|类型|必选| 中文名                           | 说明                   |
|---|---|---|---|-------------------------------|----------------------|
|access_token|cookie|string| 否 | 访问口令                          |                      |
|username|cookie|string| 否 | 当前登录用户名                       |                      |
|body|body|object| 否 | 数据权限对象                        |                      |
|» data_type|body|string| 是 | 数据资源类                | 知识库、应用
|» data_id|body|integer| 是 | 数据资源id                        | 
|» dt_type|body|string| 是 | 数据访问控制类型         | 公开、私有、所在部门、指定部门
|» dt_dept_code|body|string| 是 | 数据访问控制部门编码                    | dt_type为所在部门时，此处填写用户当前部门编码，从cookie读取：userdept_code
|» dt_dept_code_list|body|string| 是 | 数据访问控制指定部门编码列表| dt_type为指定部门时，逗号打头间隔，如：,001,002 
|» extro_info|body|string| 否 | 预留扩展字段                        |


> 返回示例

> 200 Response

```json
{
    "code": 200,
    "message": "创建数据权限成功",
    "data": {
        "dt_id": 1,
        "data_type": "amet aute ullamco",
        "data_id": 1,
        "dt_type": "知识库",
        "dt_dept_code": "001",
        "dt_dept_code_list": "",
        "create_time": "2025-02-06T10:16:08",
        "update_time": "2024-07-23T00:00:00",
        "owner_id": 1,
        "extro_info": {}
    }
}
```

## 3. 数据权限读取

GET /data-control/get

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|access_token|cookie|string| 否 ||none|
|username|cookie|string| 否 ||none|
|dt_id|query|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
    "code": 200,
    "message": "查询数据权限成功",
    "data": {
        "dt_id": 1,
        "data_type": "知识库",
        "data_id": 1,
        "dt_type": "所在部门",
        "dt_dept_code": "001",
        "dt_dept_code_list": "",
        "create_time": "2025-02-06T10:16:08",
        "update_time": "2024-06-23T10:16:08",
        "owner_id": 1,
        "extro_info": ""
    }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|--|---|---|
|200|OK|none|Inline|



## 4. 数据权限更新

POST /data-control/update

> Body 请求参数

```json
{
  "dt_id": 1,
  "data_type": "应用",
  "data_id": 1,
  "dt_type": "私有",
  "dt_dept_code": "",
  "dt_dept_code_list": "",
  "extro_info": null
}
```

### 请求参数

|名称|位置|类型|必选| 中文名                          | 说明                   |
|---|---|---|---|------------------------------|----------------------|
|access_token|cookie|string| 否 | 访问口令                         |                      |
|username|cookie|string| 否 | 当前登录用户名                      |                      |
|body|body|object| 否 | 数据权限对象                       |                      |
|» dt_id|body|integer| 是 |ID|
|» data_type|body|string| 是 | 数据资源类               | 知识库、应用
|» data_id|body|integer| 是 | 数据资源id                       | 
|» dt_type|body|string| 是 | 数据访问控制类型        | 公开、私有、所在部门、指定部门
|» dt_dept_code|body|string| 是 | 数据访问控制部门编码                   |
|» dt_dept_code_list|body|string| 是 | 数据访问控制指定部门编码列表| 逗号打头间隔，如：,001,002 
|» extro_info|body|string| 否 | 预留扩展字段                       |

> 返回示例

> 200 Response

```json
{
    "code": 200,
    "message": "更改数据权限成功",
    "data": {}
}
```
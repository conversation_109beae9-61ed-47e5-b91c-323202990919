# 统计信息接口文档

## 1. 问答统计接口

### 接口描述
获取系统中问答、文档写作和阅读总结的统计信息。

### 请求方式
- GET `/api/v1/stats/qa`

### 请求头
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Authorization | 是 | string | Bearer Token |

### 请求参数
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| start_tm | 否 | string | 开始时间，格式：YYYY-MM-DD HH:mm:ss |
| end_tm | 否 | string | 结束时间，格式：YYYY-MM-DD HH:mm:ss |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| qa_count | integer | 问答对话次数 |
| write_doc_count | integer | 文档写作次数 |
| read_summary_count | integer | 阅读总结次数 |
| total_count | integer | 总使用次数 |
| start_time | string | 查询开始时间 |
| end_time | string | 查询结束时间 |

### 响应示例
```json
{
    "code": 200,
    "message": "获取问答统计成功",
    "data": {
        "qa_count": 100,
        "write_doc_count": 50,
        "read_summary_count": 30,
        "total_count": 180
    }
}
```

### 请求示例
```http
# 不带时间范围
GET /api/v1/stats/qa
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 带时间范围
GET /api/v1/stats/qa?start_tm=2025-04-01%2000:00:00&end_tm=2025-04-20%2023:59:59
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 2. Tokens统计接口

### 接口描述
获取系统中文章生成和文章总结的 tokens 使用统计，包括输入和输出的 tokens 总量。

### 请求方式
- GET `/api/v1/stats/tokens`

### 请求头
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Authorization | 是 | string | Bearer Token |

### 请求参数
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| start_tm | 否 | string | 开始时间，格式：YYYY-MM-DD HH:mm:ss |
| end_tm | 否 | string | 结束时间，格式：YYYY-MM-DD HH:mm:ss |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| input_tokens_count | integer | 输入tokens总数 |
| output_tokens_count | integer | 输出tokens总数 |
| all_tokens_count | integer | tokens总使用量 |
| start_time | string | 查询开始时间 |
| end_time | string | 查询结束时间 |

### 响应示例
```json
{
    "code": 200,
    "message": "获取Tokens统计成功",
    "data": {
        "input_tokens_count": 50000,
        "output_tokens_count": 30000,
        "all_tokens_count": 80000
    }
}
```

### 请求示例
```http
# 不带时间范围
GET /api/v1/stats/tokens
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 带时间范围
GET /api/v1/stats/tokens?start_tm=2025-04-01%2000:00:00&end_tm=2025-04-20%2023:59:59
Authorization: Bearer ...
```


## 3. 知识库统计接口

### 接口描述
获取系统中知识库及其文档数量的统计信息，支持按时间范围过滤统计。

### 请求方式
- GET `/api/v1/stats/kbs`

### 请求头
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Authorization | 是 | string | Bearer Token |

### 请求参数
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| start_tm | 否 | string | 开始时间，格式：YYYY-MM-DD HH:mm:ss |
| end_tm | 否 | string | 结束时间，格式：YYYY-MM-DD HH:mm:ss |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| kb_count | integer | 知识库数量 |
| kb_doc_count | integer | 知识库文档总数 |
| start_time | string | 查询开始时间 |
| end_time | string | 查询结束时间 |

### 响应示例
```json
{
    "code": 200,
    "message": "获取知识库统计成功",
    "data": {
        "kb_count": 2,
        "kb_doc_count": 1000,
        "start_time": "2025-04-01 00:00:00",
        "end_time": "2025-04-20 23:59:59"
    }
}
```

### 请求示例
```http
# 不带时间范围
GET /api/v1/stats/kbs
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 带时间范围
GET /api/v1/stats/kbs?start_tm=2025-04-01%2000:00:00&end_tm=2025-04-20%2023:59:59
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 4. 模型统计接口

### 接口描述
获取系统中当前配置的可用模型数量统计信息。

### 请求方式
- GET `/api/v1/stats/models`

### 请求头
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Authorization | 是 | string | Bearer Token |

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| model_count | integer | 可用模型数量 |

### 响应示例
```json
{
    "code": 200,
    "message": "获取模型统计成功",
    "data": {
        "model_count": 3
    }
}
```


### 请求示例
```http
GET /api/v1/stats/models
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

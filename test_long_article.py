#!/usr/bin/env python3
"""
长文章生成功能测试脚本
"""

import asyncio
import aiohttp
import json
import time

# 测试配置
BASE_URL = "http://localhost:9989"  # 根据实际配置调整
API_TOKEN = "your_test_token"  # 需要替换为实际的测试token

async def test_long_article_generation():
    """测试长文章生成功能"""
    
    # 测试数据
    test_data = {
        "doc_topic": "人工智能在现代教育中的应用与发展趋势",
        "outline": {
            "primary": [
                {
                    "title": "人工智能教育概述",
                    "secondary": [
                        {"title": "1.1 人工智能教育的定义与内涵"},
                        {"title": "1.2 人工智能教育的发展历程"}
                    ]
                },
                {
                    "title": "人工智能在教育中的具体应用",
                    "secondary": [
                        {"title": "2.1 智能教学系统"},
                        {"title": "2.2 个性化学习推荐"},
                        {"title": "2.3 智能评估与反馈"}
                    ]
                },
                {
                    "title": "人工智能教育的发展趋势",
                    "secondary": [
                        {"title": "3.1 技术发展趋势"},
                        {"title": "3.2 应用场景扩展"},
                        {"title": "3.3 挑战与机遇"}
                    ]
                },
                {
                    "title": "案例分析与实践经验",
                    "secondary": [
                        {"title": "4.1 国内外典型案例"},
                        {"title": "4.2 实施经验总结"}
                    ]
                },
                {
                    "title": "未来展望与建议",
                    "secondary": [
                        {"title": "5.1 发展前景预测"},
                        {"title": "5.2 政策建议与实施路径"}
                    ]
                }
            ]
        },
        "article_id": 1,  # 需要先创建大纲获取真实的article_id
        "length": 15000,  # 目标1.5万字
        "keywords": ["人工智能", "教育技术", "个性化学习", "智能教学"],
        "file_objects": []
    }
    
    print("开始测试长文章生成功能...")
    print(f"目标主题: {test_data['doc_topic']}")
    print(f"目标字数: {test_data['length']}")
    print(f"章节数量: {len(test_data['outline']['primary'])}")
    
    # 首先生成大纲获取article_id
    outline_data = {
        "doc_topic": test_data["doc_topic"],
        "keywords": test_data["keywords"],
        "file_objects": []
    }
    
    async with aiohttp.ClientSession() as session:
        # 1. 生成大纲
        print("\n1. 生成文章大纲...")
        try:
            async with session.post(
                f"{BASE_URL}/api/v1/generate/outline",
                json=outline_data,
                headers={"Authorization": f"Bearer {API_TOKEN}"}
            ) as response:
                if response.status == 200:
                    outline_result = await response.json()
                    article_id = outline_result.get("data", {}).get("article_id")
                    if article_id:
                        test_data["article_id"] = article_id
                        print(f"大纲生成成功，article_id: {article_id}")
                    else:
                        print("大纲生成失败，使用默认article_id")
                else:
                    print(f"大纲生成请求失败: {response.status}")
        except Exception as e:
            print(f"大纲生成异常: {str(e)}")
        
        # 2. 生成长文章
        print("\n2. 开始生成长文章...")
        start_time = time.time()
        
        try:
            async with session.post(
                f"{BASE_URL}/api/v1/generate/article/long_stream",
                json=test_data,
                headers={
                    "Authorization": f"Bearer {API_TOKEN}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status == 200:
                    print("开始接收流式响应...")
                    
                    sections_completed = 0
                    total_content = ""
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if not line:
                            continue
                            
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                status = data.get('status')
                                
                                if status == 'started':
                                    print(f"✓ {data.get('message')}")
                                elif status == 'sections_parsed':
                                    print(f"✓ 解析出 {data.get('sections')} 个章节")
                                elif status == 'generating_section':
                                    print(f"⏳ 正在生成第 {data.get('section_index')} 章节: {data.get('section_title')}")
                                elif status == 'section_completed':
                                    sections_completed += 1
                                    section_length = data.get('length', 0)
                                    print(f"✅ 第 {data.get('section_index')} 章节完成 ({section_length} 字)")
                                    total_content += data.get('content', '')
                                elif status == 'section_failed':
                                    print(f"❌ 第 {data.get('section_index')} 章节生成失败: {data.get('error')}")
                                elif status == 'generation_completed':
                                    print(f"🎉 文章生成完成！")
                                    print(f"   总字数: {data.get('total_length')}")
                                    print(f"   完成章节: {data.get('sections_completed')}")
                                elif status == 'error':
                                    print(f"❌ 生成错误: {data.get('error')}")
                                    
                            except json.JSONDecodeError:
                                continue
                                
                        elif line.startswith('final: '):
                            try:
                                final_data = json.loads(line[7:])
                                if final_data.get('code') == 200:
                                    article_data = final_data.get('data', {})
                                    print(f"\n📊 最终统计:")
                                    print(f"   文章ID: {article_data.get('article_id')}")
                                    print(f"   总字数: {article_data.get('total_length')}")
                                    print(f"   章节数: {article_data.get('sections_count')}")
                                    print(f"   输入tokens: {article_data.get('input_tokens')}")
                                    print(f"   输出tokens: {article_data.get('output_tokens')}")
                                    
                                    # 保存生成的文章到文件
                                    article_content = article_data.get('article', '')
                                    if article_content:
                                        filename = f"generated_article_{int(time.time())}.md"
                                        with open(filename, 'w', encoding='utf-8') as f:
                                            f.write(article_content)
                                        print(f"   文章已保存到: {filename}")
                                else:
                                    print(f"❌ 最终响应错误: {final_data.get('message')}")
                            except json.JSONDecodeError:
                                print("❌ 最终响应解析失败")
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        end_time = time.time()
        print(f"\n⏱️  总耗时: {end_time - start_time:.2f} 秒")

async def test_simple_case():
    """测试简单案例"""
    print("=" * 60)
    print("测试简单案例")
    print("=" * 60)
    
    simple_data = {
        "doc_topic": "Python编程入门指南",
        "outline": {
            "primary": [
                {"title": "Python基础语法"},
                {"title": "数据类型与变量"},
                {"title": "控制流程"}
            ]
        },
        "article_id": 999,  # 测试用ID
        "length": 5000,
        "keywords": ["Python", "编程", "入门"],
        "file_objects": []
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{BASE_URL}/api/v1/generate/article/long_stream",
                json=simple_data,
                timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
            ) as response:
                print(f"响应状态: {response.status}")
                if response.status == 200:
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                print(f"状态: {data.get('status')} - {data.get('message', '')}")
                            except:
                                pass
                else:
                    error_text = await response.text()
                    print(f"错误: {error_text}")
        except Exception as e:
            print(f"异常: {str(e)}")

if __name__ == "__main__":
    print("长文章生成功能测试")
    print("=" * 60)
    
    # 运行测试
    asyncio.run(test_long_article_generation())
    
    # 运行简单测试
    # asyncio.run(test_simple_case())

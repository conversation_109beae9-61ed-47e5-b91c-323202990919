# 长文章生成 API 文档

## 概述

长文章生成功能支持生成数万字的高质量文章，通过分章节并发生成的方式，突破了传统单次调用的字数限制。

## 核心特性

- ✅ **支持数万字生成**：单篇文章可生成1-5万字
- ✅ **分章节生成**：智能拆分大纲，分章节并发生成
- ✅ **实时进度反馈**：流式输出，实时显示生成进度
- ✅ **错误重试机制**：自动重试失败的章节
- ✅ **内容质量保证**：每个章节独立生成，保证内容质量
- ✅ **灵活配置**：支持自定义章节长度和并发数

## API 接口

### 长文章流式生成

**接口地址：** `POST /api/v1/generate/article/long_stream`

**请求头：**
```
Content-Type: text/event-stream
Authorization: Bearer {your_token}
```

**请求参数：**
```json
{
    "doc_topic": "文章主题",
    "outline": {
        "primary": [
            {
                "title": "第一章标题",
                "secondary": [
                    {"title": "1.1 小节标题"},
                    {"title": "1.2 小节标题"}
                ]
            },
            {
                "title": "第二章标题",
                "secondary": [
                    {"title": "2.1 小节标题"}
                ]
            }
        ]
    },
    "article_id": 123,
    "length": 15000,
    "keywords": ["关键词1", "关键词2"],
    "file_objects": []
}
```

**参数说明：**
- `doc_topic` (必填): 文章主题
- `outline` (必填): 文章大纲，JSON格式
- `article_id` (必填): 文章ID，需要先调用大纲生成接口获取
- `length` (可选): 目标字数，默认10000字
- `keywords` (可选): 关键词数组
- `file_objects` (可选): 参考文档路径数组

## 响应格式

### 流式响应事件

#### 1. 开始生成
```json
{
    "status": "started",
    "message": "开始生成长文章..."
}
```

#### 2. 章节解析完成
```json
{
    "status": "sections_parsed",
    "sections": 5,
    "message": "已解析出5个章节"
}
```

#### 3. 章节生成中
```json
{
    "status": "generating_section",
    "section_index": 1,
    "section_title": "第一章标题",
    "message": "正在生成第1章节: 第一章标题"
}
```

#### 4. 章节生成完成
```json
{
    "status": "section_completed",
    "section_index": 1,
    "section_title": "第一章标题",
    "content": "章节内容...",
    "length": 2500
}
```

#### 5. 章节生成失败
```json
{
    "status": "section_failed",
    "section_index": 1,
    "section_title": "第一章标题",
    "error": "错误信息"
}
```

#### 6. 生成完成
```json
{
    "status": "generation_completed",
    "total_length": 15000,
    "sections_completed": 5,
    "message": "文章生成完成"
}
```

#### 7. 最终响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "article": "完整文章内容...",
        "article_id": 123,
        "total_length": 15000,
        "sections_count": 5,
        "input_tokens": 5000,
        "output_tokens": 12000,
        "storage": {
            "file_path": "generated_docs/article.md",
            "download_url": "https://..."
        }
    }
}
```

## 使用示例

### Python 示例

```python
import aiohttp
import json

async def generate_long_article():
    data = {
        "doc_topic": "人工智能发展趋势",
        "outline": {
            "primary": [
                {"title": "AI技术现状"},
                {"title": "发展趋势分析"},
                {"title": "未来展望"}
            ]
        },
        "article_id": 123,
        "length": 10000,
        "keywords": ["人工智能", "技术趋势"]
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            "http://localhost:9989/api/v1/generate/article/long_stream",
            json=data,
            headers={"Authorization": "Bearer your_token"}
        ) as response:
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    event_data = json.loads(line[6:])
                    print(f"状态: {event_data.get('status')}")
                elif line.startswith('final: '):
                    final_data = json.loads(line[7:])
                    print(f"生成完成: {final_data}")
```

### JavaScript 示例

```javascript
async function generateLongArticle() {
    const data = {
        doc_topic: "人工智能发展趋势",
        outline: {
            primary: [
                {title: "AI技术现状"},
                {title: "发展趋势分析"},
                {title: "未来展望"}
            ]
        },
        article_id: 123,
        length: 10000,
        keywords: ["人工智能", "技术趋势"]
    };
    
    const response = await fetch('/api/v1/generate/article/long_stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer your_token'
        },
        body: JSON.stringify(data)
    });
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const eventData = JSON.parse(line.substring(6));
                console.log('状态:', eventData.status);
            } else if (line.startsWith('final: ')) {
                const finalData = JSON.parse(line.substring(7));
                console.log('生成完成:', finalData);
            }
        }
    }
}
```

## 配置参数

### 系统配置

在 `LongArticleGenerator` 类中可以调整以下参数：

- `max_section_length`: 每个章节最大字数（默认3000字）
- `max_concurrent_sections`: 最大并发生成章节数（默认3个）
- `retry_attempts`: 重试次数（默认3次）

### 性能优化建议

1. **合理设置目标字数**：建议单篇文章控制在1-3万字
2. **优化大纲结构**：章节数量建议控制在3-8个
3. **并发控制**：根据服务器性能调整并发数
4. **超时设置**：客户端建议设置5-10分钟超时

## 错误处理

### 常见错误码

- `400`: 参数错误
- `401`: 认证失败
- `404`: 文章ID不存在
- `500`: 服务器内部错误

### 错误重试策略

系统内置指数退避重试机制：
- 第1次重试：等待2秒
- 第2次重试：等待4秒
- 第3次重试：等待8秒

## 注意事项

1. **Token消耗**：长文章生成会消耗大量tokens，请确保账户余额充足
2. **生成时间**：根据文章长度，生成时间可能需要3-10分钟
3. **网络稳定性**：建议在稳定的网络环境下使用
4. **内容质量**：生成的内容需要人工审核和编辑
5. **并发限制**：同一用户同时只能生成一篇长文章

### 登录系统获取管理员token
# @name adminLogin
POST http://**************:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

### 获取部门列表（所有用户都可以访问）
@token = {{adminLogin.response.body.data.access_token}}
GET http://**************:9901/api/v1/department/list
Authorization: Bearer {{token}}

### 管理员创建部门
@adminToken = {{adminLogin.response.body.data.access_token}}
POST http://**************:9901/api/v1/department/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
    "department_name": "数据开发部",
    "department_phone": "010-12345678",
    "department_email": "<EMAIL>",
    "extro_info": {
        "location": "3楼",
        "description": "负责公司数据中台研发"
    }
}

### 管理员更新部门信息
@adminToken = {{adminLogin.response.body.data.access_token}}
POST http://**************:9901/api/v1/department/update
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
    "department_id": 1,
    "department_name": "技术研发部",
    "department_phone": "010-87654321",
    "department_email": "<EMAIL>",
    "extro_info": {
        "location": "4楼",
        "description": "负责公司核心技术研发"
    }
}

### 管理员删除部门
@adminToken = {{adminLogin.response.body.data.access_token}}
POST http://**************:9901/api/v1/department/del
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
    "department_id": 8
}

### 登录系统获取普通用户token
# @name normalUserLogin
POST http://**************:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "test003",
  "password": "test003"
}

### 获取部门列表（所有用户都可以访问）
@normalToken = {{normalUserLogin.response.body.data.access_token}}
GET http://**************:9901/api/v1/department/list
Authorization: Bearer {{normalToken}}

### 普通用户尝试创建部门（预期失败）
@normalToken = {{normalUserLogin.response.body.data.access_token}}
POST http://**************:9901/api/v1/department/create
Content-Type: application/json
Authorization: Bearer {{normalToken}}

{
    "department_name": "市场部",
    "department_phone": "010-11112222",
    "department_email": "<EMAIL>"
}

### 普通用户尝试更新部门信息（预期失败）
@normalToken = {{normalUserLogin.response.body.data.access_token}}
POST http://**************:9901/api/v1/department/update
Content-Type: application/json
Authorization: Bearer {{normalToken}}

{
    "department_id": 1,
    "department_name": "市场营销部",
    "department_phone": "010-22221111"
}

### 普通用户尝试删除部门（预期失败）
@normalToken = {{normalUserLogin.response.body.data.access_token}}
POST http://**************:9901/api/v1/department/del
Content-Type: application/json
Authorization: Bearer {{normalToken}}

{
    "department_id": 1
}
### 登录系统获取token
# @name login
POST http://172.17.110.105:9989/api/v1/auth/login
Content-Type: application/json

{
  "username": "test002",
  "password": "test002"
}

### 文本校验 - 正常文本
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9989/api/v1/text_check
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "text": "我们今天气很好，适合户外活动。"
}

### 文本校验 - 空文本
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9989/api/v1/text_check
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "text": ""
}


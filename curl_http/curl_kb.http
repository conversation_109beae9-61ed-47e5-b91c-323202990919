### 登录系统获取token
# @name login
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "test003",
  "password": "test003"
}

### 创建知识库
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/kb/kb_create
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "kb_name": "测试知识库0328",
    "kb_parse_strategy": "normal",
    "kb_tag": ["测试", "示例"],
    "kb_desc": "这是一个测试知识库",
    "dt_dept_code": "dt_dept_code"
}

### 获取知识库信息
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb/kb_info?kb_id=63
Authorization: Bearer {{token}}

### 获取知识库列表（默认分页）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb/kb_list?pageNo=1&pageSize=10&dt_dept_code=DEPT001
Authorization: Bearer {{token}}

### 获取知识库列表（指定分页）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb/kb_list?pageNo=2&pageSize=10&dt_dept_code=DEPT001
Authorization: Bearer {{token}}

### 编辑知识库
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/kb/kb_edit
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "kb_id": 47,
    "kb_name": "更新后的知识库0328",
    "kb_parse_strategy": "normal",
    "kb_auth_level": "private",
    "kb_tag": ["新标签1", "新标签2"],
    "kb_desc": "更新后的知识库描述"
}

### 删除知识库
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/kb/kb_del
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "kb_id": 46
}

### 批量分享知识库给其他用户
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/kb/share_to
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "kb_id": 63,
    "user_ids": [2, 3, 4, 16],
}

### 分享知识库给部门
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/kb/share_to_department
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "kb_id": 63,
    "to_department": "测试部"
}

### 获取被其他用户分享的知识库列表
### 获取知识库分享列表
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb/share_list?kb_id=63
Authorization: Bearer {{token}}

### 获取分享给我的知识库列表（分页）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb/shared_kb_list?pageNo=1&pageSize=10
Authorization: Bearer {{token}}

### 取消知识库分享
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/kb/share_del
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "kb_id": 63,
    "to_user_id": 16
}
@no-cookie-jar
### 登录系统获取admin用户token
# @name adminLogin
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

### 初始化系统知识库
@adminToken = {{adminLogin.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb_init_system
Authorization: Bearer {{adminToken}}

### 获取系统知识库信息
@admintoken = {{adminLogin.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/kb/kb_info?kb_id=33
Authorization: Bearer {{admintoken}}
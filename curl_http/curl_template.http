### 登录系统获取token
# @name login
POST http://172.17.110.105:9989/api/v1/auth/login
Content-Type: application/json

{
  "username": "test002",
  "password": "test002"
}

### 获取文章模板列表
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/generate/article_template
Authorization: Bearer {{token}}

### 获取特定类型的文章模板列表
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/generate/article_template?template_type=article
Authorization: Bearer {{token}}

### 创建新的文章模板
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "template_name": "测试模板005",
  "template_type": "article",
  "template_content": "# {title}\n\n## 摘要\n{summary}\n\n## 1. 引言\n{introduction}\n\n## 2. 主要内容\n{main_content}\n\n## 3. 结论\n{conclusion}",
  "permission_level": "employee"
}

### 更新现有文章模板
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "template_id": 2,
  "template_name": "更新后的测试模板",
  "template_type": "article",
  "template_content": "# {title}\n\n## 摘要\n{summary}\n\n## 1. 引言\n{introduction}\n\n## 2. 主要内容\n{main_content}\n\n## 3. 分析\n{analysis}\n\n## 4. 结论\n{conclusion}",
  "permission_level": "employee"
}

### 从文章中提取模板
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/extract_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "file_object": "generated_docs/文件系统概述.md",
  "template_type": "article",
  "template_name": "低空经济测试06",
  "permission_level": "employee"
}

### 删除文章模板
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9989/api/v1/generate/del_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "template_id": 12
}

### 使用模板生成文章（流式响应）
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9989/api/v1/generate/article/stream_with_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "doc_topic": "人工智能在医疗领域的应用",
  "template_id": "10",
  "keywords": ["深度学习", "医学影像", "辅助诊断", "智能医疗设备"],
  "length": 2000
}

### 使用模板生成文章（指定模板内容而非ID）
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/stream_with_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "doc_topic": "区块链技术在供应链管理中的应用",
  "template_content": "# {title}\n\n## 摘要\n{summary}\n\n## 1. 引言\n{introduction}\n\n## 2. 技术背景\n{background}\n\n## 3. 应用场景\n{applications}\n\n## 4. 挑战与机遇\n{challenges}\n\n## 5. 结论\n{conclusion}",
  "keywords": ["区块链", "供应链", "溯源", "智能合约"],
  "length": 2000
}

### 使用模板生成文章（参数错误 - 缺少模板ID和模板内容）
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/stream_with_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "doc_topic": "测试主题",
  "keywords": ["关键词1", "关键词2"],
  "length": 1000
}

### 使用模板生成文章（参数错误 - 缺少主题）
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/stream_with_template
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "template_id": 1,
  "keywords": ["关键词1", "关键词2"],
  "length": 1000
}
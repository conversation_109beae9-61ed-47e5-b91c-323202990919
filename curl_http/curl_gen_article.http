### 登录系统获取token
# @name login
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "test002",
  "password": "test002"
}

### 管理文章 - 更新文章内容
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/manage_article
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "article_id": 1,
  "content": "这是更新后的文章内容，包含了新的段落和修改。\n\n第一章：引言\n在这个快速发展的时代...\n\n第二章：主要内容\n我们需要关注的重点包括以下几个方面..."
}

### 删除文章
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/del_article
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "article_id": 22
}

### 测试错误情况 - 管理文章时缺少必要参数
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/manage_article
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "article_id": 1
}

### 测试错误情况 - 删除文章时缺少必要参数
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/del_article
Content-Type: application/json
Authorization: Bearer {{token}}

{
}

### 测试错误情况 - 尝试管理不存在的文章
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/manage_article
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "article_id": 99999,
  "content": "这是一个测试内容"
}

### 测试错误情况 - 尝试删除不存在的文章
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article/del_article
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "article_id": 99999
}

### 获取文章列表
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/generate/article/manage_article
Authorization: Bearer {{token}}

### 获取单个文章详情
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/generate/article/manage_article?article_id=22
Authorization: Bearer {{token}}
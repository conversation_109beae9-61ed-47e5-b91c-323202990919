### 获取支持的数据库类型
GET http://localhost:8000/api/v1/datasource/types
Authorization: Bearer your_jwt_token_here

### 创建数据源
POST http://localhost:8000/api/v1/datasource
Content-Type: application/json
Authorization: Bearer your_jwt_token_here

{
  "db_type": "mysql",
  "db_name": "test_database",
  "db_host": "localhost",
  "db_port": 3306,
  "db_user": "test_user",
  "db_pwd": "test_password",
  "comment": "测试数据源",
  "sys_code": "TEST_SYS",
  "ext_config": {
    "charset": "utf8mb4",
    "timeout": 30
  }
}

### 获取数据源列表
GET http://localhost:8000/api/v1/datasource?page=1&page_size=10
Authorization: Bearer your_jwt_token_here

### 获取数据源详情
GET http://localhost:8000/api/v1/datasource?id=1
Authorization: Bearer your_jwt_token_here

### 更新数据源
PUT http://localhost:8000/api/v1/datasource
Content-Type: application/json
Authorization: Bearer your_jwt_token_here

{
  "id": 1,
  "comment": "更新后的测试数据源",
  "db_port": 3307
}

### 测试数据库连接
POST http://localhost:8000/api/v1/datasource/test
Content-Type: application/json
Authorization: Bearer your_jwt_token_here

{
  "db_type": "mysql",
  "db_host": "localhost",
  "db_port": 3306,
  "db_user": "root",
  "db_pwd": "password",
  "db_name": "test"
}

### 删除数据源
DELETE http://localhost:8000/api/v1/datasource
Content-Type: application/json
Authorization: Bearer your_jwt_token_here

{
  "id": 1
}
### 登录系统获取token
# @name login
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

### 获取当前用户信息
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/user/update_info
Authorization: Bearer {{token}}

### 普通用户更新自己的基本信息
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/user/update_info
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "telphone": "13800138000",
    "id_num": "123456",
    "nickname": "测试用户昵称"
}

### 管理员更新其他用户信息（包括管理员专属字段）
### 专属字段包括: department, permission_level, group
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/user/update_info
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "user_id": 16,
    "department": "技术部",
    "permission_level": "guest",
    "group": "开发组",
    "telphone": "13900139000",
    "id_num": "654321",
    "nickname": "开发工程师"
}

### 普通用户尝试更新管理员专属字段（预期失败）
# @name normalUserLogin
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "test003",
  "password": "test003"
}

### 普通用户尝试更新管理员字段
@normalToken = {{normalUserLogin.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/user/update_info
Content-Type: application/json
Authorization: Bearer {{normalToken}}

{
    "department": "运营部",
    "permission_level": "admin",
    "group": "运营组"
}

### 普通用户尝试更新其他用户信息（预期失败）
@normalToken = {{normalUserLogin.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/user/update_info
Content-Type: application/json
Authorization: Bearer {{normalToken}}

{
    "user_id": 3,
    "telphone": "13700137000",
    "nickname": "其他用户"
}
### POST /chat-messages
POST https://ark.cn-beijing.volces.com/api/v3/chat/completions
Authorization: Bearer 5ce231eb-564a-4748-b93c-fa3c41b1bb55
Content-Type: application/json

{
    "inputs": {},
    "query": "What are the specs of the iPhone 13 Pro Max?",
    "response_mode": "streaming",
    "conversation_id": "",
    "user": "abc-123",
    "files": [
        {
            "type": "image",
            "transfer_method": "remote_url",
            "url": "https://cloud.dify.ai/logo/logo-site.png"
        }
    ]
}


### POST /chat-messages
POST https://ark.cn-beijing.volces.com/api/v3/chat/completions
Authorization: Bearer 5ce231eb-564a-4748-b93c-fa3c41b1bb55
Content-Type: application/json

{
    "model": "deepseek-v3-241226",
    "messages": [
        {
            "role": "system",
            "content": "你的名字是张三，你是一个AI机器人."
        },
        {
            "role": "user",
            "content": "你叫啥？"
        }
    ],
    "temperature": 0.2
}


### POST /chat-messages
POST https://ark.cn-beijing.volces.com/api/v3/chat/completions
Authorization: Bearer 5ce231eb-564a-4748-b93c-fa3c41b1bb55
Content-Type: application/json

curl https://ark.cn-beijing.volces.com/api/v3/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 0db488f0-0a43-4d63-b37b-1bbb5aa65df8" \
  -d '{
    "model": "deepseek-v3-241226",
    "messages": [
      {"role": "system","content": "你是人工智能助手."},
      {"role": "user","content": "常见的十字花科植物有哪些？"}
    ]
  }'

### 登录系统获取token
# @name login
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "test002",
  "password": "test002"
}

### 生成大纲 - 低空经济与新质生产力
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/outline
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "doc_topic": "低空经济发展对新质生成力具有极大促进",
  "keywords": ["低空经济", "新质生产力"]
}


### 根据大纲生成文章 - 低空经济与新质生产力
@token = {{login.response.body.data.access_token}}
POST http://172.17.110.105:9901/api/v1/generate/article
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "doc_topic": "低空经济发展对新质生成力具有极大促进",
  "outline": {"primary": [{"title": "低空经济的概念与发展现状", "secondary": [{"title": "低空经济的定义与特征", "tertiary": [{"title": "低空经济的基本概念与核心要素"}, {"title": "低空经济与传统经济的区别与联系"}]}, {"title": "全球低空经济的发展现状与趋势", "tertiary": [{"title": "主要国家低空经济的发展模式与经验"}, {"title": "低空经济技术的创新与应用"}]}]}, {"title": "低空经济对新质生产力的促进作用", "secondary": [{"title": "新质生产力的内涵与低空经济的契合点", "tertiary": [{"title": "新质生产力的核心特征与低空经济的关联"}, {"title": "低空经济如何推动新质生产力的形成"}]}, {"title": "低空经济在产业升级中的具体作用", "tertiary": [{"title": "低空经济在智能制造中的应用与影响"}, {"title": "低空经济对物流与供应链的优化作用"}]}]}, {"title": "低空经济发展的挑战与未来展望", "secondary": [{"title": "低空经济发展面临的主要挑战", "tertiary": [{"title": "政策与法规的制约因素"}, {"title": "技术与安全问题的解决方案"}]}, {"title": "低空经济的未来发展方向与机遇", "tertiary": [{"title": "低空经济在绿色经济中的潜力"}, {"title": "低空经济与新质生产力的协同发展路径"}]}]}]},
  "length": 2000,
  "article_id": 2
}


### 创建知识库
POST http://172.17.110.105:9901/api/v1/kb/create
Content-Type: application/json

{
  "user_id": 1001,
  "kb_name": "测试知识库",
  "kb_parse_strategy": "normal",
  "kb_auth_level": "private",
  "kb_tag": "测试,知识库,API",
  "kb_desc": "这是一个用于测试的知识库"
}

### 获取知识库信息
GET http://172.17.110.105:9901/api/v1/kb/info?user_id=1001&kb_id=kb_001

### 获取知识库列表
GET http://172.17.110.105:9901/api/v1/kb/list?user_id=1001

### 编辑知识库
POST http://172.17.110.105:9901/api/v1/kb/edit
Content-Type: application/json

{
  "user_id": 1001,
  "kb_id": "kb_001",
  "kb_name": "更新后的知识库",
  "kb_parse_strategy": "normal",
  "kb_auth_level": "private",
  "kb_tag": "更新,测试,知识库",
  "kb_desc": "这是一个更新后的知识库描述"
}

### 知识库文档添加
POST http://172.17.110.105:9901/api/v1/kb/kb_add_doc
Content-Type: application/json

{
  "user_id": 1001,
  "kb_id": "kb_001",
  "file_path": "rag_demo_path",
  "file_name": "test.pdf"
}

### 知识库文档解析
POST http://172.17.110.105:9901/api/v1/kb/async_doc_parse
Content-Type: application/json

{
  "user_id": 1001,
  "kb_id": "kb_001",
  "file_path": "/path/to/document.pdf",
  "file_name": "测试文档.pdf",
  "parse_strategy": "normal"
}

### 获取知识库文档解析状态
GET http://172.17.110.105:9901/api/v1/kb/doc_parse_status?user_id=1001&kb_id=kb_001&doc_id=doc_001

### 获取知识库文档列表
GET http://172.17.110.105:9901/api/v1/kb/kb_doc_list?user_id=1001&kb_id=kb_001

### 删除知识库文档
POST http://172.17.110.105:9901/api/v1/kb/doc_del
Content-Type: application/json

{
  "user_id": 1001,
  "kb_id": "kb_001",
  "doc_id": "doc_001"
}

### 文档检索
GET http://172.17.110.105:9901/api/v1/kb/doc_retrieval?user_id=1001&doc_name=测试文档.pdf&doc_keyword=关键词

### 文档预览
GET http://172.17.110.105:9901/api/v1/kb/doc_preview?user_id=1001&doc_id=doc_001

### 获取用户文档列表
GET http://172.17.110.105:9901/api/v1/kb/user_doc_list?user_id=1001

### 登录系统获取token
# @name login
POST http://172.17.110.105:9901/api/v1/auth/login
Content-Type: application/json

{
  "username": "test003",
  "password": "test003"
}

### 获取问答统计信息（不带时间范围）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/qa
Authorization: Bearer {{token}}

### 获取问答统计信息（指定时间范围）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/qa?start_tm=2025-04-01%2000:00:00&end_tm=2025-04-20%2023:59:59
Authorization: Bearer {{token}}

### 获取Tokens统计信息（不带时间范围）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/tokens
Authorization: Bearer {{token}}

### 获取Tokens统计信息（指定时间范围）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/tokens?start_tm=2025-04-01%2000:00:00&end_tm=2025-04-20%2023:59:59
Authorization: Bearer {{token}}

### 获取知识库统计信息（不带时间范围）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/kbs
Authorization: Bearer {{token}}

### 获取知识库统计信息（指定时间范围）
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/kbs?start_tm=2025-04-01%2000:00:00&end_tm=2025-04-20%2023:59:59
Authorization: Bearer {{token}}

### 获取模型统计信息
@token = {{login.response.body.data.access_token}}
GET http://172.17.110.105:9901/api/v1/stats/models
Authorization: Bearer {{token}}
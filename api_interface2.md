```markdown
# 智能文档问答系统 API 协议

## 基础规范
1. **协议类型**: HTTPS
2. **数据格式**: JSON
3. **鉴权方式**: JWT <PERSON> (Authorization Header)
4. **版本前缀**: /api/v1
5. **响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

---

## 接口列表

### 0. 认证模块
#### 0.1 用户注册
```http
POST /api/v1/auth/register
```
**请求参数**:
```json
{
  "username": "testuser",
  "password": "Test@123"
}
```
**成功响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```
**错误码说明**:
- 400: 请求参数缺失或格式错误
- 400: 用户名已存在
- 500: 服务端内部错误

#### 0.2 用户登录
```http
POST /api/v1/auth/login
```
**请求参数**:
```json
{
  "username": "testuser",
  "password": "Test@123"
}
```
**成功响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "access_token": "eyJhbGciOi...",
    "token_type": "bearer"
  }
}
```
**错误码说明**:
- 400: 请求参数缺失或格式错误
- 401: 用户名或密码错误
- 500: 服务端内部错误

### 1. 问答服务
#### 1.1 生成问答
```http
POST /api/v1/chat/completion
```
**请求参数**:
```json
{
  "question": "合同有效期是多久？",
  "dialog_id": 1,   //  可选. 如果没有传，则新建对话
  "enable_rag": true,  //  可选. 是否启用 RAG 能力，不认不启用
  "file_objects": ["test/docs/DeepSeek_R1.pdf"], // 可选，临时上传文档的minio路径
}
```
**响应参数**:
```json
{
  "answer": "合同有效期为三年...",
  "dialog_id": 1,
  "references": [
    {
        "doc_id": 8,
        "doc_title": "DeepSeek_R1.pdf",
        "abstract": "",
        "path": "test/docs/DeepSeek_R1.pdf",
        "version": "1.0",
        "url": "http://172.17.110.105:9000/rag-demo/rag_docs/DeepSeek_R1.pdf/1.0/DeepSeek_R1.pdf",   // 下载原始文档的连接
        "preview_url": "http://172.17.110.105:9000/rag-demo/rag_docs/DeepSeek_R1.pdf/1.0/DeepSeek_R1.pdf"   // 文档的预览连接
    }
  ]
}
```

#### 1.2 流式生成问答
```http
POST /api/v1/chat/completion/stream
```
**请求参数**:
```json
{
  "question": "合同有效期是多久？",
  "dialog_id": 1,   //  可选. 如果没有传，则新建对话
  "enable_rag": true,  //  可选. 是否启用 RAG 能力，不认不启用
  "file_objects": ["test/docs/DeepSeek_R1.pdf"], // 可选，临时上传文档的minio路径
}
```
**响应参数**:
```json
data: {"content": "\u3002"}
data: {"content": "\u3002"}
......
data: {"content": "\u3002"}
final: {
  "code": 200,
  "message": "success",
  "data": {
    "answer": "合同有效期为三年...",
    "dialog_id": 1,
    "references": [
      {
          "doc_id": 8,
          "doc_title": "DeepSeek_R1.pdf",
          "abstract": "",
          "path": "test/docs/DeepSeek_R1.pdf",
          "version": "1.0",
          "url": "final: {
  "code": 200,
  "message": "success",
  "data": {
    "answer": "合同有效期为三年...",
    "dialog_id": 1,
    "references": [
      {
          "doc_id": 8,
          "doc_title": "DeepSeek_R1.pdf",
          "abstract": "",
          "path": "test/docs/DeepSeek_R1.pdf",
          "version": "1.0",

```

---

### 2. 文档管理
#### 2.1 文档导入
```http
POST /api/v1/docs/upload
```
**请求参数**:
```json
{
  "docments": [
  {
      "path" : "a/b",
	  "name": "c.pdf",
	  "permission_level": "admin",
	  "version": 20250120
  },
  {
      "path" : "a/c",
	  "name": "d.pdf",
	  "permission_level": "admin",
	  "version": 20250121
  }
}
```
**响应参数**:
```json
{
	"docment_ids": ["i1", "i2"]
}
```

#### 2.2 文档列表
```http
GET /api/v1/docs?page=1&size=20
```
**响应**:
```json
{
  "documents": [
    {
      "doc_id": "doc_001",
      "name": "用户协议.pdf",
      "version": 20250121,
	  "download_url": "http://****"
      "upload_time": "2023-08-20T10:00:00Z"
    }
  ],
  "total": 100
}
```

#### 2.3 获取预签名上传URL
```http
GET /api/v1/docs/upload_url?file_name=test.pdf
```
**请求参数**:
- file_name: string (必须) 要上传的文件名称

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "upload_url": "https://minio.example.com/bucket?...",   // 预签名链接，可通过PUT上传本地文件至这个连接
    "object_path": "tmp_docs/{userid}/uuid-filename"        // 上传后的对象存储路径
  }
}
```

**错误状态码**:
- 400: 缺少必要参数file_name
- 500: 服务器内部错误生成预签名URL

---

### 3. 对话管理
#### 3.1 创建对话
```http
POST /api/v1/dialog
```
**请求参数**:
```json
{
  "title": "合同条款咨询"
}
```

#### 3.2 对话列表
```http
GET /api/v1/dialogs
```
**响应**:
```json
{
  "dialogs": [
    {
      "title": "合同咨询",
      "dialog_id": 123,
      "create_time": "2023-08-20T10:10:00Z"
    }
  ],
  "total": 100
}
```

#### 3.3 删除对话
```http
DELTE /api/v1/dialog?dialog_id=1
```

#### 3.4 更改对话名
```http
PUT /api/v1/dialog?dialog_id=1
```
**请求参数**:
```json
{
  "title": "合同条款咨询"
}
```


#### 3.5 对话详情
```http
GET /api/v1/dialog?dialog_id=1
```
**响应**:
```json
{
  "dialog_id": 123,
  "title": "合同咨询",    
  "messages": [
    {
      "role": "user",
      "content": "合同有效期是多久？",
      "time": "2023-08-20T10:10:00Z"
    },
    {
      "role": "assistant",
      "content": "合同有效期为三年...",
      "references": [
			{
			  "doc_id": "doc_001",
			  "version": "v2.0",
			  "doc_path": "/a/b/c"
			}
	  ]
    }
  ]
}
```

---

### 4. 文档生成类接口

#### 4.1 生成大纲
```http
POST /api/v1/generate/outline
```
**请求参数**:
```json
{
  "doc_topic": "劳动合同条款",
  "keywords": ["有效期", "违约责任", "解除条件"]
}
```
**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "outline": {
      "sections": [
        {
          "title": "合同有效期",
          "subpoints": ["期限约定", "自动续约条款"]
        }
      ]
    }
  }
}
```
**错误码**:
- 400: 参数错误/无效的JSON数据
- 500: 服务器错误/LLM请求失败

#### 4.2 生成完整文章
```http
POST /api/v1/generate/article
```
**请求参数**:
```json
{
  "doc_topic": "劳动合同条款",
  "outline": "...",
  "length": 2000
}
```
**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "article": "完整的合同条款文本内容..."
  }
}
```

#### 4.3 流式生成文章（SSE）
```http
POST /api/v1/generate/article-stream
```
**特殊说明**:
1. 响应头包含 `Content-Type: text/event-stream`
2. 数据格式遵循 Server-Sent Events 规范

**流式响应示例**:
```
data: {"content": "第一段生成的文本..."}

data: {"content": "第二段生成的文本..."}

final: {
  "code": 200,
  "message": "success",
  "data": {
    "article": "完整文章内容",
    "storage": {
      "minio_path": "documents/劳动合同条款.pdf",
      "mysql_id": 12345
    }
  }
}
```
**错误处理**:
```
data: {"error": "错误描述信息"}
```

#### 4.4 文档总结
```http
POST /api/v1/summary
```

**请求参数**:
```json
{
  "file_objects": ["path1", "path2"]
}
```
**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "summary": "文档的总体概述和核心观点...",
    "keywords": ["关键词1", "关键词2", "关键词3", ...],
    "keysentences": ["重要句子1", "重要句子2", "重要句子3", ...],
    "keydata": {
      "数据项1": "值1",
      "数据项2": "值2"
    }
  }
}
```


### 5. 常用语管理
#### 5.1 添加常用语
```http
POST /api/v1/phrase
```
**请求参数**:
```json
{
  "content": "请说明具体的条款编号"
}
```
#### 5.2 删除常用语
```http
DELETE /api/v1/phrase?phrase_id=1
```

#### 5.3 常用语列表
```http
GET /api/v1/phrases
```
**响应**:
```json
{
  "phrases": [
    {
      "phrase_id": "p_001",
      "content": "请提供相关法律依据",
      "create_time": "2023-08-20T10:15:00Z"
    }
  ]
}
```

---

## 状态码说明
| 状态码 | 说明                |
|--------|---------------------|
| 200    | 请求成功            |
| 400    | 参数错误            | 
| 401    | 未授权访问          |
| 403    | 权限不足            |
| 404    | 资源不存在          |
| 500    | 服务器内部错误      |

---


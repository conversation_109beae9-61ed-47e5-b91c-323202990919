#!/bin/bash
cd /home/<USER>/jian<PERSON><PERSON>/rag_demo
git pull

# 查找并杀掉已存在的 python3 main.py 进程
PID=$(ps -ef | grep "python3 main.py" | grep -v "grep" | awk '{print $2}')

if [ -n "$PID" ]; then
  echo "发现正在运行的进程 PID: $PID，正在终止..."
  kill -9 $PID
else
  echo "未发现正在运行的 python3 main.py 进程。"
fi

eval "$(conda shell.bash hook)"
conda activate rag

# 启动新进程，并将日志输出重定向到 nohup.out
cd app
nohup python3 main.py > nohup.out 2>&1 &
echo "已启动 python3 main.py，日志输出到 app/nohup.out"
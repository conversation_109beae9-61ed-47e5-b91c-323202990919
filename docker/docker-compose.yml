services:
  ragserver:
    # depends_on:
    #   ragserver-mysql:
    #     condition: service_healthy
    image: rag_server:v0.1.6
    container_name: ragserver
    ports:
      - ${SVR_HTTP_PORT}:80
      - 7081:7081
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./service_conf.yaml:/rag_server/app/conf/service_conf.yaml
      - ./magic-pdf.json:/rag_server/app/conf/magic-pdf.json
      - ./start.sh:/rag_server/start.sh
    env_file: .env
    environment:
      - TZ=${TIMEZONE}
    deploy:
      resources:
        limits:
          cpus: "64"
          memory: 51200M
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - ragserver
    #restart: on-failure


  # ragserver-es:
  #   container_name: ragserver-es
  #   image: docker.elastic.co/elasticsearch/elasticsearch:8.11.3
  #   volumes:
  #     - rag_es_data:/usr/share/elasticsearch/data
  #   ports:
  #     - ${ES_PORT}:9200
  #   env_file: .env
  #   environment:
  #     - node.name=ragserver-es01
  #     - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
  #     - bootstrap.memory_lock=false
  #     - discovery.type=single-node
  #     - xpack.security.enabled=true
  #     - xpack.security.http.ssl.enabled=false
  #     - xpack.security.transport.ssl.enabled=false
  #     - cluster.routing.allocation.disk.watermark.low=5gb
  #     - cluster.routing.allocation.disk.watermark.high=3gb
  #     - cluster.routing.allocation.disk.watermark.flood_stage=2gb
  #     - TZ=${TIMEZONE}
  #   mem_limit: ${MEM_LIMIT}
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #   healthcheck:
  #     test: ["CMD-SHELL", "curl http://localhost:9200"]
  #     interval: 10s
  #     timeout: 10s
  #     retries: 120
  #   networks:
  #     - ragserver
  #   restart: on-failure

  # ragserver-mysql:
  #   # mysql:5.7 linux/arm64 image is unavailable.
  #   image: mysql:8.0.39
  #   container_name: ragserver-mysql
  #   env_file: .env
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
  #     - TZ=${TIMEZONE}
  #   command:
  #     --max_connections=1000
  #     --character-set-server=utf8mb4
  #     --collation-server=utf8mb4_unicode_ci
  #     --default-authentication-plugin=mysql_native_password
  #     --tls_version="TLSv1.2,TLSv1.3"
  #     --init-file /data/application/init.sql
  #   ports:
  #     - ${MYSQL_PORT}:3306
  #   volumes:
  #     - rag_mysql_data:/var/lib/mysql
  #     - ./init.sql:/data/application/init.sql
  #   networks:
  #     - ragserver
  #   healthcheck:
  #     test: ["CMD", "mysqladmin" ,"ping", "-uroot", "-p${MYSQL_PASSWORD}"]
  #     interval: 10s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  #   restart: on-failure

  # ragserver-minio:
  #   image: quay.io/minio/minio:RELEASE.2023-12-20T01-00-02Z
  #   container_name: ragserver-minio
  #   command: server --console-address ":9001" /data
  #   ports:
  #     - ${MINIO_PORT}:9000
  #     - ${MINIO_CONSOLE_PORT}:9001
  #   env_file: .env
  #   environment:
  #     - MINIO_ROOT_USER=${MINIO_USER}
  #     - MINIO_ROOT_PASSWORD=${MINIO_PASSWORD}
  #     - TZ=${TIMEZONE}
  #   volumes:
  #     - rag_minio_data:/data
  #   networks:
  #     - ragserver
  #   restart: on-failure


volumes:
  rag_es_data:
    driver: local
#   rag_mysql_data:
#     driver: local
#   rag_minio_data:
#     driver: local

networks:
  ragserver:
    driver: bridge

import tornado.ioloop
import tornado.web

class MainHandler(tornado.web.RequestHandler):
    def get(self):
        """处理GET请求"""
        self.write("Hello, World! This is a GET request.")

    def put(self):
        """处理POST请求"""
        self.write("Hello, World! This is a PUT request.")


def make_app():
    return tornado.web.Application([
        (r"/", MainHandler),              # 根路径
    ])

if __name__ == "__main__":
    app = make_app()
    app.listen(8887)  # 监听8888端口
    print("Server started on port 8888")
    tornado.ioloop.IOLoop.current().start()